---
description: 
globs: 
alwaysApply: true
---

# 项目说明
1、项目名称：OA协同办公系统
2、项目技术：PHP+Mysql
3、后台结构
后台管理系统，系统采用单页面操作方式，全部文件都在webmain下。
基本结构
login 登录页面文件夹
index 主页文件夹
home 桌面主页文件夹
一、系统登录到首页说明。
1、登录页面，在webmain/login下，浏览器显示显示地址：http://127.0.0.1/?m=login，也就是调用控制器webmain/login/loginAction.php下的defaultAction方法，对应模版文件webmain/login/tpl_login.html，在模版文件引入了js：webmain/login/loginscript.js。这样登录页面就显示出来了。
2、输入用户名密码，点击登录调用loginscript.js的方法loginsubmit()，用ajax验证登录，loginAction.php控制器下的checkAjax方法。
3、登录成功跳转到地址：http://127.0.0.1/?m=index。在webmain/index下调用对应控制器和模版文件，渲染出来首页。
4、首页主页js文件：webmain/index/indexscript.js，初始页面initbody()，运行下来，clickhome()用了addtabs方法，加载第一个【首页】选项卡。
5、选择卡加载方式可具体看webmain/index/indexscript.js文件下的addtabs()方法。
6、首页选择卡加载，从clickhome()可以看到url地址是home,index，地址格式说明往下看，也就是文件webmain/home/<USER>/rock_index.php。
二、左边栏目菜单管理，可到系统【系统→菜单管理】下管理。菜单URL地址说明，也可以用绝对地址http://地址/。
1、如：system,menu 对应文件，webmain/system/menu/rock_menu.php。
2、如：main,xinhu,cog 对应文件，webmain/main/xinhu/rock_xinhu_cog.php。
3、如：地址有带=就是参数，在对应文件有params，来获取。
如一个地址：main,kaoqin,dkjl,atype=all 对应文件： webmain/main/kaoqin/rock_kaoqin_dkjl.php，在页面上有params来获取atype参数，var atype = params.atype;
4、如：flow,page,project,atype=all,pnum=all对应文件：webmain/flow/page/rock_page_project.php，这个模块project是自动生成的列表页，atype=流程模块条件下的编号，punu=流程模块条件下的分组编号，流程模块条件在【流程模块→流程模块条件】。
三、【说明】在菜单管理很多页面上都可以看到有带atype的参数说明。
atype一般是列表展示时候显示的数据条件，可以自定义，在对应的shtml上有个modenum模块编号，条件可在对应模块的接口文件上添加。
如地址：main,daily,list,atype=my，文件webmain/main/daily/rock_daily_list.php，
可以在对应模块接口文件下设置列表展示条件，文件：webmain/model/flow/dailyModel.php下的方法 flowbillwhere($uid,$lx)，其中$lx就是对应atype的值，
一、基本文件结构说明
config 基础配置
include 核心文件
├ chajian 插件库
├ class 基础使用(mysql)
mode 静态素材框架引用等
web 桌面版源码
webmain 系统代码文件
├flow 流程文件夹
├├input 流程模块录入页面控制器和接口
├├page PC录入页和详情展示页的模版
├├inputAction.php 流程主控制器文件
├├├inputjs 流程模块js文件目录
├model m模型数据文件
├├flow 流程模块各个接口文件
├├agent 应用数据接口文件
├system 后台系统基本文件
├we 移动端目录，详见介绍
├task
├├api 系统api目录，如桌面版，app等的api
├├mode 这个是系统上单据详情展示
├├openapi 对外接口目录
├├runt 计划任务文件
index.php 入口文件
二、一个网址运行周期从index.php入口开始。
index.php引入两个文件。
1、include_once('config/config.php');
在config.php又引入了框架文件：include/rockFun.php(常用方法m,c等)，include/Chajian.php(插件)，include/class/rockClass.php，webmain/webmainConfig.php(用户配置文件)
2、include_once('include/View.php');
3、手机版本在系统上的目录
webmain/we 这个目录
webmain/we/login/tpl_login.html 登录页面
webmain/we/login/loginAction.php 登录页面控制器
webmain/we/index/tpl_index.html 手机版本主页面
webmain/we/index/indexAction.php 手机版本主页面控制器
4、手机版主页面中的“应用”可到【系统→即时通信管理→应用管理】下管理，详情开发。
应用的的目录文件
webmain/we/ying/tpl_ying.html 应用文件
webmain/we/ying/yingAction.php 应用控制器
webmain/we/ying/ying.js 应用主js文件
webmain/we/ying/tpl_ying_daka.html 考勤打卡文件
1、PC展示
1.1、网址：http://url/task.php?a=p&num=模块编号&mid=单据id
1.2、控制器文件：webmain/task/mode/modeAction.php 下的 pAction方法。
1.3、默认模版：webmain/task/mode/tpl_mode_p.html
1.4、js文件：webmain/task/mode/modeview.js
1.5、模块模版文件：webmain/flow/page/view_模块编号_0.html
2、手机展示
2.1、网址：http://url/task.php?a=x&num=模块编号&mid=单据id
2.2、控制器文件：webmain/task/mode/modeAction.php 下的 xAction方法。
2.3、默认模版：webmain/task/mode/tpl_mode_x.html
2.4、js文件：webmain/task/mode/modeview.js
2.5、模块模版文件：webmain/flow/page/view_模块编号_1.html
3、默认模版文件位置pAction和xAction方法的调用到m('flow')->getdatalog()，调用流程核心文件：webmain/model/flow/flow.php下的getdatalog()。可以查看到是通过如下创建默认展示。
其中c('html')调用插件文件：include/chajian/htmlChajian.php 下的createtable方法。
4、如详情有子表显示，子表显示格式是固定的，代码在flow.php下的getsubdata方法读取创建的，
include/chajian/htmlChajian.php 下的createrows方法
终端及测试信息：
本地测试域名：https://cr.hc797.com
根据目录：D:\phpstudy_pro\WWW\hxoas\
PHP执行目录：D:\phpstudy_pro\Extensions\php\php7.3.4nts
数据库备份：D:\phpstudy_pro\WWW\hxoas\upload\data\hxkj202506_2025-06-09_16-58-26_mysql_data_MlvoO.sql