# 客户管理移动端标签页功能

## 📱 功能概述

这是一个专为移动端优化的客户管理标签页系统，提供了丰富的客户信息展示和交互功能。

## 🚀 主要特性

- **响应式设计**：自动适配PC端和移动端
- **配置驱动**：通过配置文件轻松管理标签页
- **数据集成**：无缝集成现有的客户模块数据
- **丰富内容**：支持静态HTML和动态AJAX内容
- **美观界面**：现代化的移动端UI设计

## 📋 标签页类型

### 1. 基本信息
- **类型**：静态HTML
- **内容**：客户基本资料、联系信息、业务信息
- **特点**：即时显示，无需加载

### 2. 联系记录
- **类型**：动态AJAX
- **内容**：客户沟通记录、联系历史
- **数据源**：客户模块索引8

### 3. 销售机会
- **类型**：动态AJAX
- **内容**：销售机会列表、进度跟踪
- **数据源**：客户模块索引1

### 4. 合同信息
- **类型**：动态AJAX
- **内容**：合同列表、执行状态
- **数据源**：客户模块索引2

### 5. 服务记录
- **类型**：动态AJAX
- **内容**：售后服务记录
- **数据源**：客户模块索引5

### 6. 跟进计划
- **类型**：动态AJAX
- **内容**：客户跟进计划
- **数据源**：客户模块索引7

### 7. 收款记录
- **类型**：动态AJAX
- **内容**：收款历史记录
- **数据源**：客户模块索引3

### 8. 销售统计
- **类型**：动态生成
- **内容**：合同统计、回款进度
- **特点**：实时计算统计数据

### 9. 客户动态
- **类型**：动态AJAX
- **内容**：客户活动时间线
- **数据源**：客户模块索引9

## 🛠️ 文件结构

```
webmain/
├── config/
│   └── mobileTabsConfig.php          # 标签页配置文件
├── model/
│   └── mobileTabConfigModel.php      # 配置模型（可选）
├── we/component/
│   ├── componentAction.php           # API控制器
│   └── mobileTabs.js                 # 前端组件
├── css/
│   └── rui.css                       # 样式文件
└── flow/page/
    └── view_customer_1.html           # 移动端客户详情页面

根目录/
├── check_config.php                  # 配置检查API
├── test_tabs.html                    # 完整测试页面
├── integration_guide.md              # 集成指南
└── README.md                         # 使用说明
```

## 🎯 快速开始

### 1. 测试功能
```
访问：http://您的域名/test_tabs.html
```

### 2. 检查配置
```
访问：http://您的域名/check_config.php
```

### 3. 集成到现有系统
参考 `integration_guide.md` 文档

## 🔧 API接口

### 获取标签页配置
```
GET index.php?d=we&m=component&a=getMobileTabs&category_code=customer
```

### 获取客户数据
```
GET index.php?d=we&m=component&a=getCustomerTabs&customer_id=123&tab_type=contact_record
```

## 📱 移动端集成示例

```javascript
// 初始化客户标签页
function initCustomerTabs(customerId) {
    $.ajax({
        url: 'index.php?d=we&m=component&a=getMobileTabs&category_code=customer',
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                createMobileCustomerTabs(response.data, customerId);
            }
        }
    });
}
```

## 🎨 自定义配置

### 添加新标签页
在 `webmain/config/mobileTabsConfig.php` 中添加：

```php
[
    'tab_name' => '新标签页',
    'tab_code' => 'new_tab',
    'tab_icon' => 'fa fa-star',
    'content_type' => 'ajax',
    'content_source' => 'index.php?d=we&m=component&a=getCustomerTabs&tab_type=new_tab',
    'load_method' => 'lazy',
    'sort' => 10,
    'status' => 1,
    'is_default' => false,
    'permissions' => ''
]
```

### 修改样式
编辑 `webmain/css/rui.css` 中的相关样式类。

## 🔍 故障排除

### 问题1：标签页不显示
- 检查配置文件是否存在
- 确认JavaScript文件是否正确加载
- 查看浏览器控制台错误

### 问题2：AJAX请求失败
- 检查API控制器文件是否存在
- 确认客户控制器文件是否存在
- 验证URL路径是否正确

### 问题3：数据不显示
- 确认数据库中有客户数据
- 检查客户ID参数是否正确传递
- 验证getothernrAjax方法是否正常

## 📞 技术支持

如需技术支持或功能定制，请联系开发团队。

## 📝 更新日志

### v1.0.0 (2025-01-03)
- 初始版本发布
- 支持9种标签页类型
- 完整的移动端适配
- 配置文件驱动的架构
