<?php
/**
 * 检查标签页配置
 * 创建时间：2025-01-03
 * 用途：最简单的配置检查，不依赖任何系统文件
 */

header('Content-Type: application/json; charset=utf-8');

$result = [
    'success' => false,
    'message' => '',
    'data' => [],
    'debug' => []
];

try {
    // 检查配置文件
    $configFile = 'webmain/config/mobileTabsConfig.php';
    
    if (!file_exists($configFile)) {
        $result['message'] = '配置文件不存在: ' . $configFile;
        $result['debug'][] = '请运行 install_mobile_tabs_config.php 安装';
        echo json_encode($result, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 定义必要的常量
    if (!defined('HOST')) {
        define('HOST', true);
    }
    
    // 加载配置
    $config = include $configFile;
    
    if (!is_array($config)) {
        $result['message'] = '配置文件格式错误';
        echo json_encode($result, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 检查客户标签页配置
    if (!isset($config['tabs']['customer']) || !is_array($config['tabs']['customer'])) {
        $result['message'] = '客户标签页配置不存在';
        echo json_encode($result, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    $customerTabs = $config['tabs']['customer'];
    
    // 过滤启用的标签页
    $activeTabs = [];
    foreach ($customerTabs as $tab) {
        if (isset($tab['status']) && $tab['status'] == 1) {
            $activeTabs[] = [
                'id' => count($activeTabs),
                'tab_name' => $tab['tab_name'],
                'tab_code' => $tab['tab_code'],
                'tab_icon' => $tab['tab_icon'] ?? '',
                'content_type' => $tab['content_type'],
                'content_source' => $tab['content_source'],
                'load_method' => $tab['load_method'],
                'sort' => $tab['sort'],
                'status' => $tab['status'],
                'is_default' => $tab['is_default'],
                'permissions' => $tab['permissions'] ?? '',
                'category_code' => 'customer',
                'category_name' => '客户详情'
            ];
        }
    }
    
    $result['success'] = true;
    $result['message'] = '获取成功';
    $result['data'] = $activeTabs;
    $result['debug'] = [
        'config_file' => $configFile,
        'total_tabs' => count($customerTabs),
        'active_tabs' => count($activeTabs),
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
} catch (Exception $e) {
    $result['message'] = '配置检查失败: ' . $e->getMessage();
    $result['debug'][] = $e->getTraceAsString();
}

echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
?>
