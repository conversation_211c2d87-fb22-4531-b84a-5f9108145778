.r-tabs{
    background: linear-gradient(to bottom, #ffffff 0%, #f8f8f8 100%);
    overflow:hidden;
    height:40px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-bottom: 1px #ddd solid;
}

.r-tabs .r-tabs-item{
    height:36px;
    overflow:hidden;
    line-height:36px;
    display: block;
    float:left;
    text-align:center;
    padding:0px 15px;
    border:1px #e0e0e0 solid;
    border-bottom:2px solid transparent;
    border-right-width:0px;
    cursor:pointer;
    color:#666;
    position:relative;
    transition: all 0.3s ease;
    background: linear-gradient(to bottom, #fafafa 0%, #f0f0f0 100%);
    margin: 2px 1px 0 1px;
    border-radius: 6px 6px 0 0;
}

.r-tabs .r-tabs-item:first-child{
    border-top-left-radius:6px;
}

.r-tabs .r-tabs-item:last-child{
    border-top-right-radius:6px;
    border-right-width:1px;
}

.r-tabs .r-tabs-item:not(:first-child):not(:last-child){
    border-radius:6px 6px 0 0;
}

.r-tabs .r-tabs-item:hover {
    background: linear-gradient(to bottom, #f0f8ff 0%, #e6f3ff 100%);
    color: #4a90e2;
    transform: translateY(-1px);
    border-color: #d0e7ff;
}

.r-tabs .r-tabs-item.active{
    color:var(--main-color);
    background: linear-gradient(to bottom, #ffffff 0%, #f5f9ff 100%);
    border-bottom-color: var(--main-color);
    font-weight: bold;
    box-shadow: 0 -2px 8px rgba(19, 137, 211, 0.2);
    position: relative;
    z-index: 2;
    height: 38px;
    line-height: 38px;
    margin-top: 0;
}

.r-tabs .r-tabs-item.active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(to right, 
        transparent 0%, 
        var(--main-color) 20%, 
        var(--main-color) 80%, 
        transparent 100%);
    border-radius: 2px;
}

.r-tabs .r-tabs-item:active {
    background: linear-gradient(to bottom, #e6f3ff 0%, #cce7ff 100%);
    transform: translateY(1px);
} 