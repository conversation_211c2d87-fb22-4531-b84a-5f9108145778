<?php
/**
 * 移动端标签页功能调试工具
 * 创建时间：2025-01-03
 * 用途：诊断标签页功能问题
 */

// 检查是否在正确的目录
if (!file_exists('config/config.php')) {
    die('请将此文件放在网站根目录下运行');
}

require_once('config/config.php');

class MobileTabsDebugger
{
    private $results = [];
    
    public function debug()
    {
        echo "<h2>移动端标签页功能调试工具</h2>\n";
        echo "<div style='font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px;'>\n";
        
        $this->checkFiles();
        $this->checkConfig();
        $this->testAPI();
        $this->checkCustomerData();
        
        $this->printSummary();
        echo "</div>\n";
    }
    
    /**
     * 检查文件
     */
    private function checkFiles()
    {
        echo "<h3>📁 文件检查</h3>\n";
        
        $files = [
            'webmain/config/mobileTabsConfig.php' => '配置文件',
            'webmain/model/mobileTabConfigModel.php' => '配置模型',
            'webmain/we/component/componentAction.php' => 'API控制器',
            'webmain/we/component/mobileTabs.js' => '前端组件',
            'webmain/flow/input/mode_customerAction.php' => '客户控制器'
        ];
        
        foreach ($files as $file => $description) {
            if (file_exists($file)) {
                $this->addResult("✅ {$description} 存在", true);
            } else {
                $this->addResult("❌ {$description} 不存在", false);
            }
        }
    }
    
    /**
     * 检查配置
     */
    private function checkConfig()
    {
        echo "<h3>⚙️ 配置检查</h3>\n";
        
        $configFile = 'webmain/config/mobileTabsConfig.php';
        if (file_exists($configFile)) {
            try {
                $config = include $configFile;
                
                if (is_array($config)) {
                    $this->addResult("✅ 配置文件格式正确", true);
                    
                    // 检查分类
                    if (isset($config['categories']) && is_array($config['categories'])) {
                        $categoryCount = count($config['categories']);
                        $this->addResult("✅ 分类数量: {$categoryCount}", true);
                        
                        if (isset($config['categories']['customer'])) {
                            $this->addResult("✅ 客户分类存在", true);
                        } else {
                            $this->addResult("❌ 客户分类不存在", false);
                        }
                    } else {
                        $this->addResult("❌ 分类配置错误", false);
                    }
                    
                    // 检查标签页
                    if (isset($config['tabs']) && is_array($config['tabs'])) {
                        if (isset($config['tabs']['customer']) && is_array($config['tabs']['customer'])) {
                            $tabCount = count($config['tabs']['customer']);
                            $this->addResult("✅ 客户标签页数量: {$tabCount}", true);
                            
                            foreach ($config['tabs']['customer'] as $index => $tab) {
                                $tabName = $tab['tab_name'] ?? '未知';
                                $tabCode = $tab['tab_code'] ?? '未知';
                                $contentType = $tab['content_type'] ?? '未知';
                                $this->addResult("   - 标签页 {$index}: {$tabName} ({$tabCode}) - {$contentType}", true);
                            }
                        } else {
                            $this->addResult("❌ 客户标签页配置不存在", false);
                        }
                    } else {
                        $this->addResult("❌ 标签页配置错误", false);
                    }
                    
                } else {
                    $this->addResult("❌ 配置文件返回格式错误", false);
                }
                
            } catch (Exception $e) {
                $this->addResult("❌ 配置文件解析错误: " . $e->getMessage(), false);
            }
        } else {
            $this->addResult("❌ 配置文件不存在", false);
        }
    }
    
    /**
     * 测试API
     */
    private function testAPI()
    {
        echo "<h3>🔌 API测试</h3>\n";
        
        // 测试配置模型
        try {
            // 加载模型文件
            if (file_exists('webmain/model/mobileTabConfigModel.php')) {
                require_once('webmain/model/mobileTabConfigModel.php');

                if (class_exists('mobileTabConfigClassModel')) {
                    $this->addResult("✅ 配置模型类存在", true);

                    // 创建模型实例（不依赖数据库）
                    $model = new mobileTabConfigClassModel();
                    $categories = $model->getCategoryList(['status' => 1]);
                    $this->addResult("✅ 获取分类列表成功，数量: " . count($categories), true);

                    $tabs = $model->getTabsByCategory(0, 'customer');
                    $this->addResult("✅ 获取客户标签页成功，数量: " . count($tabs), true);

                } else {
                    $this->addResult("❌ 配置模型类不存在", false);
                }
            } else {
                $this->addResult("❌ 配置模型文件不存在", false);
            }
        } catch (Exception $e) {
            $this->addResult("❌ 配置模型测试失败: " . $e->getMessage(), false);
        }
        
        // 测试客户控制器
        try {
            // 加载客户控制器文件
            if (file_exists('webmain/flow/input/mode_customerAction.php')) {
                require_once('webmain/flow/input/mode_customerAction.php');

                if (class_exists('mode_customerClassAction')) {
                    $this->addResult("✅ 客户控制器类存在", true);
                } else {
                    $this->addResult("❌ 客户控制器类不存在", false);
                }
            } else {
                $this->addResult("❌ 客户控制器文件不存在", false);
            }
        } catch (Exception $e) {
            $this->addResult("❌ 客户控制器测试失败: " . $e->getMessage(), false);
        }
    }
    
    /**
     * 检查客户数据
     */
    private function checkCustomerData()
    {
        echo "<h3>👥 客户数据检查</h3>\n";

        try {
            global $db;

            // 如果全局$db不存在，尝试创建数据库连接
            if (!$db) {
                // 获取数据库配置
                $configFile = 'webmain/webmainConfig.php';
                if (file_exists($configFile)) {
                    $config = include $configFile;

                    // 创建数据库连接
                    $dbClass = $config['db_drive'] . 'Class';
                    $dbFile = 'include/class/' . $dbClass . '.php';

                    if (file_exists($dbFile)) {
                        require_once($dbFile);
                        $db = new $dbClass();
                        $db->changeattr($config['db_host'], $config['db_user'], $config['db_pass'], $config['db_base']);
                        $db->connectdb();
                        $GLOBALS['db'] = $db;
                    }
                }
            }

            if ($db && method_exists($db, 'isconnect') && $db->isconnect()) {
                $this->addResult("✅ 数据库连接正常", true);
                
                // 检查客户表
                $customerCount = $db->getmou("SELECT COUNT(*) as count FROM `" . PREFIX . "customer`");
                if ($customerCount) {
                    $this->addResult("✅ 客户表存在，记录数: " . $customerCount['count'], true);
                    
                    // 获取一个示例客户
                    $sampleCustomer = $db->getmou("SELECT * FROM `" . PREFIX . "customer` LIMIT 1");
                    if ($sampleCustomer) {
                        $this->addResult("✅ 示例客户: ID={$sampleCustomer['id']}, 名称={$sampleCustomer['name']}", true);
                    } else {
                        $this->addResult("⚠️ 没有客户数据", false);
                    }
                } else {
                    $this->addResult("❌ 客户表不存在或查询失败", false);
                }
                
                // 检查相关表
                $tables = ['custsale', 'custract', 'contacts', 'custplan'];
                foreach ($tables as $table) {
                    $fullTableName = PREFIX . $table;
                    $exists = $db->query("SHOW TABLES LIKE '$fullTableName'");
                    if ($exists && $db->num_rows($exists) > 0) {
                        $count = $db->getmou("SELECT COUNT(*) as count FROM `$fullTableName`");
                        $this->addResult("✅ 表 {$table} 存在，记录数: " . ($count['count'] ?? 0), true);
                    } else {
                        $this->addResult("❌ 表 {$table} 不存在", false);
                    }
                }
                
            } else {
                $this->addResult("❌ 数据库连接失败", false);
            }
        } catch (Exception $e) {
            $this->addResult("❌ 数据库检查失败: " . $e->getMessage(), false);
        }
    }
    
    /**
     * 添加结果
     */
    private function addResult($message, $success)
    {
        $this->results[] = ['message' => $message, 'success' => $success];
        echo "<div style='margin: 5px 0; color: " . ($success ? 'green' : 'red') . ";'>{$message}</div>\n";
    }
    
    /**
     * 打印总结
     */
    private function printSummary()
    {
        echo "<h3>📊 调试总结</h3>\n";
        
        $total = count($this->results);
        $passed = 0;
        $failed = 0;
        
        foreach ($this->results as $result) {
            if ($result['success']) {
                $passed++;
            } else {
                $failed++;
            }
        }
        
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
        echo "<strong>总检查项:</strong> {$total}<br>\n";
        echo "<strong>通过:</strong> <span style='color: green;'>{$passed}</span><br>\n";
        echo "<strong>失败:</strong> <span style='color: red;'>{$failed}</span><br>\n";
        echo "<strong>成功率:</strong> " . round(($passed / $total) * 100, 2) . "%<br>\n";
        
        if ($failed == 0) {
            echo "<div style='color: green; font-size: 18px; margin-top: 15px;'>🎉 所有检查通过！</div>\n";
            echo "<div style='margin-top: 15px;'>\n";
            echo "<h4>下一步建议：</h4>\n";
            echo "<ol>\n";
            echo "<li>访问测试页面：<a href='test_mobile_tabs.html' target='_blank'>test_mobile_tabs.html</a></li>\n";
            echo "<li>检查浏览器控制台是否有JavaScript错误</li>\n";
            echo "<li>确认AJAX请求是否正常返回数据</li>\n";
            echo "</ol>\n";
            echo "</div>\n";
        } else {
            echo "<div style='color: red; font-size: 18px; margin-top: 15px;'>⚠️ 发现 {$failed} 个问题</div>\n";
            echo "<div style='margin-top: 15px;'>\n";
            echo "<h4>问题解决建议：</h4>\n";
            echo "<ol>\n";
            echo "<li>检查文件是否正确部署</li>\n";
            echo "<li>确认配置文件格式正确</li>\n";
            echo "<li>验证数据库连接和表结构</li>\n";
            echo "<li>检查PHP错误日志</li>\n";
            echo "</ol>\n";
            echo "</div>\n";
        }
        
        echo "</div>\n";
        
        // 提供快速修复链接
        echo "<div style='margin-top: 20px; padding: 15px; background: #e7f3ff; border-radius: 5px;'>\n";
        echo "<h4>🔧 快速操作：</h4>\n";
        echo "<a href='?action=test_config' style='margin-right: 10px;'>测试配置</a>\n";
        echo "<a href='?action=reset_config' style='margin-right: 10px;'>重置配置</a>\n";
        echo "<a href='install_mobile_tabs_config.php' style='margin-right: 10px;'>重新安装</a>\n";
        echo "</div>\n";
    }
}

// 处理操作
if (isset($_GET['action'])) {
    $action = $_GET['action'];
    
    if ($action === 'test_config') {
        echo "<h3>配置测试结果</h3>";
        $configFile = 'webmain/config/mobileTabsConfig.php';
        if (file_exists($configFile)) {
            $config = include $configFile;
            echo "<pre>" . json_encode($config, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
        } else {
            echo "配置文件不存在";
        }
        exit;
    }
    
    if ($action === 'reset_config') {
        echo "<h3>重置配置</h3>";
        echo "<p>请手动删除 webmain/config/mobileTabsConfig.php 文件，然后重新运行安装程序。</p>";
        exit;
    }
}

// 运行调试
$debugger = new MobileTabsDebugger();
$debugger->debug();
?>
