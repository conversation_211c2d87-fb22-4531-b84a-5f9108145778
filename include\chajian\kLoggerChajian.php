<?php
/***
 * <AUTHOR>
 * @email liaoming<PERSON><EMAIL>
 * 文件日志插件
 *
 **/
class KLogger<PERSON><PERSON><PERSON>an
{
    // 日志级别常量
    const DEBUG = 1;
    const INFO = 2;
    const WARNING = 3;
    const ERROR = 4;

    private $logPath;
    private $logLevel;

    // 初始化日志路径和级别
    public function __construct($path = 'klogs/', $level = self::DEBUG) {
        $this->logPath =  rtrim(ROOT_PATH.'/'.$path, '/') . '/';
        $this->logLevel = $level;

        // 自动创建日志目录
        if (!is_dir($this->logPath)) {
            mkdir($this->logPath, 0755, true);
        }
    }

    // 核心写入方法
    public function write($message, $level = self::INFO) {
        if ($level < $this->logLevel) return;

        $levelName = $this->getLevelName($level);
        $logFile = $this->logPath . 'log-' . date('Y-m-d') . '.log';
        $msg = '';
        if (is_object($message) && method_exists($message,'__toString')) {
            $msg = $message->__toString();
        }elseif(is_array($message)){
            $msg = implode($message);
        }else{
            $msg = $message;
        }
        // 构建日志内容
        $logContent = sprintf("[%s] %s: %s (%s:%d)\n",
            date('Y-m-d H:i:s'),
            $levelName,
            $msg,
            $this->getCallerFile(),
            $this->getCallerLine()
        );

        // 写入文件（追加模式）
        file_put_contents($logFile, $logContent, FILE_APPEND);
    }

    // 获取调用者文件信息
    private function getCallerFile() {
        $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 3);
        return isset($backtrace[2]['file']) ? basename($backtrace[2]['file']) : 'unknown';
    }

    // 获取调用者行号
    private function getCallerLine() {
        $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 3);
        return isset($backtrace[2]['line']) ? $backtrace[2]['line'] : 0;
    }

    // 转换级别数字为名称
    private function getLevelName($level) {
        $levels = [
            self::DEBUG => 'DEBUG',
            self::INFO => 'INFO',
            self::WARNING => 'WARNING',
            self::ERROR => 'ERROR'
        ];
        return isset($levels[$level]) ? $levels[$level] : 'UNKNOWN';
    }

    // 快捷方法
    public function debug($msg) { $this->write($msg, self::DEBUG); }
    public function info($msg) { $this->write($msg, self::INFO); }
    public function warning($msg) { $this->write($msg, self::WARNING); }
    public function error($msg) { $this->write($msg, self::ERROR); }

}