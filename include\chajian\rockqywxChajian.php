<?php
/**
*	信呼企业微信平台对接
*/

class rockqywxChajian extends Chajian
{
	private $platurl 	= '';
	private $optionobj;
	
	protected function initChajian()
	{
		$this->platurl = getconfig('rockqywx_url');
		if(!$this->platurl){
			if(getconfig('systype')=='dev'){
				$this->platurl = $this->rock->jm->base64decode('aHR0cDovLzE5Mi4xNjguMS4yL2FwcC9xeXd4Lw::');
			}else{
				$this->platurl=$this->rock->jm->base64decode('aHR0cDovL3F5d3gucm9ja29hLmNvbS8:');
			}
		}
		$this->optionobj = m('option');
		$this->cnum 	 = $this->optionobj->getval('qywxplat_cnum');
		$this->secret 	 = $this->optionobj->getval('qywxplat_secret');
	}
	
	public function isconfig()
	{
		if($this->cnum && $this->secret)return true;
		return false;
	}
	
	public function geturlstr($act, $can=array(),$jbs=false)
	{
		$url = $this->platurl;
		$url.= 'api.php?a='.$act.'&m=rockqywx';
		$url.= '&cnum='.$this->cnum.'&secret='.$this->secret.'';
		if(!$jbs)$url.= '&host='.$this->rock->jm->base64encode(HOST).'&version='.VERSION.'&time='.time().'&ipstr='.$this->rock->ip.'&xinhukey='.getconfig('xinhukey').'';
		foreach($can as $k=>$v)$url.='&'.$k.'='.$v.'';
		return $url;
	}
	
	public function getdata($act, $can=array(), $data=array())
	{
		return returnerror('此功能已停用');
		if(!$this->cnum)return returnerror('未设置单位编号');
		if(!$this->secret)return returnerror('未设置单位secret');
		$url 	= $this->geturlstr($act, $can);
		if($data){
			$cont 	= c('curl')->postcurl($url, $data);
		}else{
			$cont 	= c('curl')->getcurl($url);
		}
		$data  	= array('code'=>199,'msg'=>'出错了返回:'.htmlspecialchars($cont).'');
		if($cont!='' && substr($cont,0,1)=='{'){
			$data  	= json_decode($cont, true);
		}
		return $data;
	}
	
	public function postdata($act, $data=array(), $can=array())
	{
		return $this->getdata($act, $can, $data);
	}
	
	/**
	*	获取企业微信用户到OA里面
	*/
	public function userlist($userid='')
	{
		$barr = $this->getdata('userreload', array('userid'=>$userid));
		if(!$barr['success'])return $barr;
		$data = $barr['data'];
		$db   = m('zqywx_user');
		$where= '1=1';
		if($userid)$where = "`user`='$userid'";
		$db->delete($where);
		foreach($data as $k=>$rs)$db->insert($rs);
		return returnsuccess($userid);
	}
	
	/**
	*	发送消息提醒(使用异步的)
	*/
	public function sendmess($toid, $title, $cont='', $wxurl='', $picurl='', $istest=false)
	{
		$purl = $this->optionobj->getval('reimpushurlsystem');
		if(isempt($purl))return returnerror('未配置服务端无法异步不能使用');
		if($toid=='all'){
			$touser = ',@all';
		}else{
			$sql 	= 'select a.`id`,b.`user` from `[Q]admin` a left join `[Q]zqywx_user` b on ((b.`mobile`=a.`mobile`) or (a.`name`=b.`name`)) where a.`status`=1 and b.`state`=1 and a.`id` in('.$toid.')';
			$rows	= $this->db->getall($sql);
			if(!$rows)return returnerror('“'.$toid.'”企业微信用户未激活');
			$touser  = '';
			foreach($rows as $k=>$rs){
				$touser .= '|'.$rs['user'].'';
			}
		}
		$data['touser'] = substr($touser, 1);
		$data['title'] 	= $title;
		$data['cont'] 	= $cont;
		if($wxurl)$data['wxurl'] 	= $this->rock->jm->base64encode($wxurl);
		if($picurl)$data['picurl']  = $this->rock->jm->base64encode($picurl);
		//$data['agent']  = $this->optionobj->getval('qywxplat_devnum');
		
		if($istest)return $this->postdata('sendmess', $data);
		return c('rockqueue')->push('qywx,qywxplatsend', array(
			'body' => $this->rock->jm->base64encode(json_encode($data))
		));
	}
	
	
	public function returnerrmsg($errmsg, $jms=true, $dlx='we')
	{
		if($jms)$errmsg = $this->rock->jm->base64encode($errmsg);
		$url = '?d='.$dlx.'&m=login&errmsg='.$errmsg.'';
		$this->rock->location($url);
		exit();
	}
	
	/**
	*	网页授权登录
	*/
	public function authlogin($agent)
	{
		if(!$this->cnum)return;
		$uri = URL.'?d=we&m=login&a=wxqyback';
		$uri.= '&backurl='.$this->rock->get('backurl').'';
		$url = $this->geturlstr('loginoauth', array(
			'agentid' => $agent,
			'redirect'=> $this->rock->jm->base64encode($uri),
		), true);
		$this->rock->location($url);
	}
	
	/**
	*	网页授权登录成功返回的
	*/
	public function authloginback($userid, $errmsg)
	{
		if($errmsg)$this->returnerrmsg($errmsg, false);
		$oauthcode = $this->rock->get('oauthcode');
		if(!$oauthcode || !$userid)$this->returnerrmsg('dai无效操作1');
		$barr 	= $this->getdata('loginoauthcode', array('oauthcode' => $oauthcode));
		if(!$barr['success'])$this->returnerrmsg($barr['msg']);
		$data 	= $barr['data'];
		if(arrvalue($data, 'userid') != $userid)$this->returnerrmsg('dai无效操作2');
		$sql 	= 'select a.`id`,a.`user`,a.`pass` from `[Q]admin` a left join `[Q]zqywx_user` b on ((b.`mobile`=a.`mobile`) or (a.`name`=b.`name`)) where a.`status`=1 and b.`state`=1 and b.`user`=\''.$userid.'\'';
		$rows	= $this->db->getall($sql);
		if(!$rows)$this->returnerrmsg('无法使用快捷登录');
		$usr 	= $rows[0];
		$ptoken = md5($usr['pass']);
		$burl	= $this->rock->get('backurl');
		c('cache')->set('login'.$usr['user'].'', $usr['id'], 60);
		$url 	= '?m=login&d=we&user='.$this->rock->jm->base64encode($usr['user']).'&ptoken='.$ptoken.'';
		if($burl!='')$url.='&backurl='.$burl.'';
		$this->rock->clearsession('olaizhi');
		$this->rock->location($url);
	}
	
	/**
	*	获取打卡记录(需要用异步)
	*/
	public function getcheckindata($uids='', $startdt='', $enddt='', $page=1)
	{
		$url 	= $this->geturlstr('getcheckindata', array(), true);
		$barr 	= m('weixinqy:daka')->djgetcheckindata($url, $uids, $startdt, $enddt, $page);
		if(!$uids && $barr['success']){
			$data = $barr['data'];
			if($page < $data['maxpage']){
				$carr = c('rockqueue')->push('qywx,qywxplatdaka', array(
					'startdt' 	=> $startdt,
					'enddt' 	=> $enddt,
					'page' 		=> $page + 1,
				));
			}
		}
		return $barr;
	}
}