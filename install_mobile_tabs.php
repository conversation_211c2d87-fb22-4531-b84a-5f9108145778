<?php
/**
 * 移动端标签页功能安装脚本
 * 创建时间：2025-01-03
 * 用途：一键安装移动端标签页功能
 */

// 检查是否在正确的目录
if (!file_exists('config/config.php')) {
    die('请将此文件放在网站根目录下运行');
}

require_once('config/config.php');

class MobileTabsInstaller
{
    private $db;
    private $errors = [];
    private $success = [];
    
    public function __construct()
    {
        global $db;
        $this->db = $db;
    }
    
    /**
     * 执行安装
     */
    public function install()
    {
        echo "<h2>移动端标签页功能安装程序</h2>\n";
        echo "<div style='font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px;'>\n";
        
        $this->checkEnvironment();
        $this->createTables();
        $this->insertDefaultData();
        $this->checkFiles();
        
        $this->printSummary();
        echo "</div>\n";
    }
    
    /**
     * 检查环境
     */
    private function checkEnvironment()
    {
        echo "<h3>🔍 环境检查</h3>\n";
        
        // 检查PHP版本
        if (version_compare(PHP_VERSION, '5.6.0', '>=')) {
            $this->addSuccess("PHP版本: " . PHP_VERSION . " ✅");
        } else {
            $this->addError("PHP版本过低，需要5.6.0或更高版本");
        }
        
        // 检查数据库连接
        if ($this->db && $this->db->isconnect()) {
            $this->addSuccess("数据库连接正常 ✅");
        } else {
            $this->addError("数据库连接失败");
            return;
        }
        
        // 检查必要的目录
        $dirs = [
            'webmain/model',
            'webmain/system',
            'webmain/we/component',
            'webmain/css'
        ];
        
        foreach ($dirs as $dir) {
            if (is_dir($dir)) {
                $this->addSuccess("目录 {$dir} 存在 ✅");
            } else {
                $this->addError("目录 {$dir} 不存在");
            }
        }
    }
    
    /**
     * 创建数据表
     */
    private function createTables()
    {
        echo "<h3>📊 创建数据表</h3>\n";
        
        $tables = [
            'mobile_tab_category' => "
                CREATE TABLE `" . PREFIX . "mobile_tab_category` (
                  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
                  `name` varchar(50) NOT NULL COMMENT '分类名称',
                  `code` varchar(30) NOT NULL COMMENT '分类代码',
                  `description` varchar(200) DEFAULT NULL COMMENT '分类描述',
                  `sort` int(11) DEFAULT '0' COMMENT '排序号',
                  `status` tinyint(1) DEFAULT '1' COMMENT '状态：1启用，0禁用',
                  `optdt` datetime DEFAULT NULL COMMENT '操作时间',
                  `optid` int(11) DEFAULT '0' COMMENT '操作人ID',
                  `optname` varchar(20) DEFAULT NULL COMMENT '操作人姓名',
                  `comid` smallint(6) DEFAULT '0' COMMENT '公司ID',
                  PRIMARY KEY (`id`),
                  UNIQUE KEY `code` (`code`),
                  KEY `idx_sort` (`sort`),
                  KEY `idx_status` (`status`)
                ) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='移动端标签页分类表'
            ",
            'mobile_tab_config' => "
                CREATE TABLE `" . PREFIX . "mobile_tab_config` (
                  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
                  `category_id` int(11) NOT NULL COMMENT '分类ID',
                  `tab_name` varchar(50) NOT NULL COMMENT '标签名称',
                  `tab_code` varchar(30) NOT NULL COMMENT '标签代码',
                  `tab_icon` varchar(50) DEFAULT NULL COMMENT '标签图标',
                  `content_type` varchar(20) DEFAULT 'html' COMMENT '内容类型：html,ajax,iframe',
                  `content_source` text COMMENT '内容源：HTML内容或URL',
                  `load_method` varchar(20) DEFAULT 'immediate' COMMENT '加载方式：immediate立即，lazy懒加载',
                  `sort` int(11) DEFAULT '0' COMMENT '排序号',
                  `status` tinyint(1) DEFAULT '1' COMMENT '状态：1启用，0禁用',
                  `is_default` tinyint(1) DEFAULT '0' COMMENT '是否默认选中',
                  `permissions` varchar(200) DEFAULT NULL COMMENT '权限设置',
                  `optdt` datetime DEFAULT NULL COMMENT '操作时间',
                  `optid` int(11) DEFAULT '0' COMMENT '操作人ID',
                  `optname` varchar(20) DEFAULT NULL COMMENT '操作人姓名',
                  `comid` smallint(6) DEFAULT '0' COMMENT '公司ID',
                  PRIMARY KEY (`id`),
                  KEY `idx_category` (`category_id`),
                  KEY `idx_sort` (`sort`),
                  KEY `idx_status` (`status`),
                  KEY `idx_code` (`tab_code`)
                ) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='移动端标签页配置表'
            ",
            'mobile_tab_relation' => "
                CREATE TABLE `" . PREFIX . "mobile_tab_relation` (
                  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '关联ID',
                  `tab_id` int(11) NOT NULL COMMENT '标签ID',
                  `relation_type` varchar(20) NOT NULL COMMENT '关联类型：module模块，flow流程，custom自定义',
                  `relation_id` int(11) DEFAULT NULL COMMENT '关联对象ID',
                  `relation_code` varchar(50) DEFAULT NULL COMMENT '关联对象代码',
                  `condition_field` varchar(50) DEFAULT NULL COMMENT '条件字段',
                  `condition_value` varchar(100) DEFAULT NULL COMMENT '条件值',
                  `condition_operator` varchar(10) DEFAULT '=' COMMENT '条件操作符',
                  `sort` int(11) DEFAULT '0' COMMENT '排序号',
                  `status` tinyint(1) DEFAULT '1' COMMENT '状态：1启用，0禁用',
                  `optdt` datetime DEFAULT NULL COMMENT '操作时间',
                  `optid` int(11) DEFAULT '0' COMMENT '操作人ID',
                  `optname` varchar(20) DEFAULT NULL COMMENT '操作人姓名',
                  `comid` smallint(6) DEFAULT '0' COMMENT '公司ID',
                  PRIMARY KEY (`id`),
                  KEY `idx_tab` (`tab_id`),
                  KEY `idx_relation` (`relation_type`, `relation_id`),
                  KEY `idx_status` (`status`)
                ) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='移动端标签页关联表'
            ",
            'mobile_tab_stats' => "
                CREATE TABLE `" . PREFIX . "mobile_tab_stats` (
                  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '统计ID',
                  `tab_id` int(11) NOT NULL COMMENT '标签ID',
                  `user_id` int(11) NOT NULL COMMENT '用户ID',
                  `access_count` int(11) DEFAULT '0' COMMENT '访问次数',
                  `last_access_time` datetime DEFAULT NULL COMMENT '最后访问时间',
                  `total_duration` int(11) DEFAULT '0' COMMENT '总停留时间（秒）',
                  `date_created` date DEFAULT NULL COMMENT '创建日期',
                  `comid` smallint(6) DEFAULT '0' COMMENT '公司ID',
                  PRIMARY KEY (`id`),
                  UNIQUE KEY `unique_tab_user_date` (`tab_id`, `user_id`, `date_created`),
                  KEY `idx_user` (`user_id`),
                  KEY `idx_date` (`date_created`)
                ) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='移动端标签页使用统计表'
            "
        ];
        
        foreach ($tables as $tableName => $sql) {
            $fullTableName = PREFIX . $tableName;
            
            // 检查表是否已存在
            $exists = $this->db->query("SHOW TABLES LIKE '$fullTableName'");
            if ($exists && $this->db->num_rows($exists) > 0) {
                $this->addSuccess("表 {$tableName} 已存在 ✅");
                continue;
            }
            
            // 创建表
            if ($this->db->query($sql)) {
                $this->addSuccess("创建表 {$tableName} 成功 ✅");
            } else {
                $this->addError("创建表 {$tableName} 失败: " . $this->db->error());
            }
        }
    }
    
    /**
     * 插入默认数据
     */
    private function insertDefaultData()
    {
        echo "<h3>📝 插入默认数据</h3>\n";
        
        // 检查是否已有数据
        $count = $this->db->getmou(PREFIX . 'mobile_tab_category', 'COUNT(*) as count');
        if ($count && $count['count'] > 0) {
            $this->addSuccess("默认数据已存在 ✅");
            return;
        }
        
        // 插入默认分类
        $categories = [
            ['通用标签', 'general', '适用于所有页面的通用标签页', 1],
            ['客户详情', 'customer', '客户详情页面专用标签页', 2],
            ['项目详情', 'project', '项目详情页面专用标签页', 3],
            ['流程详情', 'flow', '流程详情页面专用标签页', 4]
        ];
        
        foreach ($categories as $category) {
            $sql = "INSERT INTO `" . PREFIX . "mobile_tab_category` 
                    (`name`, `code`, `description`, `sort`, `status`, `optdt`) VALUES 
                    ('{$category[0]}', '{$category[1]}', '{$category[2]}', {$category[3]}, 1, NOW())";
            
            if ($this->db->query($sql)) {
                $this->addSuccess("插入分类 {$category[0]} 成功 ✅");
            } else {
                $this->addError("插入分类 {$category[0]} 失败");
            }
        }
        
        // 插入示例标签页配置
        $customerCategoryId = $this->db->getmou(PREFIX . 'mobile_tab_category', 'id', "`code` = 'customer'");
        if ($customerCategoryId) {
            $tabs = [
                ['基本信息', 'basic_info', 'icon-info', 'html', '{contview}', 1, 1],
                ['联系记录', 'contact_record', 'icon-phone', 'ajax', 'we,component,getCustomerTabs', 2, 0],
                ['销售机会', 'sales_opportunity', 'icon-dollar', 'ajax', 'we,component,getCustomerTabs', 3, 0],
                ['合同信息', 'contract_info', 'icon-file-text', 'ajax', 'we,component,getCustomerTabs', 4, 0]
            ];
            
            foreach ($tabs as $tab) {
                $sql = "INSERT INTO `" . PREFIX . "mobile_tab_config` 
                        (`category_id`, `tab_name`, `tab_code`, `tab_icon`, `content_type`, `content_source`, `sort`, `status`, `is_default`, `optdt`) VALUES 
                        ({$customerCategoryId['id']}, '{$tab[0]}', '{$tab[1]}', '{$tab[2]}', '{$tab[3]}', '{$tab[4]}', {$tab[5]}, 1, {$tab[6]}, NOW())";
                
                if ($this->db->query($sql)) {
                    $this->addSuccess("插入标签页 {$tab[0]} 成功 ✅");
                } else {
                    $this->addError("插入标签页 {$tab[0]} 失败");
                }
            }
        }
    }
    
    /**
     * 检查文件
     */
    private function checkFiles()
    {
        echo "<h3>📁 检查文件</h3>\n";
        
        $files = [
            'webmain/model/mobileTabModel.php' => '数据模型',
            'webmain/system/mobiletab/mobileTabAction.php' => '管理控制器',
            'webmain/system/mobiletab/tpl_mobileTab.html' => '管理界面',
            'webmain/system/mobiletab/mobileTab.js' => '管理脚本',
            'webmain/we/component/componentAction.php' => 'API控制器',
            'webmain/we/component/mobileTabs.js' => '移动端组件',
            'webmain/flow/page/view_customer_mobile_tabs.html' => '示例模板'
        ];
        
        foreach ($files as $file => $description) {
            if (file_exists($file)) {
                $this->addSuccess("{$description} ({$file}) 存在 ✅");
            } else {
                $this->addError("{$description} ({$file}) 不存在");
            }
        }
    }
    
    /**
     * 添加成功信息
     */
    private function addSuccess($message)
    {
        $this->success[] = $message;
        echo "<div style='color: green; margin: 5px 0;'>{$message}</div>\n";
    }
    
    /**
     * 添加错误信息
     */
    private function addError($message)
    {
        $this->errors[] = $message;
        echo "<div style='color: red; margin: 5px 0;'>❌ {$message}</div>\n";
    }
    
    /**
     * 打印安装总结
     */
    private function printSummary()
    {
        echo "<h3>📊 安装总结</h3>\n";
        
        $totalSuccess = count($this->success);
        $totalErrors = count($this->errors);
        
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
        echo "<strong>成功项目:</strong> {$totalSuccess}<br>\n";
        echo "<strong>错误项目:</strong> {$totalErrors}<br>\n";
        
        if ($totalErrors == 0) {
            echo "<div style='color: green; font-size: 18px; margin-top: 15px;'>🎉 安装完成！</div>\n";
            echo "<div style='margin-top: 15px;'>\n";
            echo "<h4>下一步操作：</h4>\n";
            echo "<ol>\n";
            echo "<li>访问系统管理 → 添加菜单：移动端标签页管理</li>\n";
            echo "<li>菜单地址：index.php?d=system&m=mobiletab</li>\n";
            echo "<li>配置标签页分类和标签页</li>\n";
            echo "<li>在移动端页面中集成标签页功能</li>\n";
            echo "</ol>\n";
            echo "</div>\n";
        } else {
            echo "<div style='color: red; font-size: 18px; margin-top: 15px;'>⚠️ 安装过程中出现错误</div>\n";
            echo "<div style='margin-top: 15px;'>\n";
            echo "<h4>错误列表：</h4>\n";
            echo "<ul>\n";
            foreach ($this->errors as $error) {
                echo "<li style='color: red;'>{$error}</li>\n";
            }
            echo "</ul>\n";
            echo "</div>\n";
        }
        
        echo "</div>\n";
    }
}

// 执行安装
if (isset($_GET['install'])) {
    $installer = new MobileTabsInstaller();
    $installer->install();
} else {
    echo '<h2>移动端标签页功能安装程序</h2>';
    echo '<div style="max-width: 600px; margin: 50px auto; padding: 20px; border: 1px solid #ddd; border-radius: 10px; font-family: Arial, sans-serif;">';
    echo '<h3>安装说明</h3>';
    echo '<p>此安装程序将为您的系统安装移动端标签页功能，包括：</p>';
    echo '<ul>';
    echo '<li>创建必要的数据库表</li>';
    echo '<li>插入默认配置数据</li>';
    echo '<li>检查文件完整性</li>';
    echo '</ul>';
    echo '<p><strong>注意：</strong>请确保您有数据库管理权限。</p>';
    echo '<div style="text-align: center; margin-top: 30px;">';
    echo '<a href="?install=1" style="background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-size: 16px;">开始安装</a>';
    echo '</div>';
    echo '</div>';
}
?>
