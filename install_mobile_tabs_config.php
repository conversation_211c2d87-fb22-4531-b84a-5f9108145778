<?php
/**
 * 移动端标签页功能安装脚本（配置文件版本）
 * 创建时间：2025-01-03
 * 用途：一键安装基于配置文件的移动端标签页功能
 */

// 检查是否在正确的目录
if (!file_exists('config/config.php')) {
    die('请将此文件放在网站根目录下运行');
}

class MobileTabsConfigInstaller
{
    private $errors = [];
    private $success = [];
    
    /**
     * 执行安装
     */
    public function install()
    {
        echo "<h2>移动端标签页功能安装程序（配置文件版本）</h2>\n";
        echo "<div style='font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px;'>\n";
        
        $this->checkEnvironment();
        $this->createDirectories();
        $this->checkFiles();
        $this->createConfigFile();
        
        $this->printSummary();
        echo "</div>\n";
    }
    
    /**
     * 检查环境
     */
    private function checkEnvironment()
    {
        echo "<h3>🔍 环境检查</h3>\n";
        
        // 检查PHP版本
        if (version_compare(PHP_VERSION, '5.6.0', '>=')) {
            $this->addSuccess("PHP版本: " . PHP_VERSION . " ✅");
        } else {
            $this->addError("PHP版本过低，需要5.6.0或更高版本");
        }
        
        // 检查文件写入权限
        $configDir = 'webmain/config';
        if (is_writable($configDir) || is_writable('.')) {
            $this->addSuccess("文件写入权限正常 ✅");
        } else {
            $this->addError("没有文件写入权限，请检查目录权限");
        }
    }
    
    /**
     * 创建必要的目录
     */
    private function createDirectories()
    {
        echo "<h3>📁 创建目录</h3>\n";
        
        $dirs = [
            'webmain/config',
            'webmain/model',
            'webmain/system/mobiletabconfig',
            'webmain/we/component'
        ];
        
        foreach ($dirs as $dir) {
            if (!is_dir($dir)) {
                if (mkdir($dir, 0755, true)) {
                    $this->addSuccess("创建目录 {$dir} 成功 ✅");
                } else {
                    $this->addError("创建目录 {$dir} 失败");
                }
            } else {
                $this->addSuccess("目录 {$dir} 已存在 ✅");
            }
        }
    }
    
    /**
     * 检查文件
     */
    private function checkFiles()
    {
        echo "<h3>📄 检查文件</h3>\n";
        
        $files = [
            'webmain/model/mobileTabConfigModel.php' => '配置模型',
            'webmain/system/mobiletabconfig/mobileTabConfigAction.php' => '管理控制器',
            'webmain/system/mobiletabconfig/tpl_mobileTabConfig.html' => '管理界面',
            'webmain/we/component/componentAction.php' => 'API控制器',
            'webmain/we/component/mobileTabs.js' => '移动端组件'
        ];
        
        foreach ($files as $file => $description) {
            if (file_exists($file)) {
                $this->addSuccess("{$description} ({$file}) 存在 ✅");
            } else {
                $this->addError("{$description} ({$file}) 不存在");
            }
        }
    }
    
    /**
     * 创建配置文件
     */
    private function createConfigFile()
    {
        echo "<h3>⚙️ 创建配置文件</h3>\n";
        
        $configFile = 'webmain/config/mobileTabsConfig.php';
        
        if (file_exists($configFile)) {
            $this->addSuccess("配置文件已存在 ✅");
            return;
        }
        
        $configContent = $this->getDefaultConfigContent();
        
        if (file_put_contents($configFile, $configContent)) {
            $this->addSuccess("创建配置文件成功 ✅");
            
            // 创建备份文件
            $backupFile = $configFile . '.default';
            copy($configFile, $backupFile);
            $this->addSuccess("创建默认配置备份 ✅");
            
        } else {
            $this->addError("创建配置文件失败，请检查文件权限");
        }
    }
    
    /**
     * 获取默认配置内容
     */
    private function getDefaultConfigContent()
    {
        return '<?php
/**
 * 移动端标签页配置文件
 * 创建时间：' . date('Y-m-d H:i:s') . '
 * 说明：基于配置文件的标签页管理，无需数据库
 */

if (!defined(\'HOST\')) die(\'not access\');

return [
    // 标签页分类配置
    \'categories\' => [
        \'general\' => [
            \'name\' => \'通用标签\',
            \'description\' => \'适用于所有页面的通用标签页\',
            \'sort\' => 1,
            \'status\' => 1
        ],
        \'customer\' => [
            \'name\' => \'客户详情\',
            \'description\' => \'客户详情页面专用标签页\',
            \'sort\' => 2,
            \'status\' => 1
        ],
        \'project\' => [
            \'name\' => \'项目详情\',
            \'description\' => \'项目详情页面专用标签页\',
            \'sort\' => 3,
            \'status\' => 1
        ],
        \'flow\' => [
            \'name\' => \'流程详情\',
            \'description\' => \'流程详情页面专用标签页\',
            \'sort\' => 4,
            \'status\' => 1
        ]
    ],
    
    // 标签页配置
    \'tabs\' => [
        // 客户详情标签页
        \'customer\' => [
            [
                \'tab_name\' => \'基本信息\',
                \'tab_code\' => \'basic_info\',
                \'tab_icon\' => \'icon-info\',
                \'content_type\' => \'html\',
                \'content_source\' => \'<div class="basic-info-content">{contview}</div>\',
                \'load_method\' => \'immediate\',
                \'sort\' => 1,
                \'status\' => 1,
                \'is_default\' => 1,
                \'permissions\' => \'\'
            ],
            [
                \'tab_name\' => \'联系记录\',
                \'tab_code\' => \'contact_record\',
                \'tab_icon\' => \'icon-phone\',
                \'content_type\' => \'ajax\',
                \'content_source\' => \'we,component,getCustomerTabs\',
                \'load_method\' => \'lazy\',
                \'sort\' => 2,
                \'status\' => 1,
                \'is_default\' => 0,
                \'permissions\' => \'\'
            ]
        ],
        
        // 项目详情标签页
        \'project\' => [
            [
                \'tab_name\' => \'项目详情\',
                \'tab_code\' => \'project_info\',
                \'tab_icon\' => \'icon-info\',
                \'content_type\' => \'html\',
                \'content_source\' => \'{contview}\',
                \'load_method\' => \'immediate\',
                \'sort\' => 1,
                \'status\' => 1,
                \'is_default\' => 1,
                \'permissions\' => \'\'
            ]
        ]
    ],
    
    // 关联配置
    \'relations\' => [
        \'customer\' => \'customer\',
        \'project\' => \'project\',
        \'flow\' => \'flow\'
    ],
    
    // 全局设置
    \'settings\' => [
        \'enable_stats\' => false,
        \'cache_enabled\' => true,
        \'default_load_method\' => \'immediate\',
        \'max_tabs\' => 10,
        \'enable_permissions\' => false
    ]
];
?>';
    }
    
    /**
     * 添加成功信息
     */
    private function addSuccess($message)
    {
        $this->success[] = $message;
        echo "<div style='color: green; margin: 5px 0;'>{$message}</div>\n";
    }
    
    /**
     * 添加错误信息
     */
    private function addError($message)
    {
        $this->errors[] = $message;
        echo "<div style='color: red; margin: 5px 0;'>❌ {$message}</div>\n";
    }
    
    /**
     * 打印安装总结
     */
    private function printSummary()
    {
        echo "<h3>📊 安装总结</h3>\n";
        
        $totalSuccess = count($this->success);
        $totalErrors = count($this->errors);
        
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
        echo "<strong>成功项目:</strong> {$totalSuccess}<br>\n";
        echo "<strong>错误项目:</strong> {$totalErrors}<br>\n";
        
        if ($totalErrors == 0) {
            echo "<div style='color: green; font-size: 18px; margin-top: 15px;'>🎉 安装完成！</div>\n";
            echo "<div style='margin-top: 15px;'>\n";
            echo "<h4>下一步操作：</h4>\n";
            echo "<ol>\n";
            echo "<li>访问系统管理 → 添加菜单：移动端标签页配置</li>\n";
            echo "<li>菜单地址：index.php?d=system&m=mobiletabconfig</li>\n";
            echo "<li>配置标签页分类和标签页</li>\n";
            echo "<li>在移动端页面中集成标签页功能</li>\n";
            echo "</ol>\n";
            echo "<div style='background: #e7f3ff; padding: 10px; border-radius: 5px; margin-top: 15px;'>\n";
            echo "<strong>优势：</strong><br>\n";
            echo "✅ 无需数据库，基于配置文件<br>\n";
            echo "✅ 配置简单，易于维护<br>\n";
            echo "✅ 支持导入导出配置<br>\n";
            echo "✅ 完全兼容现有系统\n";
            echo "</div>\n";
            echo "</div>\n";
        } else {
            echo "<div style='color: red; font-size: 18px; margin-top: 15px;'>⚠️ 安装过程中出现错误</div>\n";
            echo "<div style='margin-top: 15px;'>\n";
            echo "<h4>错误列表：</h4>\n";
            echo "<ul>\n";
            foreach ($this->errors as $error) {
                echo "<li style='color: red;'>{$error}</li>\n";
            }
            echo "</ul>\n";
            echo "</div>\n";
        }
        
        echo "</div>\n";
    }
}

// 执行安装
if (isset($_GET['install'])) {
    $installer = new MobileTabsConfigInstaller();
    $installer->install();
} else {
    echo '<h2>移动端标签页功能安装程序（配置文件版本）</h2>';
    echo '<div style="max-width: 600px; margin: 50px auto; padding: 20px; border: 1px solid #ddd; border-radius: 10px; font-family: Arial, sans-serif;">';
    echo '<h3>安装说明</h3>';
    echo '<p>此版本基于配置文件，<strong>无需数据库</strong>，具有以下优势：</p>';
    echo '<ul>';
    echo '<li>✅ 无需修改数据库结构</li>';
    echo '<li>✅ 配置简单，易于维护</li>';
    echo '<li>✅ 支持配置导入导出</li>';
    echo '<li>✅ 完全兼容现有系统</li>';
    echo '</ul>';
    echo '<p>安装程序将：</p>';
    echo '<ul>';
    echo '<li>检查环境和权限</li>';
    echo '<li>创建必要的目录</li>';
    echo '<li>生成默认配置文件</li>';
    echo '<li>验证文件完整性</li>';
    echo '</ul>';
    echo '<div style="text-align: center; margin-top: 30px;">';
    echo '<a href="?install=1" style="background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-size: 16px;">开始安装</a>';
    echo '</div>';
    echo '</div>';
}
?>
