# 客户管理标签页集成指南

## 🎯 集成概述

现在您已经成功测试了标签页功能，以下是在实际客户管理系统中应用的完整指南。

## 📋 集成步骤

### 第一步：在客户详情页面中添加标签页容器

在您的客户详情页面模板中添加标签页容器：

```html
<!-- 客户基本信息展示区域 -->
<div class="customer-header">
    <h3>{$customer.name}</h3>
    <p>客户ID: {$customer.id}</p>
</div>

<!-- 标签页容器 -->
<div id="customerTabs" class="mobile-tabs-container">
    <!-- 标签页将通过JavaScript动态生成 -->
</div>
```

### 第二步：加载CSS样式

确保加载了标签页样式文件：

```html
<link rel="stylesheet" href="webmain/css/rui.css">
```

### 第三步：加载JavaScript组件

在页面底部加载标签页组件：

```html
<script src="webmain/we/component/mobileTabs.js"></script>
```

### 第四步：初始化标签页

在页面JavaScript中初始化标签页：

```javascript
// 页面加载完成后初始化标签页
$(document).ready(function() {
    var customerId = {$customer.id}; // 从模板获取客户ID
    
    // 初始化客户标签页
    initCustomerTabs(customerId);
});

function initCustomerTabs(customerId) {
    // 1. 获取标签页配置
    $.ajax({
        url: 'index.php?d=we&m=component&a=getMobileTabs&category_code=customer',
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.success && response.data.length > 0) {
                // 2. 创建标签页界面
                createCustomerTabs(response.data, customerId);
            } else {
                console.error('获取标签页配置失败：', response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('标签页配置请求失败：', error);
        }
    });
}

function createCustomerTabs(tabsData, customerId) {
    var container = $('#customerTabs');
    container.empty();
    
    // 创建标签页导航
    var tabNav = $('<div class="r-tabs">');
    
    // 创建内容容器
    var contentContainer = $('<div class="tabs-content">');
    
    tabsData.forEach(function(tab, index) {
        // 创建标签页按钮
        var tabItem = $('<div class="r-tabs-item">');
        tabItem.attr('data-index', index);
        tabItem.text(tab.tab_name);
        
        // 绑定点击事件
        tabItem.click(function() {
            switchCustomerTab(index, tab, customerId);
        });
        
        tabNav.append(tabItem);
        
        // 创建标签页内容区域
        var tabContent = $('<div class="tab-content">');
        tabContent.attr('data-index', index);
        tabContent.html('<div class="loading">加载中...</div>');
        
        contentContainer.append(tabContent);
    });
    
    container.append(tabNav);
    container.append(contentContainer);
    
    // 激活第一个标签页
    switchCustomerTab(0, tabsData[0], customerId);
}

function switchCustomerTab(index, tab, customerId) {
    // 更新导航状态
    $('.r-tabs-item').removeClass('active');
    $('.r-tabs-item[data-index="' + index + '"]').addClass('active');
    
    // 更新内容显示
    $('.tab-content').hide();
    var currentContent = $('.tab-content[data-index="' + index + '"]');
    currentContent.show();
    
    // 加载标签页内容
    loadTabContent(tab, customerId, currentContent);
}

function loadTabContent(tab, customerId, container) {
    if (tab.content_type === 'html') {
        // 静态HTML内容，替换客户数据
        loadStaticContent(tab, customerId, container);
    } else if (tab.content_type === 'ajax') {
        // AJAX动态内容
        loadAjaxContent(tab, customerId, container);
    }
}

function loadStaticContent(tab, customerId, container) {
    // 获取客户详细信息（假设已经在页面中）
    var customerData = {
        name: '{$customer.name}',
        tel: '{$customer.tel}',
        lxr: '{$customer.lxr}',
        address: '{$customer.address}',
        khlx: '{$customer.khlx}',
        khly: '{$customer.khly}',
        khzt: '{$customer.khzt}',
        optdt: '{$customer.optdt}',
        explain: '{$customer.explain}'
    };
    
    // 替换模板变量
    var content = tab.content_source;
    Object.keys(customerData).forEach(function(key) {
        var regex = new RegExp('\\{' + key + '\\}', 'g');
        content = content.replace(regex, customerData[key] || '');
    });
    
    // 处理条件显示
    if (customerData.explain) {
        content = content.replace(/\{if_explain\}/g, '').replace(/\{endif_explain\}/g, '');
    } else {
        content = content.replace(/\{if_explain\}[\s\S]*?\{endif_explain\}/g, '');
    }
    
    container.html(content);
}

function loadAjaxContent(tab, customerId, container) {
    container.html('<div class="loading">加载中...</div>');
    
    $.ajax({
        url: tab.content_source + '&customer_id=' + customerId,
        type: 'GET',
        success: function(data) {
            container.html(data);
        },
        error: function(xhr, status, error) {
            container.html('<div class="error">加载失败：' + error + '</div>');
        }
    });
}
```

## 🔧 现有系统集成

### 方案1：在现有客户详情页面中集成

找到您的客户详情页面文件（通常在 `webmain/flow/input/` 或类似目录），在适当位置添加标签页容器和初始化代码。

### 方案2：修改客户模块模板

如果使用模板系统，在客户详情模板中添加标签页功能：

```smarty
{* 客户详情模板 *}
<div class="customer-detail">
    {* 现有的客户信息展示 *}
    
    {* 新增：标签页容器 *}
    <div id="customerTabs" class="mobile-tabs-container"></div>
</div>

<script>
$(document).ready(function() {
    initCustomerTabs({$rs.id});
});
</script>
```

### 方案3：作为独立组件使用

创建一个独立的客户标签页组件，可以在任何需要的地方调用：

```javascript
// 全局函数，可在任何页面调用
window.CustomerTabs = {
    init: function(customerId, container) {
        // 初始化逻辑
    },
    
    refresh: function(customerId) {
        // 刷新标签页内容
    }
};
```

## 📱 移动端适配

标签页已经针对移动端进行了优化，但您可能需要在移动端页面中特别处理：

```css
/* 移动端样式调整 */
@media (max-width: 768px) {
    .mobile-tabs-container {
        margin: 0 -15px; /* 全宽显示 */
    }
    
    .r-tabs {
        overflow-x: auto; /* 水平滚动 */
    }
    
    .r-tabs-item {
        min-width: 80px; /* 最小宽度 */
        flex-shrink: 0; /* 不收缩 */
    }
}
```

## 🎯 下一步建议

1. **选择集成方案**：根据您的系统架构选择最适合的集成方案
2. **测试功能**：在开发环境中测试标签页功能
3. **自定义样式**：根据您的系统UI风格调整标签页样式
4. **扩展功能**：根据需要添加更多标签页类型

## 🔧 常见问题

### Q: 如何添加新的标签页？
A: 在 `webmain/config/mobileTabsConfig.php` 中的 `customer` 数组中添加新的标签页配置。

### Q: 如何自定义标签页样式？
A: 修改 `webmain/css/rui.css` 中的相关样式类。

### Q: 如何集成其他模块的数据？
A: 在 `webmain/we/component/componentAction.php` 中添加新的数据获取方法。

需要我帮您实现具体的集成步骤吗？
