var MODE	= '',ACTION = '',DIR='',PROJECT='',HOST='',PARAMS='',QOM='xinhu_',apiurl='',token='',device='',CFROM='pc',ISDEMO=false,NOWURL='',nwjsgui=false,apicloud=false,isapp=false,homestyle=0,maincolor='#1389D3';
var windows	= null,ismobile=0,clientbool = false;;
function initbody(){}
function bodyunload(){}
function globalbody(){}
function initApp(){}
function apiready(){apicloud=true;var key = 'apiwinname';var svst=js.request(key);if(svst)sessionStorage.setItem(key,svst);js.setapptitle();initApp();}
$(document).ready(function(){
	try{if(typeof(nw)=='object'){nwjsgui = nw;}else{nwjsgui = require('nw.gui');}}catch(e){nwjsgui=false;}
	$(window).scroll(js.scrolla);
	HOST = js.gethost();
	adminid=js.request('adminid');
	token=js.request('token');
	js.getsplit();
	device= js.cookie('deviceid');
	if(device=='')device=js.now('time');
	js.savecookie('deviceid', device, 365);
	js.apptheme();
	try{
		var winobj = js.request('winobj');
		if(nwjsgui)window.focus=function(){nw.Window.get().focus()}
		if(winobj!='')opener.js.openarr[winobj]=window;
	}catch(e){}
	var llq = navigator.userAgent;
	if(llq.indexOf('REIMCLIENT')>0)clientbool = true;
	globalbody();
	initbody();
	$('body').click(function(e){
		js.downbody(this, e);
	});
	$(window).unload(function(){
		js.onunload();
		bodyunload();
	});
	var openfrom = js.request('openfrom',js.getoption('openfrom','', true));
	js.setoption('openfrom', openfrom, true);
	
	if(HOST=='127.0.0.1' || HOST=='localhost' || HOST.indexOf('192.168.')>-1)window.addEventListener('error',function(e){
		var msg = '文件：'+e.filename+'\n行：'+e.lineno+'\n错误：<font color=red>'+e.message+'</font>';
		js.alert(msg,'js错误');
	});
	if(llq.indexOf('XINHUOA')<0){
		if(typeof(api)=='undefined')api={};
	}
	setTimeout(function(){
		if(typeof(api)=='undefined'){
			api={};
			api.systemType='androidnew';
			api.deviceId='';
		}
	},2000);
});
var js={path:'index',url:'',bool:false,login:{},initdata:{},openarr:{},scroll:function(){}};
var isIE=true;
if(!document.all)isIE=false;
var get=function(id){return document.getElementById(id)};
var isempt=function(an){var ob	= false;if(an==''||an==null||typeof(an)=='undefined'){ob=true;}if(typeof(an)=='number'){ob=false;}return ob;}
var strreplace=function(str){if(isempt(str))return '';return str.replace(/[ ]/gi,'').replace(/\s/gi,'')}
var strhtml=function(str){if(isempt(str))return '';return str.replace(/\</gi,'&lt;').replace(/\>/gi,'&gt;')}
var form=function(an,fna){if(!fna)fna='myform';return document[fna][an]}
var xy10=function(s){var s1=''+s+'';if(s1.length<2)s1='0'+s+'';return s1;};
js.getarr=function(caa,bo){
	var s='';
	for(var a in caa)s+=' @@ '+a+'=>'+caa[a]+'';
	if(!bo)alert(s);
	return s;
}
js.getarropen=function(caa){
	jsopenararass = caa;
	js.open('js/array.shtml');
}

js.str=function(o){
	o.value	= strreplace(o.value);
}
js.getcan = function(i,dev){
	var a = PARAMS.split('-');
	var val = '';
	if(!dev)dev='';
	if(a[i])val=a[i];
	if(!val)val=dev;
	return val;
}
js.gethost=function(){
	var url = location.href,sau='';
	try{sau = url.split('//')[1].split('/')[0];}catch(e){}
	if(sau.indexOf('demo.rockoa.com')>=0 || sau.indexOf('demo1.rockoa.com')>=0)ISDEMO=true;
	var lse = url.lastIndexOf('/');NOWURL = url.substr(0, lse+1);
	QOM		= NOWURL.replace(/\./g,'').replace(/\//g,'').replace(/\:/g,'')+'_';
	var cfrom= this.request('cfrom','',url);
	if(!cfrom)cfrom=this.getoption('CFROM');
	if(cfrom){this.setoption('CFROM', cfrom);CFROM = cfrom;}
	this.opentype = this.getoption('opentype');
	var otype= this.request('opentype','',url);
	if(otype){this.setoption('opentype', otype);this.opentype = otype;}
	this.reimapplx = 0;var llq = navigator.userAgent;if(llq.indexOf('REIMPLAT_APP')>0)this.reimapplx=1;
	return sau;
}
function winHb(){
	var winH=(!isIE)?window.innerHeight:document.documentElement.offsetHeight;
	return winH;
}
function winWb(){
	var winH=(!isIE)?window.innerWidth:document.documentElement.offsetWidth;
	return winH;
}
js.scrolla	= function(){
	var top	= $(document).scrollTop();
	js.scroll(top);
}
js.colorTorgb = function(col){
	var r=0,g=0,b=0;
	if(col.length==7){
		r = parseInt(col.substr(1,2),16);
		g = parseInt(col.substr(3,2),16);
		b = parseInt(col.substr(5,2),16);
	}
	return [r,g,b];
}
js.apptheme = function(){
	
}
js.request=function(name,dev,url){
	this.requestarr = {};
	if(!dev)dev='';
	if(!name)return dev;
	if(!url)url=location.href;
	if(url.indexOf('\?')<0)return dev;
	if(url.indexOf('#')>0)url = url.split('#')[0];
	var neurl=url.split('\?')[1];
	neurl=neurl.split('&');
	var value=dev,i,val;
	for(i=0;i<neurl.length;i++){
		val=neurl[i].split('=');
		this.requestarr[val[0]] = val[1];
		if(val[0].toLowerCase()==name.toLowerCase()){
			value=val[1];
			break;
		}
	}
	if(!value)value='';
	return value;
}
js.now=function(type,sj){
	if(!type)type='Y-m-d';
	if(type=='now')type='Y-m-d H:i:s';
	var dt,ymd,his,weekArr,Y,m,d,w,H=0,i=0,s=0,W;
	if(typeof(sj)=='string')sj=sj.replace(/\//gi,'-');
	if(/^[0-9]{4}-[0-9]{1,2}-[0-9]{1,2}/.test(sj)){
		sj=sj.split(' ');
		ymd=sj[0];
		his=sj[1];if(!his)his='00:00:00';
		ymd=ymd.split('-');
		his=his.split(':');
		H = his[0];if(his.length>1)i = his[1];if(his.length>2)s = his[2];
		dt=new Date(ymd[0],ymd[1]-1,ymd[2],H,i,s);
	}else{
		dt=(typeof(sj)=='number')?new Date(sj):new Date();
	}
	weekArr=new Array('日','一','二','三','四','五','六');
	Y=dt.getFullYear();
	m=xy10(dt.getMonth()+1);
	d=xy10(dt.getDate());
	w=dt.getDay();
	H=xy10(dt.getHours());
	i=xy10(dt.getMinutes());
	s=xy10(dt.getSeconds());
	W=weekArr[w];
	if(type=='time'){
		return dt.getTime();
	}else{
		return type.replace('Y',Y).replace('m',m).replace('d',d).replace('H',H).replace('i',i).replace('s',s).replace('w',w).replace('W',W);
	}
}
js.float=function(num,w){
	if(isNaN(num)||num==''||!num||num==null)num='0';
	num=parseFloat(num);
	if(!w&&w!=0)w=2;
	var m=num.toFixed(w);
	return m;	
}
js.splittime=0;
js.getsplit=function(){
	if(!js.servernow)return false;
	var dt=js.now('Y-m-d H:i:s');
	var d1=js.now('time',dt);	
	var d2=js.now('time',js.servernow);
	js.splittime=d1-d2;
}
js.serverdt=function(atype){
	if(!atype)atype='Y-m-d H:i:s';
	var d1=js.now('time')-js.splittime;
	var dt=js.now(atype,d1);
	return dt;
}
js.open=function(url,w,h,wina,can,wjcan){
	if(apicloud){api.openWin({url:url});return;}
	if(wina){try{var owina	= this.openarr[wina];owina.document.body;owina.focus();return owina;}catch(e){}}
	if(!w)w=750;if(!h)h=500;
	var l=(screen.width-w)*0.5,t=(screen.height-h)*0.5-50,rnd = parseInt(Math.random()*50);
	if(rnd%2==0){l=l+rnd;t=t-rnd;}else{l=l-rnd;t=t+rnd;}
	if(!can)can={};
	var s='resizable=yes,scrollbars=yes,toolbar=no,menubar=no,scrollbars=yes,resizable=yes,location=no,status=no';
	var a1={'left':''+l+'px','top':''+t+'px','width':''+w+'px','height':''+h+'px'};
	a1 = js.apply(a1,can);
	for(var o1 in a1)s+=','+o1+'='+a1[o1]+'';
	var ja=(url.indexOf('?')>=0)?'&':'?';
	if(wina)url+=''+ja+'winobj='+wina+'';
	 if(clientbool){
		if(url.substr(0,4)!='http')url=NOWURL+url;
		rockclient.rockFun("openWin",{
			url:url,
			width:w,
			height:h
		});
	}else if(typeof(nw)=='undefined'){
		var opar=window.open(url,'',s);
	}else{
		var ocsn=js.apply({'frame':true,width:w,height:h,x:l,y:t,icon:'images/logo.png'},wjcan);
		if(url.substr(0,4)!='http')url=NOWURL+url;
		var opar=nw.Window.open(url, ocsn, function(wis){
			if(wina)js.openarr[wina]=wis;
			//if(w>=1000)wis.maximize();
		});
	}
	if(wina)this.openarr[wina]=opar;
	return false;
}
js.openrun=function(wina,act, ps1, ps2){
	var owina	= this.openarr[wina];
	try{
		if(owina)owina[act](ps1,ps2);
	}catch(e){
		owina = false;
	}
	return owina;
}
js.onunload=function(){
	var a=js.openarr;
	for(var b in a){
		try{a[b].close(true)}catch(e){}
	}
	try{
		var winobj = js.request('winobj');
		if(winobj!='')opener.js.openarr[winobj]=false;
	}catch(e){}
}
js.decode=function(str){
	var arr	= {length:-1};
	try{
		arr	= new Function('return '+str+'')();
	}catch(e){}
	return arr;
}
js.email=function(str){
	if(isempt(str) || str.indexOf(' ')>-1)return false;
	if(str.indexOf('.')==-1 || str.indexOf('@')==-1)return false;
	var reg = new RegExp("[\\u4E00-\\u9FFF]+","g");
	if(reg.test(str))return false ;
	return true;
}
js.reload=function(){
	location.reload();
}
js.move=function(id,rl){
	var _left=0,_top=0,_x=0,_right=0,_y=0;
	var obj	= id;if(!rl)rl='left';
	if(typeof(id)=='string')obj=get(id);
	var _Down=function(e){
		try{
			var s='<div id="divmovetemp" style="filter:Alpha(Opacity=0);opacity:0;z-index:99999;width:100%;height:100%;position:absolute;background-color:#000000;left:0px;top:0px;cursor:move"></div>';
			$('body').prepend(s);
			_x = e.clientX;_y = e.clientY;_left=parseInt(obj.style.left);_top=parseInt(obj.style.top);_right=parseInt(obj.style.right);
			document.onselectstart=function(){return false}
		}catch(e1){}		
	}
	var _Move=function(e){
		try{
			var c=get('divmovetemp').innerHTML;
			var x = e.clientX-_x,y = e.clientY-_y;
			if(rl=='left')obj.style.left=_left+x+'px';
			if(rl=='right')obj.style.right=_right-x+'px';
			obj.style.top=_top+y+'px';
		}catch(e1){_Down(e)}
	}
	var _Up=function(){
		document.onmousemove='';
		document.onselectstart='';
		$('#divmovetemp').remove();	
	}
	document.onmousemove=_Move;
	document.onmouseup=_Up;
}
js.setdev=function(val,dev){
	var cv	= val;
	if(isempt(cv))cv=dev;
	return cv;
}
js.upload=function(call,can, glx){
	if(!call)call='';
	if(!can)can={};
	js.uploadrand	= js.now('YmdHis')+parseInt(Math.random()*999999);
	var url = 'index.php?m=upload&d=public&callback='+call+'&upkey='+js.uploadrand+'';
	for(var i in can)if(i!='title')url+='&'+i+'='+can[i]+'';
	if(glx=='url')return url;
	var s='',tit=can.title;if(!tit)tit='上传文件';
	js.tanbody('uploadwin',tit,500,300,{
		html:'<div style="height:280px;overflow:hidden;border-radius:0px 0px 5px 5px"><iframe src="" name="winiframe" width="100%" height="100%" frameborder="0"></iframe></div>',
		bbar:'none'
	});
	winiframe.location.href=url;
	return false;
}
js.locationshow=function(sid){
	var url = 'index.php?m=kaoqin&d=main&a=location&id='+sid+'';
	if(ismobile==1){js.location(url);return;}
	js.winiframe('地图位置查看', url);
	return false;
}
js.winiframemax=65;
js.winiframewidth = '900x800'; //默认的宽x高
js.winiframe=function(tit, url){
	var mxw= 900,mxh=800,tar = this.winiframewidth.split('x');
	if(tar[0])mxw=parseFloat(tar[0]);
	if(tar[1])mxh=parseFloat(tar[1]);
	var hm = winHb()-150;if(hm>mxh)hm=mxh;if(hm<400)hm=400;
	if(url.indexOf('wintype=max')>0){
		if(mxw<1000)mxw= 1000;
		hm=winHb()-js.winiframemax;
	}
	var wi = winWb()-150;if(wi>mxw)wi=mxw;if(wi<700)wi=700;
	js.tanbody('winiframe',tit,wi,410,{
		html:'<div style="height:'+hm+'px;overflow:hidden;border-radius:0px 0px 5px 5px"><iframe src="" name="openinputiframe" width="100%" height="100%" frameborder="0"></iframe></div>',
		bbar:'none'
	});
	openinputiframe.location.href=url;
	return false;	
}

//下载
js.downshow=function(id, fnun, cans){
	if(this.fileoptWin(id))return;
	if(appobj1('openfile', id))return;
	if(!isempt(fnun)){this.fileopt(id, 1);return false;}
	var url = 'api.php?m=upload&id='+id+'&a=down';
	if(cans)for(var i in cans)url+='&'+i+'='+cans[i]+'';
	this.location(url);
	return false;
}
js.downupdels=function(sid, said, o1){
	js.confirm('确定要删除此文件吗？', function(lx){
		if(lx=='yes'){
			js.downupdel(sid, said, o1);
		}
	});
}
js.downupdel=function(sid, said, o1){
	if(sid>0){
		$.get(js.getajaxurl('delfile','upload','public',{id:sid}));
	}
	if(o1)$(o1).parent().remove();
	var o = $('#view_'+said+'');
	var to= $('#count_'+said+'');
	var o1 = o.find('span'),s1='';
	for(i=0;i<o1.length;i++)$(o1[i]).html(''+(i+1));
	to.html('');
	if(i>0)to.html('<font style="font-size:11px" color="#555555">文件:'+i+'</font>');
	o1 = o.find('font');
	for(i=0;i<o1.length;i++)s1+=','+$(o1[i]).html();
	if(s1!='')s1=s1.substr(1);
	$('#'+said+'-inputEl').val(s1);
	$('#fileid_'+said+'').val(s1);
}
js.downupshow=function(a, showid, nbj){
	var s = '',i=0,s1='',fis,ofisd=',doc,docx,xls,xlsx,ppt,pptx,';
	var o = $('#view_'+showid+'');
	for(i=0; i<a.length; i++){
		fis= 'web/images/fileicons/'+js.filelxext(a[i].fileext)+'.gif';
		if(js.isimg(a[i].fileext) && !isempt(a[i].thumbpath))fis=a[i].thumbpath;
		s='<div onmouseover="this.style.backgroundColor=\'#f1f1f1\'" onmouseout="this.style.backgroundColor=\'\'" style="padding:4px 5px;border-bottom:1px #eeeeee solid;font-size:14px"><span>'+(i+1)+'</span><font style="display:none">'+a[i].id+'</font>、<img src="'+fis+'" align="absmiddle" height="20" width="20"> '+a[i].filename+' ('+a[i].filesizecn+')';
		s+=' <a class="a" temp="yula" onclick="return js.fileopt('+a[i].id+',1)" href="javascript:;">下载</a>';
		s+=' <a class="a" temp="yula" onclick="return js.fileopt('+a[i].id+',0)" href="javascript:;">预览</a>';
		if(ofisd.indexOf(','+a[i].fileext+',')>=0)s+=' <a class="a" temp="dela" onclick="return js.fileopt('+a[i].id+',2)" href="javascript:;">编辑</a>';
		s+=' <a class="a" temp="dela" onclick="return js.downupdels('+a[i].id+',\''+showid+'\', this)" href="javascript:;">×</a>';
		s+='</div>';
		o.append(s);
	}
	js.downupdel(0, showid, false);
	if(nbj)o.find('[temp="dela"]').remove();//禁止编辑
}
js.loading=function(txt){
	js.msg('wait',txt);
}
js.msgerror=function(txt){
	js.msg('msg',txt);
}
js.unloading=function(){js.msg();}
//文件操作id文件id,lx0预览,1下载,2编辑
js.fileopt=function(id,lx){
	if(!lx)lx=0;
	if(ismobile==1 && lx==1 && this.fileoptWin(id))return;
	js.loading('加载中...');
	var gurl = 'api.php?a=fileinfo&m=upload&id='+id+'&type='+lx+'&ismobile='+ismobile+'';
	$.ajax({
		type:'get',url:gurl,dataType:'json',
		success:function(ret){
			js.unloading();
			if(ret.success){
				var da = ret.data;
				var ext= da.fileext;
				var url= da.url;
				if(da.type==2)js.importplugin('rockoffice',da.editwsinfo);
				if(ismobile==1){
					if(da.type==0 && !da.isview && appobj1('openfile', id))return; //不能预览就用app打开
					if(da.type==0 && !da.isview && js.fileoptWin(id))return; //不能预览就用app打开
					if(da.type==1 && appobj1('openfile', id))return; //下载用app的
					if(da.type==0 && !js.isimg(ext)){
						if(appobj1('openWindow', url))return;
						if(js.appwin('预览',url))return;
					}
				}
				if(da.type==1){js.location(url);return;}//下载直接跳转
				if(js.isimg(ext)){
					$.imgview({'url':url,'ismobile':ismobile==1,'downbool':false});
				}else if(ext=='rockedit'){
					if(ismobile==0){
						js.open(url,screen.width-200,screen.height-200);
					}else{
						js.location(url);return;
						var str = '<div id="rockeditdiv" style="background:white;position:fixed;z-index:99;top:0px;left:0px;width:100%;height:'+winHb()+'px"><iframe src="'+url+'" width="100%" height="100%" frameborder="0"></iframe></div>';
						$('body').append(str);
						js.location('#rockedit');
						window.onhashchange=function(){
							var has = location.hash;
							if(has.indexOf('#rockedit')==-1)$('#rockeditdiv').remove();
						}
					}
				}else if(ext=='rockoffice'){
					js.sendeditoffices(url);
				}else{
					url+='&wintype=max';
					if(ismobile==0){
						if(!nwjsgui){
							js.winiframe(da.filename,url);
						}else{
							js.open(url, 1000,500);
						}
					}else{
						js.location(url);
					}
				}
			}else{
				js.msgerror(ret.msg);
			}
		},
		error:function(e){
			js.unloading();
			js.msg('msg','处理出错:'+e.responseText+'');
		}
	});
}


//文件预览
js.yulanfile=function(id, ext,pts, sne, fnun,isxq){
	if(!isempt(fnun)){this.fileopt(id, 0);return false;}
	var url = 'index.php?m=public&a=fileviewer&id='+id+'&wintype=max';
	if(pts!=''&&js.isimg(ext)){
		$.imgview({'url':pts,'ismobile':ismobile==1,'downbool':false});
		$.get('api.php?m=upload&a=logs&fileid='+id+'&type=0');
		return false;
	}
	if(ismobile==1){
		var docsx = ',doc,docx,ppt,pptx,xls,xlsx,pdf,txt,html,';
		if(docsx.indexOf(','+ext+',')==-1)if(appobj1('openfile', id))return;
		if(appobj1('openWindow', url))return;
		if(js.appwin('预览',url))return;
		js.location(url);
	}else{
		if(!sne)sne='文件预览';
		if(isxq=='xq'){js.open(url,screen.width-200,screen.height-200)}else{js.winiframe(sne,url);}
	}
	return false;
}
js.apiurl = function(m,a,cans){
	var url='api.php?m='+m+'&a='+a+'';
	url+='&cfrom='+CFROM+'';
	if(!cans)cans={};
	for(var i in cans)url+='&'+i+'='+cans[i]+'';
	return url;
}
js.getajaxurl=function(a,m,d,can){
	if(!can)can={};
	if(!m)m=MODE;
	if(!d)d=DIR;
	if(d=='null')d='';
	var jga	= a.substr(0,1);
	if(jga=='@')a = a.substr(1);
	var url	= ''+this.path+'.php?a='+a+'&m='+m+'&d='+d+'';
	for(var c in can)url+='&'+c+'='+can[c]+'';
	if(jga!='@')url+='&ajaxbool=true';	
	url+='&rnd='+parseInt(Math.random()*999999)+'';	
	return url;
}
js.formatsize=function(size){
	var arr = new Array('Byte', 'KB', 'MB', 'GB', 'TB', 'PB');
	var e	= Math.floor(Math.log(size)/Math.log(1024));
	var fs	= size/Math.pow(1024,Math.floor(e));
	return js.float(fs)+' '+arr[e];
}
js.getselectval=function(o){
	var str='';
	for(var i=0;i<o.length;i++){
		if(o[i].selected){
			str+=','+o[i].value+'';
		}
	}
	if(str!='')str=str.substr(1);
	return str;
}
js.setselectval=function(o,val){
	var str='',vals=','+val+',';
	for(var i=0;i<o.length;i++){
		if(vals.indexOf(','+o[i].value+',')>-1){
			o[i].selected=true;
		}
	}
}
js.getformdata=function(nas){
	var da	={},ona='',o,type,val,na,i,obj;
	if(!nas)nas='myform';
	obj	= document[nas];
	for(i=0;i<obj.length;i++){
		o 	 = obj[i];type = o.type,val = o.value,na = o.name;
		if(!na)continue;
		if(type=='checkbox'){
			val	= '0';
			if(o.checked)val='1';
			da[na]	= val;
		}else if(type=='radio'){
			if(o.checked)da[na]	= val;					
		}else{
			da[na] = val;
		}
		if(na.indexOf('[]')>-1){
			if(ona.indexOf(na)<0)ona+=','+na+'';
		}
	}
	if(ona != ''){
		var onas = ona.split(',');
		for(i=1; i<onas.length; i++){
			da[onas[i].replace('[]','')] = js.getchecked(onas[i]);
		}
	}
	return da;
}
js.getdata = function(na,da){
	if(!da)da={};
	var obj	= $('#'+na+'');
	var inp	= obj.find('input,select');
	for(var i=0;i<inp.length;i++){
		var type	= inp[i].type;
		var val		= inp[i].value;
		if(type=='checkbox'){
			val	= '0';
			if(inp[i].checked)val='1';
		}
		var ad1	= inp[i].name;
		if(!ad1)ad1 = inp[i].id;
		da[ad1]	= val;
	}
	return da;
}
js.selall = function(o,na,bh){
	var i,oi1;
	if(bh){
		o1=$("input[name^='"+na+"']");
	}else{
		o1=$("input[name='"+na+"']");
	}
	for(i=0;i<o1.length;i++){
		if(!o1[i].disabled)o1[i].checked = o.checked;
	}
}
js.getchecked=function(na,bh){
	var s = '';
	var o1;
	if(bh){
		o1=$("input[name^='"+na+"']");
	}else{
		o1=$("input[name='"+na+"']");
	}
	for(var i=0;i<o1.length;i++){
		if(o1[i].checked && !o1[i].disabled)s+=','+o1[i].value+'';
	}
	if(s!='')s=s.substr(1);
	return s;
}
js.cookie=function(name){
	var str=document.cookie,cda,val='',arr,i;
	if(str.length<=0)return '';
	arr=str.split('; ');
	for(i=0;i<arr.length;i++){
		cda=arr[i].split('=');
		if(name.toLowerCase()==cda[0].toLowerCase()){
			val=cda[1];
			break;
		}
	}
	if(!val)val='';
	return val;
}
js.savecookie=function(name,value,d){
	var expires = new Date();
	if(!d)d=365;
	if(!value)d=-10;
	expires.setTime(expires.getTime()+d*24*60*60*1000);
	var str=''+name+'='+value+';expires='+expires.toGMTString()+';path=/;SameSite=Strict';
	document.cookie = str;
}
js.backtop=function(ci){
	if(!ci)ci=0;
	$('body,html').animate({scrollTop:ci});
	return false;
}
js.backto = function(oid){
	if(!get(oid))return;
	var of	= $('#'+oid+'').offset();
	this.backtop(of.top);
	return false;
}
js.applyIf=function(a,b){
	if(!a)a={};
	if(!b)b={};
	for(var c in b)if(typeof(a[c])=='undefined')a[c]=b[c];
	return a;
}
js.apply=function(a,b){
	if(!a)a={};
	if(!b)b={};
	for(var c in b)a[c]=b[c];
	return a;
}
js.tanbody=function(act,title,w,h,can1){
	var H	= (document.body.scrollHeight<winHb())?winHb()-5:document.body.scrollHeight;
	var W	= document.documentElement.scrollWidth+document.body.scrollLeft;
	if(!this.tanbodyindex)this.tanbodyindex=80;
	this.tanbodyindex++;
	var l=(winWb()-w)*0.5,t=(winHb()-h-20)*0.5;
	var s = '',mid	= ''+act+'_main',i,d;
	var can	= js.applyIf(can1,{html:'',btn:[],bodystyle:'',showfun:function(){}});
	if(w>winWb())w=winWb()-50;
	var s = '<div id="'+mid+'" style="position:fixed;background-color:#ffffff;left:'+l+'px;width:'+w+'px;top:'+t+'px;box-shadow:0px 0px 10px rgba(0,0,0,0.3);border-radius:6px"><div style="background:var(--main-bgcolor);border-radius:5px;">';
	s+='	<div style="-moz-user-select:none;-webkit-user-select:none;user-select:none;border-bottom:var(--border)">';
	s+='		<table border="0" width="100%" style="background:none" cellspacing="0" cellpadding="0"><tr>';
	s+='			<td height="50" style="font-size:16px; font-weight:bold;padding-left:10px" width="100%" onmousedown="js.move(\''+mid+'\')" id="'+act+'_title"  class="zhu">'+title+'</td>';
	s+='			<td><div  id="'+act+'_spancancel1" style="padding:0px 8px;height:50px;line-height:45px;overflow:hidden;cursor:pointer;" onclick="js.tanclose(\''+act+'\')">✖</div></td>';
	s+='		</tr></table>';
	s+='	</div>';
	s+='	<div id="'+act+'_body" style="'+can.bodystyle+'">'+can.html+'</div>';
	s+='	<div id="'+act+'_bbar" style="overflow:hidden;padding:12px 10px;background:rgba(0,0,0,0.05);border-radius:0px 0px 5px 5px" align="right"><span id="msgview_'+act+'"></span>';
	var hasCancelButton = false;
	for(i=0; i<can.btn.length; i++){
		d = can.btn[i];
		if(!d.bgcolor)d.bgcolor='';
		s+='<button type="button" oi="'+i+'" style="border-radius:5px;padding:8px 15px;margin-left:10px;background:'+d.bgcolor+'" id="'+act+'_btn'+i+'" class="webbtn">'+d.text+'</button>';
		if(d.text === '取消') hasCancelButton = true;
	}
	if(!hasCancelButton){
		s+='<button type="button" id="'+act+'_spancancel_default" onclick="js.tanclose(\''+act+'\')" style="border-radius:5px;padding:8px 15px;background:rgba(0,0,0,0.5);margin-left:10px" class="webbtn">取消</button>';
	}
	s+='	</div>';
	s+='</div></div>';
	var str = '<div id="amain_'+act+'" tanbodynew="'+act+'" oncontextmenu="return false" style="position:absolute;height:'+H+'px;width:'+W+'px;background:rgba(0,0,0,0.3);z-index:'+this.tanbodyindex+';left:0px;top:0px">'+s+'</div>';
	$('body').append(str);
	if(can.closed=='none'){
		$('#'+act+'_spancancel').remove();
		$('#'+act+'_spancancel1').remove();
	}
	if(can.bbar=='none'){
		$('#'+act+'_bbar').remove();
		//$('#'+mid+'').append('<div style="height:5px;overflow:hidden;border-radius:0px 0px 5px 5px"></div>');
	}
	this.resizetan(act);
	can.showfun(act);
}
js.resizetan=function(act){
	var mid	= ''+act+'_main';
	var o1  = $('#'+mid+'');
	var h1 = o1.height();
	var w1 = o1.width();	
	var l=(winWb()-w1)*0.5,t=(winHb()-h1-20)*0.5;if(t<0)t=5;
	o1.css({'left':''+l+'px','top':''+t+'px'});
}
js.tanclose=function(act){
	$('#amain_'+act+'').remove();
}
js.xpbodysplit = 0;
js.xpbody=function(act,type){
	if(type=='none'){
		$("div[xpbody='"+act+"']").remove();
		if(!get('xpbg_bodydds'))$('div[tanbody]').remove();
		return;
	}
	if(get('xpbg_bodydds'))return false;
	var H	= (document.body.scrollHeight<winHb())?winHb()-this.xpbodysplit-5:document.body.scrollHeight-this.xpbodysplit*2;
	var W	= document.documentElement.scrollWidth+document.body.scrollLeft-this.xpbodysplit*2;
	
	var bs='<div id="xpbg_bodydds" xpbody="'+act+'" oncontextmenu="return false" style="position:absolute;display:none;width:'+W+'px;height:'+H+'px;filter:Alpha(opacity=30);opacity:0.3;left:'+this.xpbodysplit+'px;top:'+this.xpbodysplit+'px;background-color:#000000;z-index:80"></div>';
	$('body').prepend(bs);	
	$('#xpbg_bodydds').fadeIn(300);
}
js.focusval	= '0';
js.number=function(obj){
	val=strreplace(obj.value);
	if(!val){
		obj.value=js.focusval;
		return false;
	}
	if(isNaN(val)){
		js.msg('msg','输入的不是数字');
		obj.value=js.focusval;
		obj.focus();
	}else{
		var o1 = $(obj);
		var min= o1.attr('minvalue');
		if(isempt(min))min= o1.attr('min');
		if(min && parseFloat(val)<parseFloat(min))val=min;
		var max= o1.attr('maxvalue');
		if(isempt(max))max= o1.attr('max');
		if(max && parseFloat(val)>parseFloat(max))val=max;
		obj.value=val;
	}
}
js.setmsg=function(txt,col,ids){
	if(!ids)ids='msgview';
	$('#'+ids+'').html(js.getmsg(txt,col));
}
js.getmsg  = function(txt,col){
	if(!col)col='red';
	var s	= '';
	if(!txt)txt='';
	if(txt.indexOf('...')>0){
		s=''+this.ling(16)+' ';
		col = '#ff6600';
	}	
	s+='<span style="color:'+col+'">'+txt+'</span>';
	if(!txt)s='';
	return s;
}
js.setcopy	= function(txt,nts){
	if(!txt)return;
	var str='<div id="copydiv" style="position:absolute;z-index:0;bottom:0px;right:0px;height:1px;overflow:hidden"><textarea id="copytext">'+txt+'</textarea></div>';
	$('body').append(str);
	get('copytext').select();
	document.execCommand('Copy');
	if(!nts)js.msg('success','复制成功');
	$('#copydiv').remove();
	return false;
}
js.getcopy = function(){
	var txt	= js.cookie('copy_text');
	txt	= unescape(txt);
	return txt;
}
js.chao=function(obj,shuzi,span,guo){
	var cont=(guo)?strreplace(obj.value):obj.value;
    if (cont.length>shuzi){
		alert("您输入的字符超过"+shuzi+"个字符\n\n将被截掉"+(cont.length-shuzi)+"个字符！");
		cont=cont.substring(0,shuzi);
		obj.value=cont;
	}
	if(guo)obj.value=cont;
	if(span)get(span).innerHTML=obj.value.length;
}
js.debug	= function(s){
	if(typeof(console)!='object')return;
	console.error(s);
}
js.alert = function(txt,tit,fun){
	js.confirm(txt, fun, '', tit, 2, '');
}
js.wait	= function(txt,tit,fun){
	js.confirm(txt, fun, '', tit, 3, '');
}
js.alertclose=function(){
	js.tanclose('confirm');
}
js.tanstyle = 0;
js.confirm	= function(txt,fun, tcls, tis, lx,ostr,bstr){
	if(!lx)lx=0;js.alertclose();
	var h = '<div style="padding:20px;line-height:30px" align="center">',w=320;
	if(lx==1)w= 350;
	if(w>winWb())w=winWb()-10;
	if(lx==1){
		if(!tcls)tcls='';if(!ostr)ostr='';if(!bstr)bstr='';
		h='<div style="padding:10px;" align="center">'+ostr+'';
		h+='<div align="left" style="padding-left:10px">'+txt+'</div>';
		h+='<div ><textarea class="input form-control" id="confirm_input" style="width:'+(w-40)+'px;height:60px;border-radius:5px">'+tcls+'</textarea></div>'+bstr+'';
	}else if(lx==3){
		h+='<img src="images/mloading.gif" height="32" width="32" align="absmiddle">&nbsp; '+txt+'';
	}else{
		h+=''+txt+'';
	}
	h+='</div>';
	if(!tcls)tcls='danger';if(lx==1)tcls='info';
	if(!tis)tis='<i class="icon-question-sign"></i>&nbsp;系统提示';
	var btn=[{text:'确定'}];
	if(lx<2)btn.push({text:'取消',bgcolor:'gray'});
	js.tanbody('confirm', tis, w, 200,{closed:'none',bbar:'',html:h,titlecls:tcls,btn:btn});
	function backl(jg){
		var val=$('#confirm_input').val();
		if(val==null)val='';
		if(typeof(fun)=='function'){
			var cbo = fun(jg, val);
			if(cbo)return false;
		}
		js.alertclose();
		return false;
	}
	$('#confirm_btn0').click(function(){backl('yes')});
	if(get('confirm_btn1'))$('#confirm_btn1').click(function(){backl('no')});
	if(lx==1)get('confirm_input').focus();
}
js.prompt = function(tit,txt,fun, msg, ostr,bstr){
	js.confirm(txt, fun, msg, tit, 1, ostr,bstr);
}
js.msg = function(lx, txt,sj){
	clearTimeout(this.msgshowtime);
	if(typeof(sj)=='undefined')sj=5;
	$('#msgshowdivla').remove();
	if(lx == 'none' || !lx){
		return;
	}
	if(lx == 'wait'){
		txt	= ''+this.ling(14)+' '+txt;
		sj	= 60;
	}
	if(lx=='msg')txt='<font color=red>'+txt+'</font>';var t=10;
	if(get('header_title'))t+=50;
	var s = '<div onclick="$(this).remove()" id="msgshowdivla" style="position:fixed;top:'+t+'px;z-index:200;" align="center"><div style="padding:8px 20px;background:rgba(0,0,0,0.7);color:white;font-size:16px;border-radius:5px">'+txt+'</div></div>';
	$('body').append(s);
	var w=$('#msgshowdivla').width(),l=(winWb()-w)*0.5;
	$('#msgshowdivla').css('left',''+l+'px');
	if(sj>0)this.msgshowtime= setTimeout("$('#msgshowdivla').remove()",sj*1000);	
}
js.repempt=function(stt,v){
	var s = stt;
	if(isempt(s))s=v;
	return s;
}
js.getrand=function(){
	var r;
	r = ''+new Date().getTime()+'';
	r+='_'+parseInt(Math.random()*9999)+'';
	return r;
}
js.arraystr=function(str){
	if(!str)str='1|是,0|否';
	var s = str.split(','),
		d = [],i,s1,nv,vv;
	for(i=0; i<s.length; i++){
		s1 = s[i].split('|');
		nv = s1[0];
		vv = nv;
		if(s1.length>1)nv=s1[1];
		d.push([vv,nv]);
	}	
	return d;
}
js._bodyclick = {};
js.downbody=function(o1, e){
	this.allparent = '';
	this.getparenta($(e.target),0);
	var a,s = this.allparent,a1;
	for(a in js._bodyclick){
		a1 = js._bodyclick[a];
		if(s.indexOf(a)<0){
			if(a1.type=='hide'){
				$('#'+a1.objid+'').hide();
			}else{
				$('#'+a1.objid+'').remove();
			}
			if(a1.fun)a1['fun']();
		}
	}
	return true;
}
js.addbody = function(num, type,objid, fun1){
	js._bodyclick[num] = {type:type,objid:objid,fun:fun1};
}
js.getparenta=function(o, oi){
	try{
	if(o[0].nodeName.toUpperCase()=='BODY')return;}catch(e){return;}
	var id = o.attr('id');
	if(!isempt(id)){
		this.allparent+=','+id;
	}
	this.getparenta(o.parent(), oi+1);
}
js.ajaxwurbo = false;
js.ajaxbool = false;
js.ajax = function(url,da,fun,type,efun, tsar){
	if(js.ajaxbool && !js.ajaxwurbo)return;
	if(!da)da={};if(!type)type='get';if(!tsar)tsar='';tsar=tsar.split(',');
	if(typeof(fun)!='function')fun=function(){};
	if(typeof(efun)!='function')efun=function(){};
	var atyp = type.split(','),dtyp='';type=atyp[0];
	if(atyp[1])dtyp=atyp[1];
	js.ajaxbool=true;if(tsar[0])js.msg('wait', tsar[0]);
	var ajaxcan={
		type:type,
		data:da,url:url,
		success:function(str){
			js.ajaxbool=false;
			try{
				if(tsar[1])js.msg('success', tsar[1]);
				fun(str);
			}catch(e){
				js.msg('msg', str);
				js.debug(e);
			}
		},error:function(e){
			js.ajaxbool=false;
			js.msg('msg','处理出错:'+e.responseText+'');
			efun(e.responseText);
		}
	};
	if(dtyp)ajaxcan.dataType=dtyp;
	$.ajax(ajaxcan);
}
js.setoption=function(k,v,qzb){
	if(!qzb)k=QOM+k;
	try{
		if(isempt(v)){
			localStorage.removeItem(k);
		}else{
			localStorage.setItem(k, escape(v));
		}
	}catch(e){
		js.savecookie(k,escape(v));
	}
	return true;
}
js.getoption=function(k,dev, qzb){
	var s = '';
	if(!qzb)k=QOM+k;
	try{s = localStorage.getItem(k);}catch(e){s=js.cookie(k);}
	if(s)s=unescape(s);
	if(isempt(dev))dev='';
	if(isempt(s))s=dev;
	return s;
}
js.location = function(url){
	location.href = url;
}
js.backla=function(msg){
	if(msg)if(!confirm(msg))return;
	try{api.closeWin();}catch(e){}
}
js.isimg = function(lx){
	var ftype 	= '|png|jpg|bmp|gif|jpeg|';
	var bo		= false;
	if(ftype.indexOf('|'+lx+'|')>-1)bo=true;
	return bo;
}
js.changeuser_before=function(na){}
js.changeuser_after=function(){}
js.changeuser=function(na, lx, tits,ocans){
	var h = winHb()-70,w=350;if(!ocans)ocans={};
	if(h>400)h=400;if(!tits)tits='请选择...';
	var nibo = ((lx=='changedeptusercheck'||lx=='deptusercheck') && ismobile==0);
	if(nibo)w=650;
	var formname = '';
	var can = {
		'changetype': lx,
		'showview' 	: 'showuserssvie',
		'titlebool'	:false,
		'changevalue':'',
		'changerange':'', //选择范围
		'oncancel'	:function(){
			js.tanclose('changeaction');
		},
		'onselect':function(sna,sid){
			js.changeuser_after(this.formname,this,sna,sid);
		}
	};
	if(na){
		can.idobj = get(na+'_id');
		can.nameobj = get(na);
		if(can.nameobj)formname = can.nameobj.name;
	}
	
	can.formname= formname;
	var bcar = js.changeuser_before(formname,1),i;
	for(i in ocans)can[i]=ocans[i];
	if(typeof(bcar)=='string' && bcar){js.msg('msg', bcar);return;}
	if(typeof(bcar)=='object')for(i in bcar)can[i]=bcar[i];
	
	js.tanbody('changeaction',tits,w,h,{
		html:'<div id="showuserssvie" style="height:'+h+'px;border-radius:0px 0px 5px 5px"><iframe src="" name="winiframe" width="100%" height="100%" frameborder="0"></iframe></div>',
		bbar:'none'
	});
	
	if(nibo){
		if(can.idobj)can.changevalue=can.idobj.value;
		changcallback=function(sna,sid){
			if(can.idobj)can.idobj.value = sid;
			if(can.nameobj){
				can.nameobj.value = sna;
				can.nameobj.focus();
			}
			js.changeuser_after(formname, can, sna,sid);
			js.tanclose('changeaction');
			if(can.callback)can.callback(sna,sid);
		}
		var url = 'index.php?d=system&m=dept&changetype='+lx+'&changevalue='+can.changevalue+'&callback=changcallback&changerange='+can.changerange+'';
		winiframe.location.href = url;
	}else{
		$('#showuserssvie').chnageuser(can);
	}
	return false;
}
js.back=function(){
	if(isapp){
		plus.webview.currentWebview().close('auto');
	}else if(apicloud){
		if(api.historyBack){
			api.historyBack({},function(ret){if(!ret.status)api.closeWin();});
		}else{
			api.closeWin();
		}
	}else{
		history.back();
	}
}
js.changeclear=function(na){
	var fne  = get(na).name;
	var bcar = js.changeuser_before(fne,0);
	if(typeof(bcar)=='string' && bcar){js.msg('msg', bcar);return;}
	get(na).value='';
	get(na+'_id').value='';
	get(na).focus();
	js.changeuser_after(fne,{nameobj:get(na),idobj:get(na+'_id')},'','');
}
js.changedate=function(o1,id,v){
	if(!v)v='date';
	$(o1).rockdatepicker({initshow:true,view:v,inputid:id});
}
js.fileall=',aac,ace,ai,ain,amr,app,arj,asf,asp,aspx,av,avi,bin,bmp,cab,cad,cat,cdr,chm,com,css,cur,dat,db,dll,dmv,doc,docx,dot,dps,dpt,dwg,dxf,emf,eps,et,ett,exe,fla,ftp,gif,hlp,htm,html,icl,ico,img,inf,ini,iso,jpeg,jpg,js,m3u,max,mdb,mde,mht,mid,midi,mov,mp3,mp4,mpeg,mpg,msi,nrg,ocx,ogg,ogm,pdf,php,png,pot,ppt,pptx,psd,pub,qt,ra,ram,rar,rm,rmvb,rtf,swf,tar,tif,tiff,txt,url,vbs,vsd,vss,vst,wav,wave,wm,wma,wmd,wmf,wmv,wps,wpt,wz,xls,xlsx,xlt,xml,zip,';
js.filelxext = function(lx){
	if(js.fileall.indexOf(','+lx+',')<0)lx='wz';
	return lx;
}
js.datechange=function(o1,lx,isbo){
	if(!$(o1).rockdatepicker && !isbo){
		js.importcss('mode/plugin/css/jquery-rockdatepicker.css');
		js.importjs('mode/plugin/jquery-rockdatepicker.js', function(){
			js.datechange(o1,lx, true);
		});
		return;
	}
	if(!lx)lx='date';
	$(o1).rockdatepicker({'view':lx,'initshow':true});
	return false;
}
js.selectdate=function(o1,inp,lx){
	if(!lx)lx='date';
	$(o1).rockdatepicker({'view':lx,'initshow':true,'inputid':inp});
	return false;
}
js.importjs=function(url,fun,dzc){
	var sid = jm.base64encode(url);
	if(!fun)fun=function(){};
	if(get(sid)){fun();return;}
	var scr = document.createElement('script');if(!dzc)dzc='';
	scr.src = url+dzc;
	scr.id 	= sid;
	if(isIE){
		scr.onreadystatechange = function(){
			if(this.readyState=='loaded' || this.readyState=='complete'){fun(this);}
		}
	}else{
		scr.onload = function(){fun(this);}
	}
	document.getElementsByTagName('head')[0].appendChild(scr);
	return false;	
}
js.importplugin = function(na, cans){
	var dz = 'mode/plugin/jquery-'+na+'.js';
	this.importjs(dz, function(){
		js['plugin_'+na+''](cans);
	},'?'+this.getrand()+'');
}
js.importcss = function(url){
	var sid  = jm.base64encode(url);
	if(get(sid))return;
	var scr  = document.createElement('link');
	scr.href = url;
	scr.id   = sid;
	scr.rel  = 'stylesheet';
	document.getElementsByTagName('head')[0].appendChild(scr);
}

js.replacecn=function(o1){
	var  val = strreplace(o1.value);
	val		 = val.replace(/[\u4e00-\u9fa5]/g,'');
	o1.value =val;
}

js.setselectdata = function(o, data, vfs, devs){
	var i,ty = data,sv,str='';
	if(!data)return;	
	if(!vfs)vfs='name';	
	if(typeof(devs)=='undefined')devs='&nbsp;';
	for(i=0;i<ty.length;i++){
		if(ty[i].optgroup){
			if(ty[i].optgroup=='start')str+='<optgroup label="'+ty[i].name+'">';
			if(ty[i].optgroup=='end')str+='</optgroup>';
		}else{
			str+='<option value="'+ty[i][vfs]+'">'+ty[i].name+'</option>';
		}
	}
	$(o).append(str);
}
//是否app上接口
function appobj1(act, can1){
	var bo = false;
	if(typeof(appxinhu)=='object'){
		if(appxinhu[act]){
			try{appxinhu[act](can1);bo = true;}catch(e){}
		}
	}
	return bo;
}
//向PC客户端发送命令
js.cliendsend=function(at, cans, fun,ferr){
	var dk  = '2829';
	if(at=='rockoffice')dk='2827';
	var url = unescape('http%3A//127.0.0.1%3A'+dk+'/%3Fatype');
	if(!cans)cans={};if(!fun)fun=function(){};if(!ferr)ferr=function(){return false;}
	url+='='+at+'&callback=?';
	var llq = navigator.userAgent.toLowerCase();
	if(llq.indexOf('windows nt 5')>0 && dk=='2829'){
		if(!ferr())js.msg('msg','XP的系统不支持哦');
		return;
	}
	var i,v,bo=typeof(jm);
	for(i in cans){
		v = cans[i];
		if(bo=='object')v='base64'+jm.base64encode(v)+'';
		url+='&'+i+'='+v+'';
	}
	var timeoout = setTimeout(function(){if(!ferr())js.msg('msg','无法使用，可能没有登录REIM客户端');},500);
	$.getJSON(url, function(ret){clearTimeout(timeoout);fun(ret);});
}

//发送文档编辑
js.sendeditoffice=function(id,lx){
	if(!lx)lx='0';
	this.ajax('api.php?m=upload&a=rockofficeedit',{id:id,lx:lx},function(ret){
		if(ret.success){
			js.sendeditoffices(ret.data);
		}else{
			js.msg('msg', ret.msg);
		}
	},'get,json');
}
js.sendeditoffices=function(str){
	js.cliendsend('rockoffice',{paramsstr:str},false,function(){js.msg('msg','无法使用，可能没有安装在线编辑插件');return true;});
}

js.ontabsclicks=function(){};
js.inittabs=function(){
	$('.r-tabs div').click(function(){
		js.tabsclicks(this);
	});
}
js.tabsclicks=function(o1){
	var o = $(o1);
	var tid= o.parent().attr('tabid');
	$('.r-tabs[tabid="'+tid+'"] div').removeClass('active');
	$('[tabitem][tabid="'+tid+'"]').hide();
	var ind = o.attr('index');
	o.addClass('active');
	var ho = $('[tabitem='+ind+'][tabid="'+tid+'"]');
	ho.show();
	this.ontabsclicks(ind, tid, o, ho);
}
js.changdu=function(o){
	var max = $(o).attr('maxlength');
	if(max>0){
		var zlen = o.value.length;
		if(zlen>parseFloat(max))js.alert('录入数据长度'+zlen+'超过'+max+'总长度，其余会被截取掉');
	}
}
js.showmap=function(str){
	// 兼容旧版本的地图显示函数
	// 参数格式通常为: "纬度,经度,其他参数,地点名称"
	try {
		// 优先使用新的地图导航功能
		if (js.mapNavigation && typeof js.mapNavigation === 'function') {
			console.log('使用新版地图导航功能:', str);
			js.mapNavigation(str);
			return;
		}
	} catch (error) {
		console.error('新版地图导航调用失败，使用旧版方式:', error);
	}
	
	// 备用方案：使用原有的内部页面方式
	try {
		var url = 'index.php?d=main&m=kaoqin&a=location&info='+jm.base64encode(str)+'';
		js.location(url);
	} catch (error) {
		console.error('地图显示失败:', error);
		alert('无法显示地图，请检查位置信息');
	}
}


js.setapptitle=function(tit){
	if(!apicloud)return;
	var svst = sessionStorage.getItem('apiwinname');
	if(svst){
		if(!tit)tit=document.title;
		//js.sendevent('title',svst,{title:tit})
	}
}
js.fileoptWin=function(id){
	var otype = this.opentype,ourl='widget://index.html';
	if(otype && otype!='nei')ourl=jm.base64decode(otype);
	var bstr=jm.base64encode('{"name":"文件","fileid":"'+id+'","url":"fileopen","fileext":""}');
	var url = ''+ourl+'?bstr='+bstr+'';
	return this.apiopenWin(url);
}
js.apiopenWin=function(url){
	if(!apicloud)return false;
	api.openWin({name:'url'+js.getrand(),url: url,bounces:false,softInputBarEnabled:false,slidBackEnabled:true,vScrollBarEnabled:false,hScrollBarEnabled:false,allowEdit:false,progress:{type:'',title:'', text:'',   color:''}});	
	
	return true;
}
js.appwin=function(na,dz){	
	var otype = this.opentype,ourl='widget://index.html';
	if(otype && otype!='nei')ourl=jm.base64decode(otype);
	if(dz.substr(0,4)!='http')dz=NOWURL+dz;
	var jg  = (dz.indexOf('?')==-1)?'?':'&';
	if(!na)na='&nbsp;';
	var dizhi 	= ''+dz+''+jg+'hideheader=true';
	if(apicloud && api.openWindcloud){
		api.openWindcloud({name:na,url:dz})
		return true;
	}
	var bstr	=jm.base64encode('{"name":"'+na+'","url":"openurl","dizhi":"'+dizhi+'"}');
	var url 	= ''+ourl+'?bstr='+bstr+'';
	return this.apiopenWin(url);	
}
js.sendevent=function(typ,na,d){
	if(!apicloud)return false;
	if(!d)d={};
	d.opttype=typ;
	if(!na)na='xinhuhome';
	if(api.sendEvent)api.sendEvent({name: na,stype:typ,extra:d});
}

function lang(ky){
	return ky;
}

js.ling = function(w){
	var sve = 'style="height:'+w+'px;width:'+w+'px"';
	if(!w)sve='';
	return '<i '+sve+' class="rock-loading"></i>';
}

js.chajian = function(type, cans){
	if(!$[type]){
		js.importjs('mode/plugin/jquery-'+type+'.js?'+js.getrand()+'', function(){$[type](cans);});
	}else{
		$[type](cans);
	}
}

/**
 * 初始化地图导航功能
 * @param {Object|string} config - 配置参数或坐标字符串
 * 当传入字符串时，格式为：'纬度,经度,缩放级别,地点名称'，例如：'23.45234,116.731323,17,广东省汕头市龙湖区新北路与青年路交叉口东北20米东杰快餐'
 * @param {Object} config.location - 位置信息，默认为天安门坐标
 * @param {string} config.location.name - 位置名称
 * @param {number} config.location.latitude - 纬度（高德坐标系）
 * @param {number} config.location.longitude - 经度（高德坐标系）
 * @param {string} config.navButtonId - 导航按钮ID，默认为'navButton'
 * @param {string} config.mapSelectionId - 地图选择容器ID，默认为'mapSelection'
 * @param {string} config.closeMapBtnId - 关闭按钮ID，默认为'closeMapBtn'
 * @param {string} config.containerId - 容器ID，默认为'mapNavContainer'
 * @param {boolean} config.injectStyles - 是否注入样式，默认为true
 */
js.mapNavigation = function(config) {
	// 参数验证和解析
	let location = null;
	
	if (typeof config === 'string') {
		// 字符串格式: "纬度,经度,其他参数,地点名称"
		const parts = config.split(',');
		if (parts.length >= 4) {
			const lat = parseFloat(parts[0]);
			const lng = parseFloat(parts[1]);
			const name = parts.slice(3).join(',').trim();
			
			// 验证解析结果
			if (!isNaN(lat) && !isNaN(lng) && name) {
				location = {
					name: name,
					latitude: lat,
					longitude: lng
				};
			} else {
				console.error('地图导航参数解析失败:', config);
			}
		} else {
			console.error('地图导航参数格式错误，应为: "纬度,经度,其他参数,地点名称"');
		}
	} else if (config && typeof config === 'object' && config.location) {
		// 对象格式
		const loc = config.location;
		if (loc.name && typeof loc.latitude === 'number' && typeof loc.longitude === 'number') {
			location = {
				name: loc.name,
				latitude: loc.latitude,
				longitude: loc.longitude
			};
		} else {
			console.error('地图导航对象参数格式错误:', config);
		}
	}

	// 使用默认配置（天安门广场）
	if (!location) {
		location = {
			name: '天安门广场',
			latitude: 39.909187,
			longitude: 116.397451
		};
		console.warn('使用默认地点:', location.name);
	}
	
	// 最终验证坐标有效性
	if (location.latitude < -90 || location.latitude > 90 || 
		location.longitude < -180 || location.longitude > 180) {
		alert('地点坐标无效，请检查位置信息');
		return;
	}

	// 检测App环境并优先使用App内地图功能
	if (detectAppEnvironment()) {
		console.log('检测到App环境，使用App内地图功能');
		if (tryAppMapNavigation(location)) {
			return; // App内地图调用成功，直接返回
		}
		console.warn('App内地图调用失败，使用备用方案');
	}

	// 检测设备类型
	const deviceType = detectDevice();
	
	// PC端直接使用腾讯地图，移动端显示地图选择框
	if (deviceType === 'web') {
		console.log('PC端环境，使用腾讯地图');
		navigateToMap('tencent', location);
	} else {
		console.log('移动端环境，显示地图选择框');
		// 移动端显示地图选择对话框
		const mapChoice = prompt('请选择地图应用：\n1. 高德地图\n2. 百度地图\n3. 腾讯地图\n\n请输入数字(1-3):', '1');
		
		if (mapChoice === '1' || mapChoice === null || mapChoice === '') {
			console.log('用户选择高德地图');
			navigateToMap('gaode', location);
		} else if (mapChoice === '2') {
			console.log('用户选择百度地图');
			navigateToMap('baidu', location);
		} else if (mapChoice === '3') {
			console.log('用户选择腾讯地图');
			navigateToMap('tencent', location);
		} else {
			console.log('无效选择，默认使用高德地图');
			navigateToMap('gaode', location);
		}
	}

	/**
	 * 坐标转换函数 - GCJ-02(高德)到BD-09(百度)
	 * @param {number} lng - 经度
	 * @param {number} lat - 纬度
	 * @returns {Object} 转换后的坐标
	 */
	function gcj02ToBd09(lng, lat) {
		// 输入验证
		if (typeof lng !== 'number' || typeof lat !== 'number') {
			console.error('坐标转换参数错误: lng和lat必须是数字');
			return { lng: lng, lat: lat };
		}
		
		// 验证坐标范围
		if (lng < -180 || lng > 180 || lat < -90 || lat > 90) {
			console.error('坐标超出有效范围: lng(-180~180), lat(-90~90)');
			return { lng: lng, lat: lat };
		}
		
		// 使用更精确的PI值
		const PI = Math.PI;
		const x_PI = PI * 3000.0 / 180.0;
		
		// 坐标转换算法
		const z = Math.sqrt(lng * lng + lat * lat) + 0.00002 * Math.sin(lat * x_PI);
		const theta = Math.atan2(lat, lng) + 0.000003 * Math.cos(lng * x_PI);
		const bd_lng = z * Math.cos(theta) + 0.0065;
		const bd_lat = z * Math.sin(theta) + 0.006;
		
		// 返回转换后的坐标，保留6位小数精度
		return {
			lng: Math.round(bd_lng * 1000000) / 1000000,
			lat: Math.round(bd_lat * 1000000) / 1000000,
			// 为了兼容性，同时提供longitude和latitude属性
			longitude: Math.round(bd_lng * 1000000) / 1000000,
			latitude: Math.round(bd_lat * 1000000) / 1000000
		};
	}

	/**
	 * 检测App环境
	 * @returns {boolean} 是否在App环境中
	 */
	function detectAppEnvironment() {
		const ua = navigator.userAgent;
		
		// 检测APICloud环境
		if (typeof apicloud !== 'undefined' && apicloud) {
			console.log('检测到APICloud环境');
			return true;
		}
		
		// 检测信呼OA原生App环境
		if (ua.indexOf('XINHUOA') > -1) {
			console.log('检测到信呼OA原生App环境');
			return true;
		}
		
		// 检测自定义App对象
		if (typeof appxinhu === 'object' && appxinhu) {
			console.log('检测到自定义App对象');
			return true;
		}
		
		// 检测API对象（通用App环境）
		if (typeof api !== 'undefined' && api && typeof api.openWin === 'function') {
			console.log('检测到通用App环境（API对象）');
			return true;
		}
		
		return false;
	}

	/**
	 * 尝试在App内调用地图导航
	 * @param {Object} location 位置信息
	 * @returns {boolean} 是否调用成功
	 */
	function tryAppMapNavigation(location) {
		try {
			// 1. 优先尝试自定义App对象的地图功能
			if (typeof appxinhu === 'object' && appxinhu) {
				if (appxinhu.openMap && typeof appxinhu.openMap === 'function') {
					console.log('使用appxinhu.openMap调用地图');
					appxinhu.openMap({
						latitude: location.latitude,
						longitude: location.longitude,
						name: location.name
					});
					return true;
				}
				if (appxinhu.showMap && typeof appxinhu.showMap === 'function') {
					console.log('使用appxinhu.showMap调用地图');
					appxinhu.showMap(location.latitude, location.longitude, location.name);
					return true;
				}
			}
			
			// 2. 尝试APICloud的地图功能
			if (typeof apicloud !== 'undefined' && apicloud && typeof api !== 'undefined') {
				// 尝试打开地图模块
				if (api.openMap && typeof api.openMap === 'function') {
					console.log('使用APICloud api.openMap调用地图');
					api.openMap({
						type: 'gaode', // 默认使用高德地图
						lat: location.latitude,
						lon: location.longitude,
						title: location.name
					});
					return true;
				}
				
				// 尝试使用bMap模块（百度地图）
				if (api.require) {
					try {
						console.log('尝试使用APICloud bMap模块');
						const bMap = api.require('bMap');
						if (bMap && bMap.openMap) {
							bMap.openMap({
								type: 'gaode',
								lat: location.latitude,
								lon: location.longitude,
								title: location.name
							});
							return true;
						}
					} catch (e) {
						console.warn('bMap模块调用失败:', e);
					}
				}
			}
			
			// 3. 尝试通用API对象的地图功能
			if (typeof api !== 'undefined' && api) {
				// 尝试直接调用系统地图
				if (api.call && typeof api.call === 'function') {
					console.log('使用api.call调用系统地图');
					// Android系统
					if (api.systemType && api.systemType.toLowerCase().indexOf('android') > -1) {
						api.call('system', 'openMap', {
							latitude: location.latitude,
							longitude: location.longitude,
							title: location.name
						}, function(ret) {
							console.log('Android地图调用结果:', ret);
						});
						return true;
					}
					// iOS系统
					else if (api.systemType && api.systemType.toLowerCase().indexOf('ios') > -1) {
						api.call('system', 'openMap', {
							latitude: location.latitude,
							longitude: location.longitude,
							title: location.name
						}, function(ret) {
							console.log('iOS地图调用结果:', ret);
						});
						return true;
					}
				}
			}
			
			// 4. 最后尝试使用URL Scheme方式（适用于混合App）
			const deviceType = detectDevice();
			if (deviceType !== 'web') {
				console.log('尝试使用URL Scheme调用地图');
				
				// 按优先级尝试不同的地图应用
				const mapTypes = ['gaode', 'baidu', 'tencent'];
				
				for (let i = 0; i < mapTypes.length; i++) {
					const mapType = mapTypes[i];
					const mapUrl = buildStandardMapUrl(mapType, deviceType, location);
					
					if (mapUrl) {
						try {
							console.log(`尝试打开${mapType}地图URL Scheme:`, mapUrl);
							
							// 使用iframe方式尝试打开，避免页面跳转
							const iframe = document.createElement('iframe');
							iframe.style.display = 'none';
							iframe.src = mapUrl;
							document.body.appendChild(iframe);
							
							// 设置超时检测URL Scheme是否成功
							let schemeSuccess = false;
							setTimeout(() => {
								try {
									document.body.removeChild(iframe);
								} catch (e) {
									console.warn('移除iframe失败:', e);
								}
								
								// 如果URL Scheme失败，尝试网页版
								if (!schemeSuccess) {
									console.log(`${mapType}地图URL Scheme可能失败，尝试网页版`);
									const webUrl = buildWebMapUrl(location, mapType);
									if (webUrl) {
										try {
											window.open(webUrl, '_blank');
											console.log(`${mapType}地图网页版打开成功`);
										} catch (webError) {
											console.warn(`${mapType}地图网页版打开失败:`, webError);
										}
									}
								}
							}, 2000);
							
							// 标记可能成功（实际成功很难检测）
							schemeSuccess = true;
							console.log(`${mapType}地图URL Scheme已尝试`);
							return true;
							
						} catch (e) {
							console.warn(`${mapType}地图URL Scheme调用失败:`, e);
							// URL Scheme失败，立即尝试网页版
							const webUrl = buildWebMapUrl(location, mapType);
							if (webUrl) {
								try {
									window.open(webUrl, '_blank');
									console.log(`${mapType}地图网页版打开成功`);
									return true;
								} catch (webError) {
									console.warn(`${mapType}地图网页版也失败:`, webError);
								}
							}
							// 继续尝试下一个地图应用
						}
					}
				}
				
				// 如果所有URL Scheme都失败，尝试直接跳转
				console.log('所有URL Scheme尝试失败，使用直接跳转方式');
				const fallbackUrl = buildStandardMapUrl('gaode', deviceType, location);
				if (fallbackUrl) {
					try {
						window.location.href = fallbackUrl;
						return true;
					} catch (e) {
						console.warn('直接跳转也失败:', e);
					}
				}
				
				// 最后尝试网页版地图
				console.log('尝试打开网页版地图');
				const webMapUrl = buildWebMapUrl(location);
				if (webMapUrl) {
					try {
						window.open(webMapUrl, '_blank');
						return true;
					} catch (e) {
						console.warn('网页版地图打开失败:', e);
					}
				}
			}
			
		} catch (error) {
			console.error('App内地图调用失败:', error);
		}
		
		return false;
	}

	/**
	 * 构建App内地图URL
	 * @param {string} deviceType 设备类型
	 * @param {Object} location 位置信息
	 * @returns {string|null} 地图URL
	 */
	function buildAppMapUrl(deviceType, location) {
		try {
			if (deviceType === 'ios') {
				// iOS优先使用高德地图URL Scheme
				return `iosamap://navi?sourceApplication=xinhuoa&poiname=${encodeURIComponent(location.name)}&lat=${location.latitude}&lon=${location.longitude}&dev=1&style=2`;
			} else if (deviceType === 'android') {
				// Android优先使用高德地图URL Scheme
				return `androidamap://navi?sourceApplication=xinhuoa&poiname=${encodeURIComponent(location.name)}&lat=${location.latitude}&lon=${location.longitude}&dev=1&style=2`;
			}
		} catch (error) {
			console.error('构建App地图URL失败:', error);
		}
		return null;
	}

	/**
	 * 构建标准地图URL Scheme
	 * @param {string} mapType 地图类型：'gaode', 'baidu', 'tencent'
	 * @param {string} deviceType 设备类型
	 * @param {Object} location 位置信息
	 * @returns {string|null} 地图URL
	 */
	function buildStandardMapUrl(mapType, deviceType, location) {
		try {
			const lat = location.latitude;
			const lng = location.longitude;
			const name = encodeURIComponent(location.name);
			
			if (mapType === 'gaode') {
				// 高德地图统一URL Scheme
				return `amapuri://route/plan/?dlat=${lat}&dlon=${lng}&dname=${name}&dev=0&t=0`;
			} else if (mapType === 'baidu') {
				// 百度地图需要坐标转换
				const bdCoords = gcj02ToBd09(lat, lng);
				// 百度地图正确URL Scheme
				return `baidumap://map/direction?destination=${bdCoords.lat},${bdCoords.lng}&mode=driving&region=&src=xinhuoa&coord_type=bd09ll`;
			} else if (mapType === 'tencent') {
				// 腾讯地图正确URL Scheme
				return `qqmap://map/routeplan?type=drive&tocoord=${lat},${lng}&to=${name}&coord_type=1&policy=0`;
			}
		} catch (error) {
			console.error('构建标准地图URL失败:', error);
		}
		return null;
	}

	/**
	 * 构建网页版地图URL
	 * @param {Object} location 位置信息
	 * @param {string} mapType 地图类型，可选
	 * @returns {string|null} 网页版地图URL
	 */
	function buildWebMapUrl(location, mapType = 'gaode') {
		try {
			const lat = location.latitude;
			const lng = location.longitude;
			const name = encodeURIComponent(location.name);
			const deviceType = detectDevice();
			const isPC = deviceType === 'web';
			
			// 如果是PC端且没有指定地图类型，默认使用腾讯地图
			if (isPC && mapType === 'gaode') {
				mapType = 'tencent';
			}
			
			if (mapType === 'gaode') {
				// 高德地图网页版
				return `https://uri.amap.com/navigation?to=${lng},${lat},${name}&mode=car&policy=1&src=xinhuoa&callnative=${isPC ? '0' : '1'}`;
			} else if (mapType === 'baidu') {
				// 百度地图网页版
				const bdCoords = gcj02ToBd09(lat, lng);
				if (isPC) {
					// PC端使用百度地图PC版
					return `https://map.baidu.com/direction?origin=我的位置&destination=latlng:${bdCoords.latitude},${bdCoords.longitude}|name:${name}&mode=driving&coord_type=bd09ll&src=xinhuoa`;
				} else {
					// 移动端使用百度地图移动版
					return `https://map.baidu.com/mobile/webapp/search/search/qt=nav&sn=2$$$$$$&en=2$$${bdCoords.latitude},${bdCoords.longitude}$$${name}$$$$&sc=4&ec=4&pn=0&rn=5&version=4&da_src=shareurl`;
				}
			} else if (mapType === 'tencent') {
				// 腾讯地图网页版
				if (isPC) {
					// PC端使用腾讯地图PC版
					return `https://map.qq.com/m/direction/result/?type=drive&to=${name}&tocoord=${lat},${lng}&policy=0&referer=xinhuoa`;
				} else {
					// 移动端使用腾讯地图移动版
					return `https://apis.map.qq.com/uri/v1/routeplan?type=drive&to=${name}&tocoord=${lat},${lng}&coord_type=1&policy=0&referer=xinhuoa&callnative=1`;
				}
			}
			
			// 默认返回腾讯地图（PC端）或高德地图（移动端）
			if (isPC) {
				return `https://map.qq.com/m/direction/result/?type=drive&to=${name}&tocoord=${lat},${lng}&policy=0&referer=xinhuoa`;
			} else {
				return `https://uri.amap.com/navigation?to=${lng},${lat},${name}&mode=car&policy=1&src=xinhuoa&callnative=1`;
			}
		} catch (error) {
			console.error('构建网页版地图URL失败:', error);
		}
		return null;
	}

	/**
	 * 检测设备类型
	 * @returns {string} 设备类型：'ios', 'android', 或 'web'
	 */
	function detectDevice() {
		const ua = navigator.userAgent.toLowerCase();
		if (/iphone|ipad|ipod/.test(ua)) {
			return 'ios';
		} else if (/android/.test(ua)) {
			return 'android';
		}
		return 'web';
	}

	/**
	 * 尝试打开应用，如果失败则提示未安装或打开网页版
	 * @param {string} appUrl - 应用URL
	 * @param {string} webUrl - 网页版URL（备用方案）
	 * @param {string} mapName - 地图名称
	 */
	function tryOpenApp(appUrl, webUrl, mapName) {
		// 验证参数
		if (!appUrl || !webUrl || !mapName) {
			console.error('tryOpenApp: 缺少必要参数');
			alert('地图调用参数错误');
			return;
		}

		// 记录尝试打开应用的时间
		const startTime = Date.now();
		// 标记应用是否已成功打开
		let appOpened = false;
		// 标记是否已经处理过结果
		let handled = false;
		// 超时定时器ID
		let timeoutId = null;

		// 清理函数
		const cleanup = function() {
			if (timeoutId) {
				clearTimeout(timeoutId);
				timeoutId = null;
			}
			try {
				document.removeEventListener('visibilitychange', visibilityChange);
				document.removeEventListener('pagehide', pageHideHandler);
				window.removeEventListener('blur', windowBlurHandler);
			} catch (e) {
				// 忽略移除事件监听器时的错误
			}
		};

		// 处理应用打开失败的情况
		const handleAppOpenFailed = function() {
			if (handled) return;
			handled = true;
			cleanup();
			
			if (webUrl) {
				if (confirm(`${mapName}应用可能未安装或无法打开，是否使用网页版？`)) {
					try {
						window.open(webUrl, '_blank');
					} catch (error) {
						console.error('打开网页版地图失败:', error);
						alert('无法打开地图，请检查浏览器设置');
					}
				}
			} else {
				alert('地图应用无法打开，且没有网页版备用方案');
			}
		};

		// 处理应用成功打开的情况
		const handleAppOpenSuccess = function() {
			if (handled) return;
			handled = true;
			appOpened = true;
			cleanup();
		};

		// 监听页面可见性变化
		const visibilityChange = function() {
			if (handled) return;
			
			const timeElapsed = Date.now() - startTime;
			// 如果页面变为不可见，说明应用可能已经打开
			if (document.hidden) {
				handleAppOpenSuccess();
			}
			// 如果页面在短时间内（小于1.5秒）恢复可见，且之前没有成功打开应用，说明应用可能没有被打开
			else if (timeElapsed < 1500 && !appOpened) {
				handleAppOpenFailed();
			}
		};

		// 监听页面隐藏事件（更可靠的应用切换检测）
		const pageHideHandler = function() {
			handleAppOpenSuccess();
		};

		// 监听窗口失焦事件
		const windowBlurHandler = function() {
			// 延迟检查，避免误判
			setTimeout(function() {
				if (!handled && document.hidden) {
					handleAppOpenSuccess();
				}
			}, 100);
		};

		// 添加事件监听
		try {
			document.addEventListener('visibilitychange', visibilityChange);
			document.addEventListener('pagehide', pageHideHandler);
			window.addEventListener('blur', windowBlurHandler);
		} catch (e) {
			console.warn('添加事件监听器失败:', e);
		}

		// 尝试打开应用
		try {
			// 使用iframe方式尝试打开应用（更兼容的方式）
			const iframe = document.createElement('iframe');
			iframe.style.display = 'none';
			iframe.src = appUrl;
			document.body.appendChild(iframe);
			
			// 清理iframe
			setTimeout(function() {
				try {
					document.body.removeChild(iframe);
				} catch (e) {
					// 忽略清理iframe时的错误
				}
			}, 1000);
			
			// 备用方式：直接设置location.href
			setTimeout(function() {
				if (!handled) {
					try {
						window.location.href = appUrl;
					} catch (error) {
						console.warn('location.href方式调用失败:', error);
					}
				}
			}, 25);
			
		} catch (error) {
			// 如果直接调用失败，立即打开网页版
			console.error('应用调用失败:', error);
			handleAppOpenFailed();
			return;
		}

		// 设置超时，如果超过2.5秒还没有打开应用，则提供备用方案
		timeoutId = setTimeout(function() {
			if (!handled) {
				handleAppOpenFailed();
			}
		}, 2500);
	}

	/**
	 * 导航到不同地图的函数
	 * @param {string} mapType - 地图类型：'gaode', 'baidu', 或 'tencent'
	 * @param {Object} location - 位置信息
	 */
	function navigateToMap(mapType, location) {
		const deviceType = detectDevice();
		let appUrl = '';
		let webUrl = '';

		switch (mapType) {
			case 'gaode':
				// 高德地图 - 使用GCJ-02坐标系，2024年最新URL格式
				if (deviceType === 'ios') {
					// iOS高德地图导航URL scheme - 使用navi接口（官方推荐）
					appUrl = `iosamap://navi?sourceApplication=xinhuoa&poiname=${encodeURIComponent(location.name)}&lat=${location.latitude}&lon=${location.longitude}&dev=1&style=2`;
				} else if (deviceType === 'android') {
					// Android高德地图导航URL scheme - 使用navi接口（官方推荐）
					appUrl = `androidamap://navi?sourceApplication=xinhuoa&poiname=${encodeURIComponent(location.name)}&lat=${location.latitude}&lon=${location.longitude}&dev=1&style=2`;
				}
				// 高德地图网页版导航URL - 2024年最新格式
				webUrl = `https://uri.amap.com/navigation?to=${location.longitude},${location.latitude},${encodeURIComponent(location.name)}&mode=car&policy=1&src=xinhuoa&coordinate=gaode&callnative=1`;
				break;
			case 'baidu':
				// 百度地图 - 将GCJ-02坐标转换为BD-09坐标
				const bdCoords = gcj02ToBd09(location.longitude, location.latitude);
				// 百度地图2024年最新URL scheme格式
				if (deviceType === 'ios') {
					// iOS百度地图URL scheme - 使用direction接口（官方推荐）
					appUrl = `baidumap://map/direction?destination=${bdCoords.latitude},${bdCoords.longitude}&mode=driving&coord_type=bd09ll&src=xinhuoa`;
				} else {
					// Android百度地图URL scheme - 使用direction接口（官方推荐）
					appUrl = `baidumap://map/direction?destination=${bdCoords.latitude},${bdCoords.longitude}&mode=driving&coord_type=bd09ll&src=xinhuoa`;
				}
				// 百度地图网页版导航URL - 2024年最新格式
				webUrl = `https://api.map.baidu.com/direction?destination=latlng:${bdCoords.latitude},${bdCoords.longitude}|name:${encodeURIComponent(location.name)}&mode=driving&coord_type=bd09ll&src=xinhuoa&origin_region=当前位置&region=当前位置&callnative=1`;
				break;
			case 'tencent':
				// 腾讯地图 - 使用GCJ-02坐标系，2024年最新URL格式
				if (deviceType === 'ios') {
					// iOS腾讯地图URL scheme
					appUrl = `qqmap://map/routeplan?type=drive&to=${encodeURIComponent(location.name)}&tocoord=${location.latitude},${location.longitude}&policy=0&referer=xinhuoa`;
				} else {
					// Android腾讯地图URL scheme
					appUrl = `qqmap://map/routeplan?type=drive&to=${encodeURIComponent(location.name)}&tocoord=${location.latitude},${location.longitude}&policy=0&referer=xinhuoa`;
				}
				// 腾讯地图网页版导航URL - 2024年最新格式
				webUrl = `https://apis.map.qq.com/uri/v1/routeplan?type=drive&to=${encodeURIComponent(location.name)}&tocoord=${location.latitude},${location.longitude}&policy=0&referer=xinhuoa&callnative=1`;
				break;
		}

		// 获取地图名称
		const mapNames = {
			'gaode': '高德地图',
			'baidu': '百度地图',
			'tencent': '腾讯地图'
		};
		const mapName = mapNames[mapType] || '地图';

		// 验证URL是否构建成功
		if (!appUrl && !webUrl) {
			alert('地图URL构建失败，请检查位置信息');
			return;
		}

		// PC端直接使用网页版
		if (deviceType === 'web') {
			if (webUrl) {
				window.open(webUrl, '_blank');
			} else {
				alert('无法打开地图，请检查网络连接');
			}
			return;
		}
		
		// 移动端优先尝试打开原生应用
		if (appUrl) {
			// 创建一个简单的UI界面，显示正在打开地图
			const loadingDiv = document.createElement('div');
			loadingDiv.style.cssText = 'position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.7);z-index:9999;display:flex;flex-direction:column;justify-content:center;align-items:center;color:#fff;font-size:16px;';
			loadingDiv.innerHTML = `
				<div style="margin-bottom:15px;">正在打开${mapName}...</div>
				<div style="width:40px;height:40px;border:3px solid #f3f3f3;border-top:3px solid #3498db;border-radius:50%;animation:spin 1s linear infinite;"></div>
				<style>@keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}</style>
				<div style="margin-top:20px;font-size:14px;">如果未自动打开，请点击下方按钮</div>
				<button id="openWebMapBtn" style="margin-top:15px;padding:8px 15px;background:#3498db;color:#fff;border:none;border-radius:4px;font-size:14px;">打开网页版${mapName}</button>
			`;
			document.body.appendChild(loadingDiv);
			
			// 添加网页版按钮点击事件
			document.getElementById('openWebMapBtn').addEventListener('click', function() {
				if (webUrl) {
					window.open(webUrl, '_blank');
				}
				document.body.removeChild(loadingDiv);
			});
			
			// 尝试打开应用
			tryOpenApp(appUrl, webUrl, mapName);
			
			// 3秒后自动关闭加载界面
			setTimeout(function() {
				if (document.body.contains(loadingDiv)) {
					document.body.removeChild(loadingDiv);
				}
			}, 3000);
		} else if (webUrl) {
			// 如果没有应用URL，直接使用网页版
			window.open(webUrl, '_blank');
		} else {
			alert('无法打开地图，请检查网络连接');
		}
	}}

function showDebug(strv,col){
	var obj = $('div[temp="divt"]'),hei=50;
	for(var i=0;i<obj.length;i++)hei+=$(obj[i]).height()+11;
	if(!col)col='red';
	if(typeof(strv)!='string')strv = JSON.stringify(strv);
	var str = '<div temp="divt" onclick="$(\'div[temp=divt]\').remove()" style="background:rgba(0,0,0,0.8);font-size:12px;position:fixed;right:0px;top:'+hei+'px;padding:5px;z-index:99;word-wrap:break-word;word-break:break-all;white-space:normal;color:'+col+'">['+js.now('now')+']'+strv+'</div>';
	$('body').append(str);
}