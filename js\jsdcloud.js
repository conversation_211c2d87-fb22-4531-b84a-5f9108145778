/**
*	app使用的访问js，主页是来自dcloud下的接口
*	日期：2023-07-06
*	开发者：www.rockoa.com
*/
api = {
	deviceId:'',
	nowtheme:'',
	systemType:'android',
	deviceName:'',
	deviceModel:'',
	appVersion:'0.1',
	actionname:'',
	init:function(){
		this.deviceModel = plus.device.model;
		this.deviceName  = plus.device.vendor;
		this.deviceId    = plus.device.uuid;
		if(this.deviceName=='Apple')this.systemType='ios';
		this.appVersion  = plus.runtime.version;
		nowwin=plus.webview.currentWebview();
		nowwin.setTitleNViewButtonStyle(0,{onclick:api.rightbtn});
		if(window['xcy'] && xcy.resiezess)document.addEventListener("keyboardchange", function(e){
			setTimeout(function(){xcy.resiezess()},1000);
		}, false);
		this.hideProgress();
		navigator.geolocation.getCurrentPosition = plus.geolocation.getCurrentPosition;
		var zoom = this.rockFun('getOption',{key:'bodyzoom'});
		if(zoom){
			if(this.systemType=='android'){
				var wih= 100;
				if(zoom=='1')wih= 110;
				if(zoom=='2')wih= 120;
				var nwv=plus.android.currentWebview();
				var aa = plus.android.invoke(nwv,"getSettings");
				plus.android.invoke(aa,"setTextZoom",wih);
			}
		}
	},
	addEventListener:function(cans, fun){
		var key= 'event_'+cans.name+'';
		this.actionname  = cans.name;
		api[key] = fun;
		plus.storage.removeItem(key);
		clearInterval(this.Intervalobj);
		this.Intervalobj = setInterval(function(){
			var val = plus.storage.getItem(key),da=[],i;
			if(val){
				plus.storage.removeItem(key);
				da = JSON.parse(val);
				for(i=0;i<da.length;i++)fun(da[i]);
			}
		},500);
	},
	sendEvent:function(cans){
		var key = 'event_'+cans.name+'',val=cans.name,wn=false;
		if(val=='rockhome' || val=='rocklogin')wn= plus.webview.getWebviewById(plus.runtime.appid);
		if(val=='rockchat')wn= plus.webview.getWebviewById(val);
		if(wn){
			wn.evalJS('api.'+key+'('+JSON.stringify(cans)+')');
			return;
		}
		var val = plus.storage.getItem(key),da=[];
		if(val)da = JSON.parse(val);
		da.push(cans);
		plus.storage.setItem(key, JSON.stringify(da));
	},
	hideProgress:function(){
		plus.nativeUI.closeWaiting();
	},
	showProgress:function(cans){
		var msg = '';
		if(cans && cans.msg)msg = cans.msg;
		plus.nativeUI.showWaiting(msg, {modal:false});
	},
	rightbtn:function(){
		var str = '刷新';
		if(api.menulist)str+=','+api.menulist+'';
		api.createMenu({
			menu:str,
		}, function(ret, err) {
			var index = ret.menuIndex;
			if(index==0){
				api.showProgress();
				js.reload();
			}else{
				if(api.menufun)api.menufun(ret);
			}
		});
	},
	openWin:function(cans){
		var buttons = [{float:'right',type:'menu',fontSize:'20px'}];
		var titleNView = {backgroundColor:this.nowtheme,titleColor:'#ffffff',buttons:buttons,autoBackButton:true};
		var winid = cans.winid;
		if(!winid)winid = '';
		var wn= plus.webview.create(cans.url,winid, {softinputMode:'adjustResize',softinputNavBar:'auto',userSelect:false,titleNView:titleNView,backButtonAutoControl:'close',popGesture:'close'});
		wn.onloaded=function(){
			api.hideProgress();
			wn.evalJS(api.getScript());
		};
		if(cans.onclose)wn.onclose = cans.onclose;
		wn.show('slide-in-right',300);
		return wn;
	},
	openWindcloud:function(cans){
		this.openWin(cans);
	},
	closeWin:function(){
		nowwin.close("auto")
	},
	getScript:function(){
		var surl= this.jsdcloud,rnd=parseInt(Math.random()*999999);
		var bstr= 'api.jsdcloud="'+surl+'";api.nowtheme="'+this.nowtheme+'";api.init();apiready();';
		var str = 'if(!window.isappbo && (!window.api || !api.openWin)){isappbo=true;var scr = document.createElement("script");scr.src="'+surl+'?'+rnd+'";scr.onload = function(){'+bstr+'};document.getElementsByTagName("head")[0].appendChild(scr);}';
		return str;
	},
	prompt:function(cans, fun){
		plus.nativeUI.prompt(cans.msg, function(e){
			if(e.index==0)fun({buttonIndex:1,text:e.value});
		}, cans.title,cans.text, cans.buttons);
	},
	alert:function(cans, fun){
		plus.nativeUI.alert(cans.msg, function(ret){
			if(ret.index==0 && fun)fun({buttonIndex:1});
		}, cans.title, '确定');
	},
	createMenu:function(cans, fun){
		var btns=[];
		var arrs = cans.menu.split(',');
		for(var i=0;i<arrs.length;i++){
			btns.push({title:arrs[i].split('|')[0]});
		}
		plus.nativeUI.actionSheet({
			title:'选择菜单',
			cancel:'取消',
			buttons:btns
		},function(e){
			var ind = e.index-1;
			if(ind>-1)fun({menuIndex:ind,name:btns[ind].title});
		});
	},
	toast:function(cans){
		plus.nativeUI.toast(cans.msg);
	},
	rockFun:function(act,cans, fun){
		var bstr = '';
		//showAlert(act);
		if(act=='NotificationCancel'){
			plus.push.clear();
		}
		if(act=='startWebsocket'){
			this.startWebsocket(cans);
		}
		if(act=='clipBoard'){
			
		}
		if(act=='download'){
			api.toast({msg:''+cans.filename+'下载中...'});
			var dtask = plus.downloader.createDownload(cans.url,{filename:'_documents/'+cans.filename+''}, function(d, zt){
				if(zt==200){
					fun({status:1,downid:d.filename});
					api.toast({msg:'下载完成路径：'+d.filename+''});
				}
			});
			dtask.start();
		}
		if(act=='downloadView'){
			//api.toast({msg:'路径：'+cans.path+''});
			plus.runtime.openFile(cans.path);
		}
		if(act=='uploadcancel'){
			if(this.uploadtask)this.uploadtask.abort();
			this.uploadtask = false;
		}
		if(act=='initPush'){
			plus.push.getClientInfoAsync(function(info){
				info.pushstate = 'getui';
				fun(info);
			})
		}
		if(act=='startRecord'){
			js.yuyinload('录音中(<span id="miaodiv">0</span>)秒...<br>点击结束/取消', false, 60);
			var miao= 0;
			var red = plus.audio.getRecorder();
			clearInterval(this.yuyintime);
			red.record({
				filename:'_doc/audio/',format:'amr'
			}, function (luj) {
				clearInterval(api.yuyintime);
				if(miao>0){
					api.confirm({msg:'确定发送该语音'+miao+'秒？'}, function(ret){
						if(ret.buttonIndex==1)fun({status:miao*1000,filepath:luj});
					});
				}
			}, function (e) {
				miao = 0;
				api.toast({msg:'录音失败('+e.message+')'});
				js.wx.unload();
				clearInterval(api.yuyintime);
			});
			get('rockmodelmsg').onclick=function(){
				red.stop();$(this).remove();
			};
			this.yuyintime = setInterval(function(){
				miao++;$('#miaodiv').html(miao);
			},1000);
		}
		if(act=='AudioPlay'){
			if(api.playerobj)api.playerobj.close();
			js.yuyinload('播放中(<span id="miaodiv">0</span>)秒...<br>点击结束', false, 60);
			var miao= 0,filepath = '_documents/a.amr';
			plus.io.resolveLocalFileSystemURL(filepath,function(entry){entry.remove();});
			clearInterval(this.yuyintime);
			plus.downloader.createDownload(cans.url,{filename:filepath}, function(d){
				var player = plus.audio.createPlayer(d.filename);
				player.play( function () {
					player.close();
					clearInterval(api.yuyintime);
					js.yuyinload('播放已结束', false, 60);
					api.playerobj=false;
				}, function ( e ) {
					player.close();
					clearInterval(api.yuyintime);
				} ); 
				api.playerobj = player;
			}).start();
			this.yuyintime = setInterval(function(){
				if(!get('miaodiv'))js.yuyinload('播放中(<span id="miaodiv">0</span>)秒...<br>点击结束', false, 60);
				miao++;$('#miaodiv').html(miao);
			},1000);
		}
		if(act=='openCog'){
			xcy.opennei({name:'设置',url:'cog',nlogin:true});
		}
		if(act=='updateChange'){
			api.toast({msg:'已是最新'});
		}
		if(act=='setOption'){
			plus.storage.setItem(cans.key, cans.value+'');
		}
		if(act=='getOption'){
			bstr = plus.storage.getItem(cans.key)
		}
		return bstr;
	},
	showMsg:function(cans, fun){
		this.hideProgress();
		plus.nativeUI.toast(cans.msg);
		if(fun)setTimeout(function(){fun()},2000);
	},
	setMenu:function(cans,fun){
		api.menulist = cans.menu;
		api.menufun  = fun;
		nowwin.setTitleNViewButtonStyle(0,{onclick:api.rightbtn});
	},
	startWebsocket:function(conf){
		if(this.ws)this.ws.close();
		var me  	= this;
		this.ws 	= new WebSocket(jm.base64decode(conf.wsurl));
		this.ws.onopen = function(){
			this.send('{"from":"'+conf.recid+'_app","adminid":"'+conf.adminid+'","atype":"connect","sendname":"'+conf.adminname+'"}');
			me.broadWebsocket('open');
		}
		this.ws.onclose = function(e){
			me.broadWebsocket('close');
		};
		this.ws.onmessage = function(evt){
			var ds = JSON.parse(evt.data);
			me.broadWebsocket('message', ds);
		};
	},
	broadWebsocket:function(stype, data){
		if(!data)data='';
		var da = {name:this.actionname,stype:'websocket',sockettype:stype,socketdata:data};
		homeim.websocketData(da);
	},
	regHome:function(){
		document.addEventListener("pause", function(){
			homeim.pushListener({name:api.actionname,stype:'stop'});
		}, false);
		
		document.addEventListener("resume", function(){
			homeim.pushListener({name:api.actionname,stype:'resume'});
		}, false);
		document.addEventListener('touchstart', function(){
			windowfocus=true;nowchat='';
		}, false);
		this.addBiaoti('＋');
		this.reglongmenu();
	},
	addBiaoti:function(tx){
		//var style = nowwin.getStyle();
		var style = {softinputMode:'adjustResize',softinputNavBar:'none'};
		var buttons = [{text:tx,float:'right',onclick:api.rightbtn,type:'none',fontSize:'20px'}];
		style.titleNView = {backgroundColor:maincolor,titleColor:'#ffffff',autoBackButton:false,buttons:buttons};
		style.userSelect = false;
		nowwin.setStyle(style);
		var tit = document.title;
		document.title = ''+tit+'.';
		document.title = tit;
	},
	reglongmenu:function(){
		document.addEventListener('touchstart', function(){
			clearTimeout(api.longtapv);
			api.longtapv = setTimeout(function(){xcy.longmenu();},300);
		}, false);
		document.addEventListener('touchmove', function(){
			clearTimeout(api.longtapv);
		}, false);
		document.addEventListener('touchend', function(){
			clearTimeout(api.longtapv);
		}, false);
	},
	openScan:function(cans, fun){
		var titleNView = {backgroundColor:maincolor,type:'float',titleText:'扫一扫',titleColor:'#ffffff',autoBackButton:true};
		var wn = plus.webview.create('list/openscan.html?'+js.getrand()+'','',{titleNView:titleNView,backButtonAutoControl:'close'});
		wn.onclose=function(){
			var result = plus.storage.getItem('scanresult');
			if(result)fun({result:result});
		};
		wn.show('slide-in-right');
	},
	imageView:function(cans){
		plus.nativeUI.previewImage([cans.url.replace('_s.','.')]);
	},
	Notification:function(cans){
		plus.push.createMessage(cans.title+'\n'+cans.msg);
	},
	ajax:function(cans, fun){
		var xhr = new plus.net.XMLHttpRequest();
		var met = cans.method,url=cans.url;
		if(met=='get' || met=='GET'){
			var jg = (url.indexOf('?')==-1)?'?':'&';
			if(cans.data)url+=''+jg+''+cans.data+'';
			cans.data = '';
		}
		xhr.onreadystatechange = function(){
			if(xhr.readyState==4){
				if(xhr.status == 200){
					fun(js.decode(xhr.responseText));
				}else{
					fun(false, xhr);
				}
			}
		}
		xhr.open(met,url);
		if(met=='post' || met=='POST')xhr.setRequestHeader("Content-Type","application/x-www-form-urlencoded;charset=utf-8");  
		xhr.send(cans.data);
	},
	getPicture:function(cans, fun){
		if(cans.sourceType=='camera'){
			var cmr = plus.camera.getCamera();
			cmr.captureImage(function(p){
				if(!cans.quality)cans.quality = 80;
				var dst = p.replace('.','_s.');
				plus.zip.compressImage({
					src:p,dst:dst,quality:cans.quality
				},function() {
					if(cans.destinationType=='base64'){
						api.imageTobase(dst, fun);
					}else{
						fun({filepath:dst,base64Data:''});
					}
				},function(e){
					js.getarr(e);
				});
			})
		}else{
			plus.gallery.pick(function(p){
				if(p)fun({filepath:p,base64Data:''});
			}, false, {filter:'image'});
		}
	},
	imageTobase:function(path, fun){
		plus.io.resolveLocalFileSystemURL(path, function( entry ) {
			entry.file( function(file){
				var fileReader = new plus.io.FileReader();
				fileReader.readAsDataURL(file);
				fileReader.onloadend = function(evt) {
					let speech = evt.target.result;//base64图片   
					let imgData = speech.replace(/^data:image\/\w+;base64,/, ""); 
					fun({filepath:path,base64Data:imgData});
				}
			} );
		})
	},
	upload:function(cans, fun){
		if(this.uploadtask)return;
		var task = plus.uploader.createUpload(cans.url,{},function (t, zt){
			api.uploadtask = false;
			if(zt == 200) { 
				var bstr = t.responseText;
				if(bstr.indexOf('valid')>0){
					fun(JSON.parse(bstr));
				}else{
					fun(false, {responseText:bstr});
				}
			} else {
				fun(false, {responseText:'失败:'+zt+''});
			}
		});
		task.addFile(cans.filepath, {key:'file'});
		task.start();
		this.uploadtask = task;
	},
	confirm:function(cans,fun){
		plus.nativeUI.confirm(cans.msg, function(e){
			var jg = 0;
			if(e.index==0)jg=1;
			fun({buttonIndex:jg});
		});
	}
}	