// 地图管理器类 - 封装所有地图相关方法
class MapManager {
    constructor() {
        // 初始化属性
        this.map = null;
        this.marker = null;
        this.geolocation = null;
        this.currentPosition = null;
        this.infoWindow = null; // 添加信息窗口属性
        this.containerId = null; // 容器ID
        this.mapContainerId = null; // 地图容器ID
        this.sidebarId = null; // 侧边栏ID
        this.searchInputId = null; // 搜索输入框ID
        this.searchBtnId = null; // 搜索按钮ID
        this.placesListId = null; // 地点列表ID
        this.currentAddressId = null; // 当前地址ID
        this.styleElement = null; // 样式元素
        this.selectedPlaceIndex = -1; // 选中的地点索引
        this.isMobile = window.innerWidth <= 768; // 是否为移动设备
        this.currentSelectedPlace = null; // 新增：存储当前选中的地点信息
        this.markers = [];
        
        // 新增：性能优化相关属性
        this.isMapLoaded = false; // 地图是否已加载
        this.searchDebounceTimer = null; // 搜索防抖定时器
        this.resizeDebounceTimer = null; // 窗口大小变化防抖定时器
        this.domFragment = null; // DOM文档片段
        this.animationFrame = null; // 动画帧ID
        
        // 移动端弹窗手势相关
        this.touchStartY = 0;
        this.touchCurrentY = 0;
        this.isDragging = false;
        this.popupStartBottom = 0;
    }

    // 初始化地图
    init(containerId) {
        // 保存容器ID
        this.containerId = containerId;

        // 创建DOM结构
        this.createDOMStructure();

        // 添加CSS样式
        this.addStyles();

        // 懒加载地图资源
        this.lazyLoadMap();
    }

    // 懒加载地图资源
    lazyLoadMap() {
        // 使用Intersection Observer检测容器是否可见
        const container = document.getElementById(this.containerId);
        if (!container) return;

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting && !this.isMapLoaded) {
                    this.initializeMap();
                    observer.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.1
        });

        observer.observe(container);

        // 备用方案：如果容器已经可见，直接初始化
        if (this.isElementVisible(container)) {
            setTimeout(() => {
                if (!this.isMapLoaded) {
                    this.initializeMap();
                }
            }, 100);
        }
    }

    // 检查元素是否可见
    isElementVisible(element) {
        const rect = element.getBoundingClientRect();
        return rect.top < window.innerHeight && rect.bottom > 0;
    }

    // 初始化地图核心功能
    initializeMap() {
        if (this.isMapLoaded) return;
        
        // 显示加载状态
        this.showLoadingState();
        
        try {
            // 创建地图配置
            const mapOptions = {
                center: new qq.maps.LatLng(23.38624681887071, 116.75738453865051),
                zoom: 15,
                disableDefaultUI: false, // 保持默认UI
                zoomControl: true,
                panControl: false,
                scaleControl: false,
                streetViewControl: false
            };
            
            // 创建地图
            this.map = new qq.maps.Map(document.getElementById(this.mapContainerId), mapOptions);
            
            // 地图加载完成后的回调
            qq.maps.event.addListenerOnce(this.map, 'tilesloaded', () => {
                this.onMapReady();
            });
            
            // 创建信息窗口对象
            this.infoWindow = new qq.maps.InfoWindow({
                map: this.map
            });

            // 创建地点搜索服务
            this.searchService = new qq.maps.SearchService({
                complete: (results) => {
                    if(results.type === "search") {
                        this.displaySearchResults(results.detail.pois, this.map.getCenter());
                    } else if(results.type === "poi_list") {
                        this.displaySearchResults(results.detail.pois, this.map.getCenter());
                    }
                }
            });

            // 创建地理编码服务
             this.geolocation = new qq.maps.Geolocation("OHTBZ-D33X4-TSXUO-4CJEL-T6IAZ-OUFUS", this.mapContainerId);

            // 地图点击事件
            qq.maps.event.addListener(this.map, 'click', (event) => {
                const latLng = event.latLng;
                this.updateCurrentMarker(latLng);
                this.debouncedSearchNearby(latLng);
            });

            // 绑定事件监听器
            this.bindEventListeners();
            
        } catch (error) {
            console.error('地图初始化失败:', error);
            this.showErrorState('地图加载失败，请刷新页面重试');
        }
    }
    
    // 地图准备就绪回调
    onMapReady() {
        this.isMapLoaded = true;
        this.hideLoadingState();
        
        // 获取当前位置
        this.getCurrentLocation();
        
        console.log('地图加载完成');
    }
    
    // 显示加载状态
    showLoadingState() {
        const mapContainer = document.getElementById(this.mapContainerId);
        if (mapContainer) {
            const loadingDiv = document.createElement('div');
            loadingDiv.id = 'map-loading';
            loadingDiv.style.cssText = `
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(255, 255, 255, 0.9);
                padding: 20px;
                border-radius: 8px;
                text-align: center;
                z-index: 1000;
                font-size: 14px;
                color: #666;
            `;
            loadingDiv.innerHTML = '地图加载中...';
            mapContainer.style.position = 'relative';
            mapContainer.appendChild(loadingDiv);
        }
    }
    
    // 隐藏加载状态
    hideLoadingState() {
        const loadingDiv = document.getElementById('map-loading');
        if (loadingDiv) {
            loadingDiv.remove();
        }
    }
    
    // 显示错误状态
    showErrorState(message) {
        const mapContainer = document.getElementById(this.mapContainerId);
        if (mapContainer) {
            mapContainer.innerHTML = `
                <div style="
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 100%;
                    background: #f5f5f5;
                    color: #999;
                    font-size: 14px;
                    text-align: center;
                ">
                    ${message}
                </div>
            `;
        }
    }

    // 绑定事件监听器
    bindEventListeners() {
        // 搜索按钮点击事件
        const searchBtn = document.getElementById(this.searchBtnId);
        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                if (this.isMobile) {
                    const popupSearchInput = document.querySelector(`#${this.popupContainerId} input`);
                    const keyword = popupSearchInput ? popupSearchInput.value.trim() : '';
                    if (keyword) {
                        this.debouncedSearchPlaces(keyword);
                    }
                } else {
                    const keyword = document.getElementById(this.searchInputId).value.trim();
                    if (keyword) {
                        this.debouncedSearchPlaces(keyword);
                    }
                }
            });
        }

        // 回车键搜索
        const searchInput = document.getElementById(this.searchInputId);
        if (searchInput) {
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    const keyword = e.target.value.trim();
                    if (keyword) {
                        this.debouncedSearchPlaces(keyword);
                    }
                }
            });
        }
    }

    // 防抖搜索附近地点
    debouncedSearchNearby(latLng) {
        if (this.searchDebounceTimer) {
            clearTimeout(this.searchDebounceTimer);
        }
        this.searchDebounceTimer = setTimeout(() => {
            this.searchNearbyPlaces(latLng);
        }, 300);
    }

    // 防抖搜索地点
    debouncedSearchPlaces(keyword) {
        if (this.searchDebounceTimer) {
            clearTimeout(this.searchDebounceTimer);
        }
        this.searchDebounceTimer = setTimeout(() => {
            this.searchPlaces(keyword);
        }, 300);
    }

    // 创建DOM结构
    createDOMStructure() {
        // 生成唯一ID
        const uniqueId = 'map_' + Math.random().toString(36).substr(2, 9);
        this.mapContainerId = 'map-container-' + uniqueId;
        this.sidebarId = 'sidebar-' + uniqueId;
        this.searchInputId = 'search-input-' + uniqueId;
        this.searchBtnId = 'search-btn-' + uniqueId;
        this.placesListId = 'places-list-' + uniqueId;
        this.currentAddressId = 'current-address-' + uniqueId;
        this.popupContainerId = 'popup-container-' + uniqueId;
        this.overlayId = 'overlay-' + uniqueId;

        // 获取容器元素
        const container = document.getElementById(this.containerId);
        if (!container) {
            console.error(`容器元素 #${this.containerId} 不存在`);
            return;
        }

        // 设置容器样式
        container.style.display = 'flex';
        container.style.flexDirection = 'column';
        container.style.width = '100%';
        container.style.minHeight = '500px';

        // 检查是否为移动设备
        const isMobile = window.innerWidth <= 768;
        this.isMobile = isMobile;

        // 创建HTML结构
        container.innerHTML = `
            <div class="header-${uniqueId}">
                <div class="current-address-${uniqueId}" id="${this.currentAddressId}">正在获取当前位置...</div>
            </div>
            <div class="main-content-${uniqueId}${isMobile ? '-mobile' : ''}">
                <div id="${this.mapContainerId}" class="map-container-${uniqueId}"></div>
                               ${!isMobile ? `
                <div class="sidebar-${uniqueId}" id="${this.sidebarId}">
                    <!-- 新增：固定搜索框和选择位置与确认按钮 -->
                    <div class="pc-search-bar-and-confirm">
                        <div class="search-bar-${uniqueId}">
                            <input type="text" id="${this.searchInputId}" placeholder="输入地点">
                            <button id="${this.searchBtnId}">搜索</button>
                        </div>
                        <div class="pc-search-text-and-confirm">
                            <div class="pc-search-text">选择位置</div>
                            <button id="confirm-btn-${uniqueId}">确认</button>
                        </div>
                    </div>
                    <div id="${this.placesListId}">
                        <div style="padding: 10px; text-align: center;">点击地图或搜索以显示周边地点</div>
                    </div>
                </div>` : ''}
                ${isMobile ? `
                <div class="mobile-search-trigger-${uniqueId}">
                    <input type="text" id="${this.searchInputId}" placeholder="输入地点" readonly>
                </div>
                <div id="${this.overlayId}" class="overlay-${uniqueId}"></div>
                <div id="${this.popupContainerId}" class="popup-container-${uniqueId}">
                    <div class="popup-header-${uniqueId}">
                        <div class="popup-handle-${uniqueId}"></div>
                        <div class="search-bar-${uniqueId} mobile-search-bar">
                            <input type="text" placeholder="输入地址">
                            <button id="${this.searchBtnId}">搜索</button>
                        </div>
                        <div class="mobile-search-text-and-confirm">
                            <div class="mobile-search-text">选择位置</div>
                            <button id="confirm-btn-${uniqueId}">确认</button>
                        </div>
                    </div>
                    <div id="${this.placesListId}" class="popup-content-${uniqueId}">
                        <div style="padding: 10px; text-align: center;">点击地图或搜索以显示周边地点</div>
                    </div>
                </div>` : ''}
            </div>
        `;

        // 添加窗口大小变化监听（防抖处理）
        window.addEventListener('resize', () => {
            if (this.resizeDebounceTimer) {
                clearTimeout(this.resizeDebounceTimer);
            }
            this.resizeDebounceTimer = setTimeout(() => {
                const isMobile = window.innerWidth <= 768;
                const mainContent = document.querySelector(`.main-content-${uniqueId}`) || document.querySelector(`.main-content-${uniqueId}-mobile`);
                if (mainContent) {
                    mainContent.className = isMobile ? `main-content-${uniqueId}-mobile` : `main-content-${uniqueId}`;
                    this.isMobile = isMobile;
                    
                    // 如果地图已加载，触发重绘
                    if (this.isMapLoaded && this.map) {
                        setTimeout(() => {
                            qq.maps.event.trigger(this.map, 'resize');
                        }, 100);
                    }
                }
            }, 150);
        });

        // 如果是移动端，添加弹窗相关事件监听
        if (this.isMobile) {
            // 点击触发器显示弹窗
            document.getElementById(this.searchInputId).addEventListener('click', (e) => {
                e.preventDefault();
                this.showPopup();
            });

            // 弹窗中搜索按钮点击事件
            const popupSearchInput = document.querySelector(`#${this.popupContainerId} input`);
            popupSearchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    const keyword = e.target.value.trim();
                    if (keyword) {
                        this.searchPlaces(keyword);
                        // 同步到外部搜索框
                        document.getElementById(this.searchInputId).value = keyword;
                    }
                }
            });

            // 同步搜索结果到触发器
            document.getElementById(this.searchBtnId).addEventListener('click', () => {
                if (this.isMobile) {
                    const keyword = popupSearchInput.value.trim();
                    if (keyword) {
                        // 同步到外部搜索框
                        document.getElementById(this.searchInputId).value = keyword;
                    }
                }
            });

            // 点击遮罩层隐藏弹窗
            document.getElementById(this.overlayId).addEventListener('click', () => {
                this.hidePopup();
            });

            // 阻止弹窗内容区域的点击事件冒泡到遮罩层
            document.getElementById(this.popupContainerId).addEventListener('click', (e) => {
                e.stopPropagation();
            });


        }
        // 确认按钮点击事件
        document.getElementById(`confirm-btn-${uniqueId}`).addEventListener('click', () => {
            if (this.selectedPlaceIndex !== -1) {
                const placesList = document.querySelectorAll(`#${this.placesListId} .place-item`);
                const selectedPlace = placesList[this.selectedPlaceIndex];
                const lat = parseFloat(selectedPlace.getAttribute('data-lat'));
                const lng = parseFloat(selectedPlace.getAttribute('data-lng'));
                const title = selectedPlace.querySelector('.place-name')?.innerText || '未知标题';
                const address = selectedPlace.querySelector('.place-address')?.innerText || '未知地址';
                const zoom = this.map.zoom;
                console.log("选中的位置:", { lat, lng, title, address,zoom });
                this.confirmbuttonCallback({ lat, lng, title, address,zoom })
            } else {
                console.log("未选中任何位置");
            }
        });
    }

    // 添加CSS样式
    addStyles() {
        const uniqueId = this.mapContainerId.split('-').pop();

        // 创建样式元素
        this.styleElement = document.createElement('style');
        this.styleElement.type = 'text/css';

        // 定义CSS样式
        const css = `
            #${this.containerId} {
                font-family: "Microsoft YaHei", sans-serif;
                display: flex;
                flex-direction: column;
                height: 100vh;
            }
            .header-${uniqueId} {
                padding: 10px;
                background-color: #f5f5f5;
                border-bottom: 1px solid #ddd;
            }
            .current-address-${uniqueId} {
                font-size: 14px;
                color: #666;
            }
            .main-content-${uniqueId} {
                display: flex;
                flex: 1;
                overflow: hidden;
                height: calc(100% - 40px);
            }
            .main-content-${uniqueId}-mobile {
                display: flex;
                flex-direction: column;
                flex: 1;
                overflow: hidden;
                height: calc(100% - 40px);
                position: relative;
            }
            .map-container-${uniqueId} {
                flex: 7;
                height: 100%;
            }
            .sidebar-${uniqueId} {
                flex: 3;
                border-left: 1px solid #ddd;
                overflow-y: auto;
                padding: 0,10px,10px,10px;
          
            }
            /* 移动端样式 */
            .main-content-${uniqueId}-mobile .map-container-${uniqueId} {
                height: 100%;
                width: 100%;
                flex: 1;
            }
            /* 移动端搜索触发器 */
            .mobile-search-trigger-${uniqueId} {
                position: absolute;
                bottom: 20px;
                left: 50%;
                transform: translateX(-50%);
                width: 80%;
                z-index: 100;
            }
            .mobile-search-trigger-${uniqueId} input {
                width: 100%;
                padding: 12px;
                border-radius: 24px;
                border: none;
                box-shadow: 0 2px 10px rgba(0,0,0,0.2);
                background-color: white;
                font-size: 15px;
                text-align: center;
            }
            /* 遮罩层 */
            .overlay-${uniqueId} {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 999;
                opacity: 0;
                display: none;
                transition: opacity 0.3s ease;
            }
            /* 弹出容器 */
            .popup-container-${uniqueId} {
                position: fixed;
                bottom: -70vh;
                left: 0;
                right: 0;
                height: 70vh;
                background-color: white;
                z-index: 1000;
                border-radius: 20px 20px 0 0;
                box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
                transition: bottom 0.3s ease;
                display: flex;
                flex-direction: column;
            }
            /* 弹窗头部 */
            .popup-header-${uniqueId} {
                padding: 15px;
                border-bottom: 1px solid #eee;
            }
            /* 拖动手柄 */
            .popup-handle-${uniqueId} {
                width: 40px;
                height: 5px;
                background-color: #ddd;
                border-radius: 3px;
                margin: 0 auto 15px auto;
            }
            /* 弹窗内容区域 */
            .popup-content-${uniqueId} {
                flex: 1;
                overflow-y: auto;
                -webkit-overflow-scrolling: touch;
            }
            /* 搜索栏样式 */
            .search-bar-${uniqueId} {
                display: flex;
                gap: 10px;
                margin-bottom: 10px;
                padding: 5px 0;
            }
            .search-bar-${uniqueId} input {
                flex: 1;
                padding: 8px 15px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 10px;
            }
            .search-bar-${uniqueId} button {
                padding: 8px 15px;
                background-color: #3B99FC;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
            }
            #${this.placesListId} .place-item {
                padding: 10px;
                border-bottom: 1px solid #eee;
                cursor: pointer;
                display: flex;
                align-items: flex-start;
            }
                        /* 新增样式：固定PC端搜索框和选择位置与确认按钮 */
            .pc-search-bar-and-confirm {
                position: sticky;
                top: 0;
                background-color: white;
                padding: 10px;
                border-bottom: 1px solid #eee;
                z-index: 1;
            }
            #${this.placesListId} .place-item:hover {
                background-color: #f9f9f9;
            }
            #${this.placesListId} .place-content {
                flex: 1;
            }
            #${this.placesListId} .place-name {
                font-weight: bold;
                margin-bottom: 5px;
            }
            #${this.placesListId} .place-address {
                font-size: 12px;
                color: #666;
            }
            #${this.placesListId} .place-distance {
                font-size: 12px;
                color: #3B99FC;
                margin-top: 5px;
            }
            #${this.placesListId} .radio-btn {
                width: 16px;
                height: 16px;
                border-radius: 50%;
                border: 1px solid #ccc;
                margin-right: 10px;
                margin-top: 3px;
                position: relative;
            }
            #${this.placesListId} .radio-btn.selected:after {
                content: '';
                position: absolute;
                top: 3px;
                left: 3px;
                width: 8px;
                height: 8px;
                background-color: #3B99FC;
                border-radius: 50%;
            }
            /* 新增样式：使选择位置与确认按钮在同一行 */
            .mobile-search-text-and-confirm {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-top: 10px;
            }
            .mobile-search-text-and-confirm .mobile-search-text {
                flex: 1;
                font-size: 14px;
                color: #666;
            }
            .mobile-search-text-and-confirm button {
                padding: 8px 15px;
                background-color: #3B99FC;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
            }
            /* PC端样式调整：选择位置与确认按钮 */
            .pc-search-text-and-confirm {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-top: 10px;
                margin-bottom: 10px; /* 确保与下方列表有间距 */
            }
            .pc-search-text-and-confirm .pc-search-text {
                flex: 1;
                font-size: 14px;
                color: #666;
            }
            .pc-search-text-and-confirm button {
                padding: 8px 15px;
                background-color: #3B99FC;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
            }
        `;

        // 添加样式到页面
        this.styleElement.innerHTML = css;
        document.head.appendChild(this.styleElement);
    }
    //根据位置信息初始化地图
    initMapLocation(latLng) {
        this.currentPosition = latLng;

        // 更新地图中心点
        this.map.setCenter(latLng);

        // 更新当前位置标记
        this.updateCurrentMarker(latLng);

        // 获取并显示当前地址
        this.geolocation.getAddress(latLng);
        document.getElementById(this.currentAddressId).innerText = '';
        // 搜索附近地点
        this.searchNearbyPlaces(latLng);
    }

    // 获取当前位置（优化版本）
    getCurrentLocation() {
        // 默认位置
        const default_latLng = new qq.maps.LatLng(23.38624681887071, 116.75738453865051);
        
        // 先使用默认位置快速初始化地图
        this.initMapLocationFast(default_latLng);
        
        // 异步获取真实位置
        if (navigator.geolocation) {
            const options = {
                enableHighAccuracy: false, // 降低精度要求以提升速度
                timeout: 5000, // 5秒超时
                maximumAge: 300000 // 5分钟内的缓存位置可用
            };
            
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    const latLng = new qq.maps.LatLng(position.coords.latitude, position.coords.longitude);
                    // 如果获取到真实位置，更新地图
                    this.updateToRealLocation(latLng);
                },
                (error) => {
                    console.warn('定位失败:', error.message);
                    this.updateLocationStatus('无法获取当前位置，已显示默认位置');
                },
                options
            );
        } else {
            this.updateLocationStatus('您的浏览器不支持地理定位，已显示默认位置');
        }
    }
    
    // 快速初始化地图位置（不搜索附近地点）
    initMapLocationFast(latLng) {
        this.currentPosition = latLng;
        
        // 更新地图中心点
        this.map.setCenter(latLng);
        
        // 更新当前位置标记
        this.updateCurrentMarker(latLng);
        
        // 显示默认地址信息
        this.updateLocationStatus('正在获取位置信息...');
    }
    
    // 更新到真实位置
    updateToRealLocation(latLng) {
        this.currentPosition = latLng;
        
        // 平滑移动到新位置
        this.map.panTo(latLng);
        
        // 更新位置标记
        this.updateCurrentMarker(latLng);
        
        // 获取地址信息
        if (this.geolocation) {
            this.geolocation.getAddress(latLng);
        }
        
        // 延迟搜索附近地点，避免阻塞地图显示
        setTimeout(() => {
            this.searchNearbyPlaces(latLng);
        }, 500);
    }
    
    // 更新位置状态信息
    updateLocationStatus(message) {
        const addressElement = document.getElementById(this.currentAddressId);
        if (addressElement) {
            addressElement.innerText = message;
        }
    }

    // 更新当前位置标记
    updateCurrentMarker(latLng) {
        this.currentPosition = latLng;

        // 清除之前的标记
        if(this.marker) {
            this.marker.setMap(null);
        }

        // 添加新标记
        this.marker = new qq.maps.Marker({
            position: latLng,
            map: this.map,
            icon: new qq.maps.MarkerImage(
                'https://mapapi.qq.com/web/lbs/javascriptV2/demo/img/center.gif',
                new qq.maps.Size(20, 32)
            )
        });
    }

    // 搜索附近地点
    searchNearbyPlaces(latLng) {
        let that = this
        // 使用假数据代替服务端搜索
        js.msg('wait','搜索中...');
        js.ajax("api.php?m=qqmap&a=nearby&lat="+latLng.lat+"&lng="+latLng.lng,{},function (data) {
            js.msg()
            const mockPlaces = JSON.parse(data).data
            that.displaySearchResults(mockPlaces, latLng);

            // 如果是移动端，确保弹窗显示
            if (that.isMobile) {
                that.showPopup();
            }
        })

    }

    // 关键词搜索地点
    searchPlaces(keyword) {
        const center = this.currentPosition;
        let that = this
        js.msg('wait','搜索中...');

        js.ajax("api.php?m=qqmap&a=search&lat="+center.lat+"&lng="+center.lng+"&keyword="+keyword,{},function (data) {
            js.msg()
            const mockPlaces = JSON.parse(data).data
            that.displaySearchResults(mockPlaces);
            // 如果是移动端且弹窗显示中，则获取弹窗中的搜索关键词
            if (that.isMobile) {
                const popupSearchInput = document.querySelector(`#${that.popupContainerId} input`);
                if (popupSearchInput && !keyword) {
                    keyword = popupSearchInput.value.trim();
                    if (keyword) {
                        that.displaySearchResults(mockPlaces)
                }
            }
            }
        })



    }

    // 显示弹窗
    showPopup() {
        const overlay = document.getElementById(this.overlayId);
        const popup = document.getElementById(this.popupContainerId);

        overlay.style.display = 'block';
        setTimeout(() => {
            overlay.style.opacity = '1';
            popup.style.bottom = '0';
        }, 10);

        // 焦点到弹窗内的搜索框
        const popupSearchInput = document.querySelector(`#${this.popupContainerId} input`);
        setTimeout(() => {
            popupSearchInput.focus();
        }, 300);
        
        // 移动端添加手势支持
        if (this.isMobile) {
            this.addMobileGestures(popup);
        }
    }

    // 隐藏弹窗
    hidePopup() {
        const overlay = document.getElementById(this.overlayId);
        const popup = document.getElementById(this.popupContainerId);

        overlay.style.opacity = '0';
        popup.style.bottom = '-70vh';
        setTimeout(() => {
            overlay.style.display = 'none';
            // 清理手势事件
            if (this.isMobile) {
                this.removeMobileGestures(popup);
            }
        }, 300);
    }

    // 添加移动端手势支持
    addMobileGestures(popup) {
        const content = popup.querySelector('.popup-content');
        if (!content) return;

        content.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: true });
        content.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: false });
        content.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: true });
    }

    // 移除移动端手势支持
    removeMobileGestures(popup) {
        const content = popup.querySelector('.popup-content');
        if (!content) return;

        content.removeEventListener('touchstart', this.handleTouchStart.bind(this));
        content.removeEventListener('touchmove', this.handleTouchMove.bind(this));
        content.removeEventListener('touchend', this.handleTouchEnd.bind(this));
    }

    // 处理触摸开始
    handleTouchStart(e) {
        this.touchStartY = e.touches[0].clientY;
        this.isDragging = false;
    }

    // 处理触摸移动
    handleTouchMove(e) {
        if (!this.touchStartY) return;
        
        const currentY = e.touches[0].clientY;
        const deltaY = currentY - this.touchStartY;
        
        // 向下滑动超过50px时开始拖拽效果
        if (deltaY > 50) {
            this.isDragging = true;
            const content = e.currentTarget;
            const translateY = Math.min(deltaY - 50, 200); // 最大拖拽200px
            content.style.transform = `translateY(${translateY}px)`;
            content.style.transition = 'none';
            e.preventDefault();
        }
    }

    // 处理触摸结束
    handleTouchEnd(e) {
        if (!this.touchStartY) return;
        
        const content = e.currentTarget;
        const currentY = e.changedTouches[0].clientY;
        const deltaY = currentY - this.touchStartY;
        
        // 恢复过渡动画
        content.style.transition = 'transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
        
        if (this.isDragging && deltaY > 150) {
            // 向下滑动超过150px时关闭弹窗
            this.hidePopup();
        } else {
            // 否则回弹到原位置
            content.style.transform = 'translateY(0)';
        }
        
        // 重置状态
        this.touchStartY = null;
        this.isDragging = false;
        
        // 延迟清除transform样式
        setTimeout(() => {
            if (content.style.transform === 'translateY(0)') {
                content.style.transform = '';
                content.style.transition = '';
            }
        }, 300);
    }

    // 显示搜索结果（优化版本）
    displaySearchResults(places, center) {
        // 使用requestAnimationFrame优化DOM操作
        if (this.animationFrame) {
            cancelAnimationFrame(this.animationFrame);
        }
        
        this.animationFrame = requestAnimationFrame(() => {
            this.renderSearchResults(places, center);
        });
    }

    // 渲染搜索结果
    renderSearchResults(places, center) {
        // 如果是移动端，确保弹窗显示
        if (this.isMobile) {
            this.showPopup();
        }

        // 清除地图上之前的标记（除了当前位置标记）
        if (this.markers) {
            this.markers.forEach((marker) => {
                marker.setMap(null);
            });
            this.markers = [];
        }
        
        // 批量添加地点标记
        this.addMarkersToMap(places);

        // 如果是移动端且弹窗没有显示，则显示弹窗
        if (this.isMobile) {
            this.showMobilePopup();
        }

        // 使用文档片段优化DOM操作
        this.updatePlacesList(places);
    }

    // 批量添加标记到地图
    addMarkersToMap(places) {
        places.forEach((place, index) => {
            const position = new qq.maps.LatLng(place.location.lat, place.location.lng);
            const marker = new qq.maps.Marker({
                position: position,
                map: this.map,
                title: place.title
            });
            this.markers.push(marker);

            // 为标记添加点击事件
            qq.maps.event.addListener(marker, 'click', () => {
                this.showPlaceInfo(place, marker.getPosition());
                this.currentSelectedPlace = place;
            });
        });
    }

    // 显示地点信息窗口
    showPlaceInfo(place, position) {
        const content = `
            <div style="padding: 10px; max-width: 300px;">
                <h3 style="margin-bottom: 5px; color: #333;">${place.title}</h3>
                <p style="margin-bottom: 5px; font-size: 12px; color: #666;">${place.address}</p>
                ${place.tel ? `<p style="margin-bottom: 5px; font-size: 12px; color: #666;">电话: ${place.tel}</p>` : ''}
                <p style="font-size: 12px; color: #3B99FC;">距离: ${place._distance}米</p>
            </div>
        `;

        this.infoWindow.setContent(content);
        this.infoWindow.setPosition(position);
        this.infoWindow.open();
    }

    // 显示移动端弹窗
    showMobilePopup() {
        const overlay = document.getElementById(this.overlayId);
        const popup = document.getElementById(this.popupContainerId);
        if (overlay && popup && overlay.style.display !== 'block') {
            overlay.style.display = 'block';
            // 使用requestAnimationFrame确保动画流畅
            requestAnimationFrame(() => {
                overlay.style.opacity = '1';
                popup.style.bottom = '0';
            });
        }
    }

    // 更新地点列表（使用文档片段优化）
    updatePlacesList(places) {
        const placesList = document.getElementById(this.placesListId);
        if (!placesList) return;

        // 创建文档片段
        const fragment = document.createDocumentFragment();
        
        if (places.length === 0) {
            const emptyDiv = document.createElement('div');
            emptyDiv.style.cssText = 'padding: 10px; text-align: center;';
            emptyDiv.textContent = '附近没有找到地点';
            fragment.appendChild(emptyDiv);
        } else {
            // 重置选中状态
            this.selectedPlaceIndex = -1;

            places.forEach((place, index) => {
                const placeItem = this.createPlaceItem(place, index);
                fragment.appendChild(placeItem);
            });
        }

        // 一次性更新DOM
        placesList.innerHTML = '';
        placesList.appendChild(fragment);
    }

    // 创建地点项元素
    createPlaceItem(place, index) {
        const placeItem = document.createElement('div');
        placeItem.className = 'place-item';
        placeItem.setAttribute('data-index', index);
        placeItem.setAttribute('data-lat', place.location.lat);
        placeItem.setAttribute('data-lng', place.location.lng);

        const radioBtn = document.createElement('div');
        radioBtn.className = 'radio-btn';
        
        const placeContent = document.createElement('div');
        placeContent.className = 'place-content';
        
        const placeName = document.createElement('div');
        placeName.className = 'place-name';
        placeName.textContent = place.title;
        
        const placeAddress = document.createElement('div');
        placeAddress.className = 'place-address';
        placeAddress.textContent = place.address;
        
        const placeDistance = document.createElement('div');
        placeDistance.className = 'place-distance';
        placeDistance.textContent = `距离: ${place._distance}米`;
        
        placeContent.appendChild(placeName);
        placeContent.appendChild(placeAddress);
        placeContent.appendChild(placeDistance);
        
        placeItem.appendChild(radioBtn);
        placeItem.appendChild(placeContent);

        // 添加点击事件
        placeItem.addEventListener('click', (e) => {
            this.handlePlaceItemClick(e, place, index);
        });

        return placeItem;
    }

    // 处理地点项点击事件
    handlePlaceItemClick(e, place, index) {
        const lat = parseFloat(e.currentTarget.getAttribute('data-lat'));
        const lng = parseFloat(e.currentTarget.getAttribute('data-lng'));
        const position = new qq.maps.LatLng(lat, lng);

        // 移动地图到选中的地点
        this.map.panTo(position);

        // 更新选中状态
        this.selectedPlaceIndex = index;
        this.currentSelectedPlace = place;

        // 更新单选按钮状态
        this.updateRadioButtonStates(index);

        // 高亮显示选中的地点
        this.highlightSelectedPlace(e.currentTarget);

        // 显示地点信息
        this.showPlaceInfo(place, position);
    }

    // 更新单选按钮状态
    updateRadioButtonStates(selectedIndex) {
        const radioButtons = document.querySelectorAll(`#${this.placesListId} .radio-btn`);
        radioButtons.forEach((radio, i) => {
            if (i === selectedIndex) {
                radio.classList.add('selected');
            } else {
                radio.classList.remove('selected');
            }
        });
    }

    // 高亮显示选中的地点
    highlightSelectedPlace(element) {
        element.style.backgroundColor = '#e6f2ff';
        setTimeout(() => {
            if (element && document.contains(element)) {
                element.style.backgroundColor = '';
            }
        }, 1500);
    }
    //注册确认点击函数
    confirmClick(callback) {
        this.confirmbuttonCallback = callback;
    }

    // 销毁地图组件
    destroy() {
        // 移除事件监听器
        if (this.searchBtnId && document.getElementById(this.searchBtnId)) {
            document.getElementById(this.searchBtnId).removeEventListener('click', null);
        }
        if (this.searchInputId && document.getElementById(this.searchInputId)) {
            document.getElementById(this.searchInputId).removeEventListener('keypress', null);
        }

        // 移除地图标记
        if (this.marker) {
            this.marker.setMap(null);
        }

        // 移除样式
        if (this.styleElement && document.head.contains(this.styleElement)) {
            document.head.removeChild(this.styleElement);
        }

        // 清空容器
        if (this.containerId && document.getElementById(this.containerId)) {
            document.getElementById(this.containerId).innerHTML = '';
        }
    }
}
mapManager = new MapManager();
Window.mapManager = mapManager;
// 初始化地图管理器
function initMapManager() {
    Window.mapManager.init('selectmap'); // 使用默认容器ID初始化
}

// 导出MapManager类，使其可以在其他文件中使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        MapManager,
        initMapManager
    };
}