<?php
/**
 * 快速测试标签页配置
 * 创建时间：2025-01-03
 * 用途：快速验证配置文件是否正确
 */

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>快速测试标签页配置</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .code { background: #f8f9fa; border: 1px solid #ddd; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; margin: 10px 0; }
        h1 { color: #333; text-align: center; }
        h2 { color: #666; border-bottom: 2px solid #007bff; padding-bottom: 5px; }
        .btn { display: inline-block; padding: 8px 16px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 移动端标签页快速测试</h1>
        
        <?php
        echo '<h2>1. 配置文件检查</h2>';
        
        $configFile = 'webmain/config/mobileTabsConfig.php';
        if (file_exists($configFile)) {
            echo '<div class="result success">✅ 配置文件存在</div>';
            
            try {
                $config = include $configFile;
                if (is_array($config)) {
                    echo '<div class="result success">✅ 配置文件格式正确</div>';
                    
                    // 显示分类信息
                    if (isset($config['categories'])) {
                        echo '<div class="result info">📁 分类数量: ' . count($config['categories']) . '</div>';
                        foreach ($config['categories'] as $code => $category) {
                            echo '<div class="result info">   - ' . $category['name'] . ' (' . $code . ')</div>';
                        }
                    }
                    
                    // 显示标签页信息
                    if (isset($config['tabs'])) {
                        echo '<div class="result info">🏷️ 标签页分类数量: ' . count($config['tabs']) . '</div>';
                        foreach ($config['tabs'] as $categoryCode => $tabs) {
                            if (is_array($tabs)) {
                                echo '<div class="result info">   - ' . $categoryCode . ': ' . count($tabs) . ' 个标签页</div>';
                            }
                        }
                    }
                    
                } else {
                    echo '<div class="result error">❌ 配置文件格式错误</div>';
                }
            } catch (Exception $e) {
                echo '<div class="result error">❌ 配置文件解析错误: ' . $e->getMessage() . '</div>';
            }
        } else {
            echo '<div class="result error">❌ 配置文件不存在</div>';
            echo '<div class="result info">请先运行安装脚本</div>';
        }
        
        echo '<h2>2. 必要文件检查</h2>';
        
        $files = [
            'webmain/model/mobileTabConfigModel.php' => '配置模型',
            'webmain/we/component/componentAction.php' => 'API控制器',
            'webmain/we/component/mobileTabs.js' => '前端组件',
            'webmain/system/mobiletabconfig/mobileTabConfigAction.php' => '管理控制器',
            'webmain/system/mobiletabconfig/tpl_mobileTabConfig.html' => '管理界面'
        ];
        
        foreach ($files as $file => $description) {
            if (file_exists($file)) {
                echo '<div class="result success">✅ ' . $description . ' 存在</div>';
            } else {
                echo '<div class="result error">❌ ' . $description . ' 不存在</div>';
            }
        }
        
        echo '<h2>3. 客户标签页配置预览</h2>';
        
        if (isset($config) && isset($config['tabs']['customer'])) {
            $customerTabs = $config['tabs']['customer'];
            echo '<div class="result info">客户标签页数量: ' . count($customerTabs) . '</div>';
            
            foreach ($customerTabs as $index => $tab) {
                echo '<div class="code">';
                echo '<strong>' . ($index + 1) . '. ' . $tab['tab_name'] . '</strong><br>';
                echo '代码: ' . $tab['tab_code'] . '<br>';
                echo '类型: ' . $tab['content_type'] . '<br>';
                echo '加载: ' . $tab['load_method'] . '<br>';
                if ($tab['is_default']) {
                    echo '<span style="color: #007bff;">默认标签页</span><br>';
                }
                echo '</div>';
            }
        } else {
            echo '<div class="result error">❌ 客户标签页配置不存在</div>';
        }
        
        echo '<h2>4. 测试API接口</h2>';
        
        // 测试API接口是否可访问
        $apiUrl = 'index.php?d=we&m=component&a=getMobileTabs&category_code=customer&customer_id=123';
        echo '<div class="result info">API地址: ' . $apiUrl . '</div>';
        
        // 如果是通过HTTP访问，可以测试API
        if (isset($_SERVER['HTTP_HOST'])) {
            $testUrl = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/' . $apiUrl;
            echo '<div class="result info">完整测试URL: <a href="' . $testUrl . '" target="_blank">' . $testUrl . '</a></div>';
        }
        
        echo '<h2>5. 快速操作</h2>';
        
        echo '<a href="install_mobile_tabs_config.php" class="btn">重新安装</a>';
        echo '<a href="test_mobile_tabs.html" class="btn" target="_blank">测试界面</a>';
        echo '<a href="test_config_tabs.php" class="btn" target="_blank">详细测试</a>';
        
        if (file_exists('webmain/system/mobiletabconfig/tpl_mobileTabConfig.html')) {
            echo '<a href="index.php?d=system&m=mobiletabconfig" class="btn" target="_blank">管理界面</a>';
        }
        
        // 显示配置内容（可选）
        if (isset($_GET['show_config']) && isset($config)) {
            echo '<h2>6. 完整配置内容</h2>';
            echo '<div class="code" style="max-height: 400px; overflow-y: auto;">';
            echo htmlspecialchars(json_encode($config, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            echo '</div>';
        } else {
            echo '<div style="margin-top: 20px;">';
            echo '<a href="?show_config=1" class="btn">显示完整配置</a>';
            echo '</div>';
        }
        
        echo '<h2>7. 故障排除建议</h2>';
        
        echo '<div class="result info">';
        echo '<strong>如果标签页不显示数据：</strong><br>';
        echo '1. 检查浏览器控制台是否有JavaScript错误<br>';
        echo '2. 确认AJAX请求是否返回正确数据<br>';
        echo '3. 验证客户ID参数是否正确传递<br>';
        echo '4. 检查数据库中是否有客户数据<br>';
        echo '</div>';
        
        echo '<div class="result info">';
        echo '<strong>如果AJAX请求失败：</strong><br>';
        echo '1. 确认API控制器文件存在<br>';
        echo '2. 检查客户控制器文件是否存在<br>';
        echo '3. 验证URL路径是否正确<br>';
        echo '4. 查看PHP错误日志<br>';
        echo '</div>';
        ?>
    </div>
</body>
</html>
