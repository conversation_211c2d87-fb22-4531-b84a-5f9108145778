<?php
/**
 * 简单客户标签页测试
 * 创建时间：2025-01-03
 * 用途：简单测试移动端标签页功能，不依赖复杂的数据库查询
 */

// 获取客户ID
$customerId = intval($_GET['customer_id'] ?? 123);

// 模拟客户数据
$customer = [
    'id' => $customerId,
    'name' => '测试客户公司',
    'custid' => 'KH' . date('Ymd') . str_pad($customerId, 3, '0', STR_PAD_LEFT),
    'type' => '企业客户',
    'linkname' => '张经理',
    'tel' => '13800138000',
    'address' => '北京市朝阳区建国路88号',
    'laiyuan' => '网络推广',
    'status' => '正常',
    'optdt' => date('Y-m-d H:i:s'),
    'optname' => '销售经理',
    'deptname' => '销售部',
    'email' => '<EMAIL>',
    'explain' => '这是一个重要的企业客户，有长期合作潜力，主要业务是软件开发和技术服务。'
];

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>客户详情 - <?php echo htmlspecialchars($customer['name']); ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="webmain/css/rui.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        .customer-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .customer-header h2 {
            margin: 0 0 10px 0;
            font-size: 20px;
        }
        .customer-header p {
            margin: 5px 0;
            opacity: 0.9;
            font-size: 14px;
        }
        .test-controls {
            background: white;
            padding: 15px;
            border-bottom: 1px solid #ddd;
            text-align: center;
        }
        .test-controls button {
            margin: 5px;
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-controls button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px;
            text-align: center;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.loading {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="customer-header">
        <h2><?php echo htmlspecialchars($customer['name']); ?></h2>
        <p>客户ID: <?php echo $customer['id']; ?> | 类型: <?php echo htmlspecialchars($customer['type']); ?></p>
        <p>联系人: <?php echo htmlspecialchars($customer['linkname']); ?> | 电话: <?php echo htmlspecialchars($customer['tel']); ?></p>
    </div>
    
    <div class="test-controls">
        <button onclick="testConfig()">测试配置</button>
        <button onclick="testAPI()">测试API</button>
        <button onclick="testAjaxTab('contact_record')">测试联系人</button>
        <button onclick="testAjaxTab('sales_stats')">测试统计</button>
    </div>
    
    <!-- 状态显示 -->
    <div id="status" class="status loading">准备测试...</div>
    
    <!-- 标签页容器 -->
    <div id="customerTabs" class="mobile-tabs-container" style="display: none;">
        <!-- 标签页将通过JavaScript动态生成 -->
    </div>

    <script>
        var currentCustomerId = <?php echo $customerId; ?>;
        
        function updateStatus(message, type) {
            var status = document.getElementById('status');
            status.textContent = message;
            status.className = 'status ' + (type || 'loading');
        }
        
        function testConfig() {
            updateStatus('正在测试配置...', 'loading');
            
            var xhr = new XMLHttpRequest();
            xhr.open('GET', 'check_config.php', true);
            xhr.setRequestHeader('Accept', 'application/json');
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            var response = JSON.parse(xhr.responseText);
                            if (response.success) {
                                updateStatus('配置测试成功，找到 ' + response.data.length + ' 个标签页', 'success');
                            } else {
                                updateStatus('配置测试失败：' + response.message, 'error');
                            }
                        } catch (e) {
                            updateStatus('配置解析失败：' + e.message, 'error');
                        }
                    } else {
                        updateStatus('配置请求失败：HTTP ' + xhr.status, 'error');
                    }
                }
            };
            
            xhr.send();
        }
        
        function testAPI() {
            updateStatus('正在测试API...', 'loading');
            
            var xhr = new XMLHttpRequest();
            xhr.open('GET', 'index.php?d=we&m=component&a=getMobileTabs&category_code=customer', true);
            xhr.setRequestHeader('Accept', 'application/json');
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            var response = JSON.parse(xhr.responseText);
                            if (response.success && response.data.length > 0) {
                                updateStatus('API测试成功，返回 ' + response.data.length + ' 个标签页', 'success');
                                createTabs(response.data);
                            } else {
                                updateStatus('API测试失败：' + response.message, 'error');
                                console.log('API响应：', response);
                            }
                        } catch (e) {
                            updateStatus('API响应解析失败：' + e.message, 'error');
                            console.log('原始响应：', xhr.responseText);
                        }
                    } else {
                        updateStatus('API请求失败：HTTP ' + xhr.status, 'error');
                    }
                }
            };
            
            xhr.send();
        }
        
        function testAjaxTab(tabType) {
            updateStatus('正在测试 ' + tabType + ' 标签页...', 'loading');
            
            var url = 'index.php?d=we&m=component&a=getCustomerTabs&customer_id=' + currentCustomerId + '&tab_type=' + tabType;
            
            var xhr = new XMLHttpRequest();
            xhr.open('GET', url, true);
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        var response = xhr.responseText;
                        if (response && response.trim() !== '') {
                            updateStatus(tabType + ' 标签页测试成功', 'success');
                            
                            // 显示响应内容
                            var container = document.getElementById('customerTabs');
                            container.innerHTML = '<div style="padding: 15px;"><h4>' + tabType + ' 响应内容：</h4><div style="background: #f8f9fa; padding: 10px; border-radius: 4px; max-height: 300px; overflow-y: auto;">' + response + '</div></div>';
                            container.style.display = 'block';
                        } else {
                            updateStatus(tabType + ' 标签页返回空内容', 'error');
                        }
                    } else {
                        updateStatus(tabType + ' 标签页请求失败：HTTP ' + xhr.status, 'error');
                    }
                }
            };
            
            xhr.send();
        }
        
        function createTabs(tabsData) {
            var container = document.getElementById('customerTabs');
            container.innerHTML = '';
            
            // 创建标签页导航
            var tabNav = document.createElement('div');
            tabNav.className = 'r-tabs mobile-tabs-nav';
            
            // 创建内容容器
            var contentContainer = document.createElement('div');
            contentContainer.className = 'mobile-tabs-content';
            
            tabsData.forEach(function(tab, index) {
                // 创建标签页按钮
                var tabItem = document.createElement('div');
                tabItem.className = 'r-tabs-item mobile-tab-item';
                tabItem.setAttribute('data-index', index);
                
                // 添加图标
                if (tab.tab_icon) {
                    tabItem.innerHTML = '<i class="' + tab.tab_icon + '"></i> ' + tab.tab_name;
                } else {
                    tabItem.textContent = tab.tab_name;
                }
                
                // 绑定点击事件
                tabItem.addEventListener('click', function() {
                    switchTab(index, tab);
                });
                
                tabNav.appendChild(tabItem);
                
                // 创建标签页内容区域
                var tabContent = document.createElement('div');
                tabContent.className = 'mobile-tab-content';
                tabContent.setAttribute('data-index', index);
                tabContent.innerHTML = '<div class="loading">点击加载内容...</div>';
                
                contentContainer.appendChild(tabContent);
            });
            
            container.appendChild(tabNav);
            container.appendChild(contentContainer);
            
            // 激活第一个标签页
            switchTab(0, tabsData[0]);
            
            // 显示标签页容器
            container.style.display = 'block';
        }
        
        function switchTab(index, tab) {
            // 更新导航状态
            var tabItems = document.querySelectorAll('.mobile-tab-item');
            tabItems.forEach(function(item, i) {
                if (i === index) {
                    item.classList.add('active');
                } else {
                    item.classList.remove('active');
                }
            });
            
            // 更新内容显示
            var tabContents = document.querySelectorAll('.mobile-tab-content');
            tabContents.forEach(function(content, i) {
                if (i === index) {
                    content.style.display = 'block';
                } else {
                    content.style.display = 'none';
                }
            });
            
            // 加载标签页内容
            loadTabContent(tab, index);
        }
        
        function loadTabContent(tab, index) {
            var container = document.querySelector('.mobile-tab-content[data-index="' + index + '"]');
            
            if (tab.content_type === 'html') {
                // 静态HTML内容，替换客户数据
                loadStaticContent(tab, container);
            } else if (tab.content_type === 'ajax') {
                // AJAX动态内容
                loadAjaxContent(tab, container);
            }
        }
        
        function loadStaticContent(tab, container) {
            // 客户数据（从PHP传递）
            var customerData = {
                name: '<?php echo addslashes($customer['name']); ?>',
                custid: '<?php echo addslashes($customer['custid']); ?>',
                tel: '<?php echo addslashes($customer['tel']); ?>',
                lxr: '<?php echo addslashes($customer['linkname']); ?>',
                address: '<?php echo addslashes($customer['address']); ?>',
                khlx: '<?php echo addslashes($customer['type']); ?>',
                khly: '<?php echo addslashes($customer['laiyuan']); ?>',
                khzt: '<?php echo addslashes($customer['status']); ?>',
                optdt: '<?php echo addslashes($customer['optdt']); ?>',
                optname: '<?php echo addslashes($customer['optname']); ?>',
                deptname: '<?php echo addslashes($customer['deptname']); ?>',
                email: '<?php echo addslashes($customer['email']); ?>',
                explain: '<?php echo addslashes($customer['explain']); ?>'
            };
            
            // 替换模板变量
            var content = tab.content_source;
            Object.keys(customerData).forEach(function(key) {
                var regex = new RegExp('\\{' + key + '\\}', 'g');
                content = content.replace(regex, customerData[key] || '');
            });
            
            // 处理条件显示
            if (customerData.explain && customerData.explain !== '') {
                content = content.replace(/\{if_explain\}/g, '').replace(/\{endif_explain\}/g, '');
            } else {
                content = content.replace(/\{if_explain\}[\s\S]*?\{endif_explain\}/g, '');
            }
            
            container.innerHTML = content;
        }
        
        function loadAjaxContent(tab, container) {
            container.innerHTML = '<div class="loading">正在加载...</div>';
            
            var url = tab.content_source + '&customer_id=' + currentCustomerId;
            
            var xhr = new XMLHttpRequest();
            xhr.open('GET', url, true);
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        var response = xhr.responseText;
                        container.innerHTML = response;
                    } else {
                        container.innerHTML = '<div class="error">加载失败：HTTP ' + xhr.status + '</div>';
                    }
                }
            };
            
            xhr.send();
        }
        
        // 页面加载完成后准备测试
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('页面加载完成，请点击按钮进行测试', 'success');
        });
    </script>
</body>
</html>
