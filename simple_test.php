<?php
/**
 * 简单的标签页功能测试
 * 创建时间：2025-01-03
 * 用途：最简单的功能验证，不依赖复杂环境
 */

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>简单标签页功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .code { background: #f8f9fa; border: 1px solid #ddd; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; margin: 10px 0; white-space: pre-wrap; }
        h1 { color: #333; text-align: center; }
        h2 { color: #666; border-bottom: 2px solid #007bff; padding-bottom: 5px; }
        .btn { display: inline-block; padding: 8px 16px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 简单标签页功能测试</h1>
        
        <?php
        echo '<h2>1. 基础文件检查</h2>';
        
        $files = [
            'webmain/config/mobileTabsConfig.php' => '配置文件',
            'webmain/we/component/mobileTabs.js' => '前端组件',
            'webmain/css/rui.css' => '样式文件'
        ];
        
        $allFilesExist = true;
        foreach ($files as $file => $description) {
            if (file_exists($file)) {
                echo '<div class="result success">✅ ' . $description . ' 存在</div>';
            } else {
                echo '<div class="result error">❌ ' . $description . ' 不存在</div>';
                $allFilesExist = false;
            }
        }
        
        echo '<h2>2. 配置文件内容检查</h2>';
        
        $configFile = 'webmain/config/mobileTabsConfig.php';
        if (file_exists($configFile)) {
            try {
                // 定义必要的常量
                if (!defined('HOST')) {
                    define('HOST', true);
                }
                
                $config = include $configFile;
                if (is_array($config)) {
                    echo '<div class="result success">✅ 配置文件格式正确</div>';
                    
                    // 检查客户标签页配置
                    if (isset($config['tabs']['customer']) && is_array($config['tabs']['customer'])) {
                        $customerTabs = $config['tabs']['customer'];
                        echo '<div class="result success">✅ 客户标签页配置存在，数量: ' . count($customerTabs) . '</div>';
                        
                        echo '<div class="code">';
                        echo "客户标签页列表:\n";
                        foreach ($customerTabs as $index => $tab) {
                            echo ($index + 1) . ". " . $tab['tab_name'] . " (" . $tab['tab_code'] . ")\n";
                            echo "   类型: " . $tab['content_type'] . "\n";
                            echo "   加载: " . $tab['load_method'] . "\n";
                            if ($tab['is_default']) {
                                echo "   [默认标签页]\n";
                            }
                            echo "\n";
                        }
                        echo '</div>';
                    } else {
                        echo '<div class="result error">❌ 客户标签页配置不存在</div>';
                    }
                } else {
                    echo '<div class="result error">❌ 配置文件格式错误</div>';
                }
            } catch (Exception $e) {
                echo '<div class="result error">❌ 配置文件解析错误: ' . $e->getMessage() . '</div>';
            }
        } else {
            echo '<div class="result error">❌ 配置文件不存在</div>';
        }
        
        echo '<h2>3. 直接API测试</h2>';
        
        // 创建一个简单的API测试
        if (isset($_GET['test_api'])) {
            echo '<div class="result info">正在测试API接口...</div>';
            
            try {
                // 构建API URL
                $apiUrl = 'index.php?d=we&m=component&a=getMobileTabs&category_code=customer&customer_id=123';
                
                // 使用curl或file_get_contents测试
                if (function_exists('curl_init')) {
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/' . $apiUrl);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
                    $response = curl_exec($ch);
                    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                    curl_close($ch);
                    
                    if ($response !== false) {
                        echo '<div class="result success">✅ API请求成功 (HTTP ' . $httpCode . ')</div>';
                        echo '<div class="code">' . htmlspecialchars($response) . '</div>';
                        
                        $json = json_decode($response, true);
                        if ($json !== null) {
                            echo '<div class="result success">✅ 返回有效JSON格式</div>';
                            if (isset($json['success']) && $json['success']) {
                                echo '<div class="result success">✅ API调用成功，返回 ' . count($json['data']) . ' 个标签页</div>';
                            } else {
                                echo '<div class="result error">❌ API返回错误: ' . ($json['message'] ?? '未知错误') . '</div>';
                            }
                        } else {
                            echo '<div class="result error">❌ 返回的不是有效JSON格式</div>';
                        }
                    } else {
                        echo '<div class="result error">❌ API请求失败</div>';
                    }
                } else {
                    echo '<div class="result error">❌ curl函数不可用，无法测试API</div>';
                }
            } catch (Exception $e) {
                echo '<div class="result error">❌ API测试异常: ' . $e->getMessage() . '</div>';
            }
        } else {
            echo '<div class="result info">点击下面的按钮测试API接口</div>';
            echo '<a href="?test_api=1" class="btn">测试API接口</a>';
        }
        
        echo '<h2>4. 前端组件测试</h2>';
        
        if (file_exists('webmain/we/component/mobileTabs.js')) {
            echo '<div class="result success">✅ 前端组件文件存在</div>';
            echo '<div class="result info">可以进行前端界面测试</div>';
            echo '<a href="test_mobile_tabs.html" class="btn" target="_blank">打开前端测试页面</a>';
        } else {
            echo '<div class="result error">❌ 前端组件文件不存在</div>';
        }
        
        echo '<h2>5. 问题诊断建议</h2>';
        
        if (!$allFilesExist) {
            echo '<div class="result error">';
            echo '<strong>文件缺失问题：</strong><br>';
            echo '1. 请确认所有文件已正确部署<br>';
            echo '2. 运行安装脚本重新安装<br>';
            echo '3. 检查文件路径是否正确<br>';
            echo '</div>';
        }
        
        if (!file_exists($configFile)) {
            echo '<div class="result error">';
            echo '<strong>配置文件问题：</strong><br>';
            echo '1. 运行 install_mobile_tabs_config.php 安装<br>';
            echo '2. 检查 webmain/config 目录权限<br>';
            echo '3. 手动创建配置文件<br>';
            echo '</div>';
        }
        
        echo '<div class="result info">';
        echo '<strong>下一步建议：</strong><br>';
        echo '1. 如果所有检查都通过，访问前端测试页面<br>';
        echo '2. 如果API测试失败，检查服务器配置<br>';
        echo '3. 如果前端不显示数据，检查浏览器控制台<br>';
        echo '</div>';
        
        echo '<h2>6. 快速操作</h2>';
        echo '<a href="install_mobile_tabs_config.php" class="btn">重新安装</a>';
        echo '<a href="quick_test_tabs.php" class="btn">详细检查</a>';
        echo '<a href="test_mobile_tabs.html" class="btn" target="_blank">界面测试</a>';
        
        // 显示系统信息
        echo '<h2>7. 系统信息</h2>';
        echo '<div class="code">';
        echo "PHP版本: " . PHP_VERSION . "\n";
        echo "服务器: " . ($_SERVER['SERVER_SOFTWARE'] ?? '未知') . "\n";
        echo "当前目录: " . getcwd() . "\n";
        echo "脚本路径: " . __FILE__ . "\n";
        echo "时间: " . date('Y-m-d H:i:s') . "\n";
        echo '</div>';
        ?>
    </div>
</body>
</html>
