<?php
/**
 * 测试标签页API接口
 * 创建时间：2025-01-03
 * 用途：直接测试API接口是否正常工作
 */

// 检查是否在正确的目录
if (!file_exists('config/config.php')) {
    die('请将此文件放在网站根目录下运行');
}

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 模拟系统环境
if (!defined('HOST')) {
    define('HOST', true);
}
if (!defined('ROOT_PATH')) {
    define('ROOT_PATH', dirname(__FILE__));
}

// 加载基础配置
require_once('config/config.php');

// 模拟必要的全局变量
if (!isset($GLOBALS['rock'])) {
    $GLOBALS['rock'] = (object)[
        'get' => function($key, $default = '') {
            return isset($_GET[$key]) ? $_GET[$key] : $default;
        },
        'post' => function($key, $default = '') {
            return isset($_POST[$key]) ? $_POST[$key] : $default;
        }
    ];
}

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>标签页API接口测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .result { margin: 10px 0; padding: 15px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .code { background: #f8f9fa; border: 1px solid #ddd; padding: 15px; border-radius: 4px; font-family: monospace; font-size: 12px; margin: 10px 0; white-space: pre-wrap; }
        h1 { color: #333; text-align: center; }
        h2 { color: #666; border-bottom: 2px solid #007bff; padding-bottom: 5px; }
        .btn { display: inline-block; padding: 8px 16px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .test-form { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .form-group { margin: 10px 0; }
        .form-group label { display: inline-block; width: 120px; font-weight: bold; }
        .form-group input, .form-group select { padding: 5px; border: 1px solid #ddd; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔌 标签页API接口测试</h1>
        
        <?php
        echo '<h2>1. 配置文件模型测试</h2>';
        
        try {
            // 先加载Model基类
            if (!class_exists('Model')) {
                if (file_exists('include/Model.php')) {
                    require_once('include/Model.php');
                } else {
                    // 创建简化的Model基类
                    abstract class Model {
                        public $perfix = PREFIX;
                        public $rock;
                        public $db;
                        public $adminid = 1;
                        public $adminname = 'test';

                        public function __construct() {
                            $this->rock = $GLOBALS['rock'];
                        }
                    }
                }
            }

            // 加载配置模型
            require_once('webmain/model/mobileTabConfigModel.php');
            
            $model = new mobileTabConfigClassModel();
            echo '<div class="result success">✅ 配置模型加载成功</div>';
            
            // 测试获取分类
            $categories = $model->getCategoryList(['status' => 1]);
            echo '<div class="result success">✅ 获取分类成功，数量: ' . count($categories) . '</div>';
            
            // 测试获取标签页
            $tabs = $model->getTabsByCategory(0, 'customer');
            echo '<div class="result success">✅ 获取客户标签页成功，数量: ' . count($tabs) . '</div>';
            
            // 显示标签页详情
            if (count($tabs) > 0) {
                echo '<div class="code">';
                echo "客户标签页配置:\n";
                foreach ($tabs as $index => $tab) {
                    echo ($index + 1) . ". " . $tab['tab_name'] . " (" . $tab['tab_code'] . ") - " . $tab['content_type'] . "\n";
                }
                echo '</div>';
            }
            
        } catch (Exception $e) {
            echo '<div class="result error">❌ 配置模型测试失败: ' . $e->getMessage() . '</div>';
        }
        
        echo '<h2>2. API控制器测试</h2>';
        
        try {
            // 加载API控制器
            require_once('webmain/we/component/componentAction.php');
            
            // 模拟ActionNot基类
            if (!class_exists('ActionNot')) {
                class ActionNot {
                    public $display = false;
                    public $rock;
                    public $adminid = 1;
                    
                    public function __construct() {
                        $this->rock = $GLOBALS['rock'];
                    }
                }
            }
            
            $controller = new componentClassAction();
            echo '<div class="result success">✅ API控制器加载成功</div>';
            
        } catch (Exception $e) {
            echo '<div class="result error">❌ API控制器测试失败: ' . $e->getMessage() . '</div>';
        }
        
        echo '<h2>3. API接口测试</h2>';
        
        // 测试表单
        echo '<div class="test-form">';
        echo '<h4>测试getMobileTabs接口</h4>';
        echo '<form method="GET">';
        echo '<div class="form-group">';
        echo '<label>分类代码:</label>';
        echo '<input type="text" name="category_code" value="' . ($_GET['category_code'] ?? 'customer') . '">';
        echo '</div>';
        echo '<div class="form-group">';
        echo '<label>客户ID:</label>';
        echo '<input type="text" name="customer_id" value="' . ($_GET['customer_id'] ?? '123') . '">';
        echo '</div>';
        echo '<div class="form-group">';
        echo '<label></label>';
        echo '<input type="submit" value="测试接口" class="btn">';
        echo '</div>';
        echo '</form>';
        echo '</div>';
        
        // 如果有参数，执行API测试
        if (isset($_GET['category_code'])) {
            echo '<h4>API响应结果:</h4>';
            
            try {
                // 模拟API调用
                ob_start();
                
                // 设置参数
                $_GET['category_code'] = $_GET['category_code'];
                $_GET['customer_id'] = $_GET['customer_id'] ?? '123';
                
                // 调用API方法
                if (isset($controller)) {
                    $controller->getMobileTabsAction();
                }
                
                $output = ob_get_clean();
                
                echo '<div class="code">' . htmlspecialchars($output) . '</div>';
                
                // 尝试解析JSON
                $json = json_decode($output, true);
                if ($json !== null) {
                    echo '<div class="result success">✅ JSON格式正确</div>';
                    if (isset($json['success']) && $json['success']) {
                        echo '<div class="result success">✅ API调用成功，返回 ' . count($json['data']) . ' 个标签页</div>';
                    } else {
                        echo '<div class="result error">❌ API返回错误: ' . ($json['message'] ?? '未知错误') . '</div>';
                    }
                } else {
                    echo '<div class="result error">❌ 返回的不是有效的JSON格式</div>';
                }
                
            } catch (Exception $e) {
                echo '<div class="result error">❌ API测试失败: ' . $e->getMessage() . '</div>';
            }
        }
        
        echo '<h2>4. 客户数据API测试</h2>';
        
        // 测试客户数据API
        echo '<div class="test-form">';
        echo '<h4>测试getCustomerTabs接口</h4>';
        echo '<form method="GET">';
        echo '<input type="hidden" name="category_code" value="customer">';
        echo '<div class="form-group">';
        echo '<label>客户ID:</label>';
        echo '<input type="text" name="test_customer_id" value="' . ($_GET['test_customer_id'] ?? '123') . '">';
        echo '</div>';
        echo '<div class="form-group">';
        echo '<label>标签类型:</label>';
        echo '<select name="tab_type">';
        echo '<option value="contact_record"' . (($_GET['tab_type'] ?? '') == 'contact_record' ? ' selected' : '') . '>联系记录</option>';
        echo '<option value="sales_opportunity"' . (($_GET['tab_type'] ?? '') == 'sales_opportunity' ? ' selected' : '') . '>销售机会</option>';
        echo '<option value="contract_info"' . (($_GET['tab_type'] ?? '') == 'contract_info' ? ' selected' : '') . '>合同信息</option>';
        echo '<option value="service_record"' . (($_GET['tab_type'] ?? '') == 'service_record' ? ' selected' : '') . '>服务记录</option>';
        echo '</select>';
        echo '</div>';
        echo '<div class="form-group">';
        echo '<label></label>';
        echo '<input type="submit" value="测试客户数据API" class="btn">';
        echo '</div>';
        echo '</form>';
        echo '</div>';
        
        // 如果有客户数据测试参数
        if (isset($_GET['test_customer_id']) && isset($_GET['tab_type'])) {
            echo '<h4>客户数据API响应:</h4>';
            
            try {
                ob_start();
                
                // 设置参数
                $_GET['customer_id'] = $_GET['test_customer_id'];
                $_GET['tab_type'] = $_GET['tab_type'];
                
                // 调用客户数据API
                if (isset($controller)) {
                    $controller->getCustomerTabsAction();
                }
                
                $customerOutput = ob_get_clean();
                
                echo '<div class="code">' . htmlspecialchars($customerOutput) . '</div>';
                
                if (!empty($customerOutput)) {
                    echo '<div class="result success">✅ 客户数据API有响应</div>';
                } else {
                    echo '<div class="result error">❌ 客户数据API无响应</div>';
                }
                
            } catch (Exception $e) {
                echo '<div class="result error">❌ 客户数据API测试失败: ' . $e->getMessage() . '</div>';
            }
        }
        
        echo '<h2>5. 快速操作</h2>';
        echo '<a href="quick_test_tabs.php" class="btn">配置检查</a>';
        echo '<a href="test_mobile_tabs.html" class="btn" target="_blank">界面测试</a>';
        echo '<a href="install_mobile_tabs_config.php" class="btn">重新安装</a>';
        ?>
    </div>
</body>
</html>
