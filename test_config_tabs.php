<?php
/**
 * 配置文件版本标签页功能测试
 * 创建时间：2025-01-03
 * 用途：测试基于配置文件的标签页功能
 */

// 简单的测试，不依赖完整的系统环境
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>配置文件版本标签页测试</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: #fff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-header {
            background: #007bff;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .test-section {
            padding: 20px;
            border-bottom: 1px solid #eee;
        }
        .test-section:last-child {
            border-bottom: none;
        }
        .test-section h3 {
            margin: 0 0 15px 0;
            color: #333;
        }
        .result-item {
            margin: 8px 0;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
        }
        .result-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .result-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .result-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .config-preview {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .btn {
            display: inline-block;
            padding: 8px 16px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            margin-right: 10px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #1e7e34;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h2>配置文件版本标签页功能测试</h2>
            <p>测试基于配置文件的移动端标签页功能</p>
        </div>

        <?php
        $results = [];
        
        // 1. 检查配置文件
        echo '<div class="test-section">';
        echo '<h3>📁 配置文件检查</h3>';
        
        $configFile = 'webmain/config/mobileTabsConfig.php';
        if (file_exists($configFile)) {
            echo '<div class="result-success">✅ 配置文件存在: ' . $configFile . '</div>';
            
            try {
                $config = include $configFile;
                if (is_array($config)) {
                    echo '<div class="result-success">✅ 配置文件格式正确</div>';
                    
                    // 检查分类
                    if (isset($config['categories']) && is_array($config['categories'])) {
                        $categoryCount = count($config['categories']);
                        echo '<div class="result-success">✅ 分类数量: ' . $categoryCount . '</div>';
                        
                        foreach ($config['categories'] as $code => $category) {
                            echo '<div class="result-info">   - ' . $category['name'] . ' (' . $code . ')</div>';
                        }
                    } else {
                        echo '<div class="result-error">❌ 分类配置错误</div>';
                    }
                    
                    // 检查标签页
                    if (isset($config['tabs']) && is_array($config['tabs'])) {
                        foreach ($config['tabs'] as $categoryCode => $tabs) {
                            if (is_array($tabs)) {
                                $tabCount = count($tabs);
                                echo '<div class="result-success">✅ ' . $categoryCode . ' 标签页数量: ' . $tabCount . '</div>';
                                
                                foreach ($tabs as $index => $tab) {
                                    $tabName = $tab['tab_name'] ?? '未知';
                                    $contentType = $tab['content_type'] ?? '未知';
                                    echo '<div class="result-info">   - ' . $tabName . ' (' . $contentType . ')</div>';
                                }
                            }
                        }
                    } else {
                        echo '<div class="result-error">❌ 标签页配置错误</div>';
                    }
                    
                } else {
                    echo '<div class="result-error">❌ 配置文件返回格式错误</div>';
                }
            } catch (Exception $e) {
                echo '<div class="result-error">❌ 配置文件解析错误: ' . $e->getMessage() . '</div>';
            }
        } else {
            echo '<div class="result-error">❌ 配置文件不存在: ' . $configFile . '</div>';
            echo '<div class="result-info">请先运行安装脚本: <a href="install_mobile_tabs_config.php">install_mobile_tabs_config.php</a></div>';
        }
        echo '</div>';
        
        // 2. 检查模型文件
        echo '<div class="test-section">';
        echo '<h3>🔧 模型文件检查</h3>';
        
        $modelFile = 'webmain/model/mobileTabConfigModel.php';
        if (file_exists($modelFile)) {
            echo '<div class="result-success">✅ 模型文件存在: ' . $modelFile . '</div>';
            
            // 尝试加载模型
            try {
                // 定义必要的常量和函数
                if (!defined('HOST')) {
                    define('HOST', true);
                }
                
                // 加载Model基类
                if (!class_exists('Model')) {
                    if (file_exists('include/Model.php')) {
                        // 模拟必要的全局变量
                        $GLOBALS['rock'] = (object)[
                            'adminid' => 1,
                            'adminname' => 'test'
                        ];
                        $GLOBALS['db'] = null;

                        require_once('include/Model.php');
                    } else {
                        // 如果Model.php不存在，创建简化版本
                        abstract class Model {
                            public $perfix = 'hfkj_';
                            public $rock;
                            public $db;
                            public $adminid = 1;
                            public $adminname = 'test';

                            public function __construct() {
                                $this->rock = (object)['adminid' => 1, 'adminname' => 'test'];
                            }
                        }
                    }
                }
                
                require_once($modelFile);
                
                if (class_exists('mobileTabConfigClassModel')) {
                    echo '<div class="result-success">✅ 模型类加载成功</div>';
                    
                    $model = new mobileTabConfigClassModel();
                    
                    // 测试获取分类
                    $categories = $model->getCategoryList(['status' => 1]);
                    echo '<div class="result-success">✅ 获取分类列表成功，数量: ' . count($categories) . '</div>';
                    
                    // 测试获取标签页
                    $tabs = $model->getTabsByCategory(0, 'customer');
                    echo '<div class="result-success">✅ 获取客户标签页成功，数量: ' . count($tabs) . '</div>';
                    
                    // 测试内容生成
                    if (count($tabs) > 0) {
                        $testParams = [
                            'id' => 123,
                            'name' => '测试客户',
                            'tel' => '13800138000',
                            'contview' => '<div>这是测试内容</div>'
                        ];
                        
                        $content = $model->getTabContent(0, $testParams, 'customer', 'basic_info');
                        if (!empty($content)) {
                            echo '<div class="result-success">✅ 内容生成测试成功</div>';
                        } else {
                            echo '<div class="result-error">❌ 内容生成测试失败</div>';
                        }
                    }
                    
                } else {
                    echo '<div class="result-error">❌ 模型类不存在</div>';
                }
            } catch (Exception $e) {
                echo '<div class="result-error">❌ 模型测试失败: ' . $e->getMessage() . '</div>';
            }
        } else {
            echo '<div class="result-error">❌ 模型文件不存在: ' . $modelFile . '</div>';
        }
        echo '</div>';
        
        // 3. 检查前端文件
        echo '<div class="test-section">';
        echo '<h3>🌐 前端文件检查</h3>';
        
        $frontendFiles = [
            'webmain/we/component/mobileTabs.js' => '移动端组件',
            'webmain/we/component/componentAction.php' => 'API控制器',
            'webmain/css/rui.css' => '样式文件'
        ];
        
        foreach ($frontendFiles as $file => $description) {
            if (file_exists($file)) {
                echo '<div class="result-success">✅ ' . $description . ' 存在</div>';
            } else {
                echo '<div class="result-error">❌ ' . $description . ' 不存在: ' . $file . '</div>';
            }
        }
        echo '</div>';
        
        // 4. 显示配置预览
        if (isset($config) && is_array($config)) {
            echo '<div class="test-section">';
            echo '<h3>👀 配置预览</h3>';
            echo '<div class="config-preview">';
            echo htmlspecialchars(json_encode($config, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            echo '</div>';
            echo '</div>';
        }
        
        // 5. 操作按钮
        echo '<div class="test-section">';
        echo '<h3>🚀 快速操作</h3>';
        echo '<a href="test_mobile_tabs.html" class="btn" target="_blank">测试标签页界面</a>';
        echo '<a href="install_mobile_tabs_config.php" class="btn btn-success">重新安装</a>';
        if (file_exists('webmain/system/mobiletabconfig/tpl_mobileTabConfig.html')) {
            echo '<a href="index.php?d=system&m=mobiletabconfig" class="btn" target="_blank">管理界面</a>';
        }
        echo '</div>';
        ?>
    </div>
</body>
</html>
