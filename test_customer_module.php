<?php
/**
 * 客户模块集成测试页面
 * 创建时间：2025-01-03
 * 用途：测试客户模块中的移动端标签页功能
 */

// 检查是否在正确的目录
if (!file_exists('config/config.php')) {
    die('请将此文件放在网站根目录下运行');
}

// 获取客户ID
$customerId = intval($_GET['customer_id'] ?? $_GET['mid'] ?? 123);

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>客户模块集成测试 - 客户ID: <?php echo $customerId; ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="webmain/css/rui.css">
    <script src="js/jquery.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        .test-header {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .test-header h2 {
            margin: 0 0 10px 0;
            font-size: 20px;
        }
        .test-header p {
            margin: 5px 0;
            opacity: 0.9;
            font-size: 14px;
        }
        .test-controls {
            background: white;
            padding: 15px;
            border-bottom: 1px solid #ddd;
            text-align: center;
        }
        .test-controls button {
            margin: 5px;
            padding: 8px 16px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-controls button:hover {
            background: #218838;
        }
        .test-controls input {
            margin: 5px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 100px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px;
            text-align: center;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .customer-module-container {
            background: white;
            margin: 10px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="test-header">
        <h2>客户模块集成测试</h2>
        <p>测试移动端标签页功能在客户模块中的实际应用</p>
        <p>当前客户ID: <?php echo $customerId; ?></p>
    </div>
    
    <div class="test-controls">
        <input type="number" id="customerIdInput" value="<?php echo $customerId; ?>" placeholder="客户ID">
        <button onclick="switchCustomer()">切换客户</button>
        <button onclick="testMobileView()">测试移动端</button>
        <button onclick="testDesktopView()">测试桌面端</button>
        <button onclick="openRealCustomerPage()">打开真实客户页面</button>
    </div>
    
    <!-- 状态显示 -->
    <div id="status" class="status info">准备测试客户模块集成...</div>
    
    <!-- 客户模块容器 -->
    <div class="customer-module-container">
        <iframe id="customerFrame" src="" style="width: 100%; height: 600px; border: none; display: none;"></iframe>
        
        <!-- 模拟客户详情页面 -->
        <div id="mockCustomerPage" style="display: none;">
            <!-- 这里会加载客户详情页面的内容 -->
        </div>
    </div>

    <script>
        var currentCustomerId = <?php echo $customerId; ?>;
        
        function updateStatus(message, type) {
            var status = document.getElementById('status');
            status.textContent = message;
            status.className = 'status ' + (type || 'info');
        }
        
        function switchCustomer() {
            var newCustomerId = document.getElementById('customerIdInput').value;
            if (newCustomerId && newCustomerId != currentCustomerId) {
                window.location.href = '?customer_id=' + newCustomerId;
            }
        }
        
        function testMobileView() {
            updateStatus('正在测试移动端视图...', 'info');
            
            // 模拟移动端环境
            var iframe = document.getElementById('customerFrame');
            var url = 'task.php?a=x&num=customer&mid=' + currentCustomerId;
            
            iframe.src = url;
            iframe.style.display = 'block';
            document.getElementById('mockCustomerPage').style.display = 'none';
            
            updateStatus('移动端客户详情页面已加载，请查看标签页效果', 'success');
        }
        
        function testDesktopView() {
            updateStatus('正在测试桌面端视图...', 'info');
            
            // 桌面端视图
            var iframe = document.getElementById('customerFrame');
            var url = 'task.php?a=p&num=customer&mid=' + currentCustomerId;
            
            iframe.src = url;
            iframe.style.display = 'block';
            document.getElementById('mockCustomerPage').style.display = 'none';
            
            updateStatus('桌面端客户详情页面已加载', 'success');
        }
        
        function openRealCustomerPage() {
            updateStatus('正在打开真实的客户详情页面...', 'info');
            
            // 在新窗口打开真实的客户详情页面
            var mobileUrl = 'task.php?a=x&num=customer&mid=' + currentCustomerId;
            var desktopUrl = 'task.php?a=p&num=customer&mid=' + currentCustomerId;
            
            // 检测设备类型
            var isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || window.innerWidth <= 768;
            
            var url = isMobile ? mobileUrl : desktopUrl;
            window.open(url, '_blank');
            
            updateStatus('已在新窗口打开客户详情页面', 'success');
        }
        
        function loadMockCustomerPage() {
            updateStatus('正在加载模拟客户页面...', 'info');
            
            var container = document.getElementById('mockCustomerPage');
            
            // 加载客户详情页面模板
            var xhr = new XMLHttpRequest();
            xhr.open('GET', 'webmain/flow/page/view_customer_1.html', true);
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        var content = xhr.responseText;
                        
                        // 替换模板变量
                        content = content.replace(/\{id\}/g, currentCustomerId);
                        content = content.replace(/\{name\}/g, '测试客户公司');
                        content = content.replace(/\{custid\}/g, 'KH' + currentCustomerId);
                        content = content.replace(/\{type\}/g, '企业客户');
                        content = content.replace(/\{linkname\}/g, '张经理');
                        content = content.replace(/\{tel\}/g, '13800138000');
                        content = content.replace(/\{address\}/g, '北京市朝阳区');
                        content = content.replace(/\{status\}/g, '正常');
                        
                        container.innerHTML = content;
                        container.style.display = 'block';
                        document.getElementById('customerFrame').style.display = 'none';
                        
                        updateStatus('模拟客户页面加载成功，请查看移动端标签页效果', 'success');
                    } else {
                        updateStatus('模拟客户页面加载失败：HTTP ' + xhr.status, 'error');
                    }
                }
            };
            
            xhr.send();
        }
        
        function testAPI() {
            updateStatus('正在测试API接口...', 'info');
            
            var xhr = new XMLHttpRequest();
            xhr.open('GET', 'index.php?d=we&m=component&a=getMobileTabs&category_code=customer', true);
            xhr.setRequestHeader('Accept', 'application/json');
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            var response = JSON.parse(xhr.responseText);
                            if (response.success && response.data.length > 0) {
                                updateStatus('API测试成功，返回 ' + response.data.length + ' 个标签页配置', 'success');
                            } else {
                                updateStatus('API测试失败：' + response.message, 'error');
                            }
                        } catch (e) {
                            updateStatus('API响应解析失败：' + e.message, 'error');
                        }
                    } else {
                        updateStatus('API请求失败：HTTP ' + xhr.status, 'error');
                    }
                }
            };
            
            xhr.send();
        }
        
        // 页面加载完成后自动测试API
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('页面加载完成，正在测试API...', 'info');
            testAPI();
        });
    </script>
</body>
</html>
