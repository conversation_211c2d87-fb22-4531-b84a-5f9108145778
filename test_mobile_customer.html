<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>移动端客户详情测试</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="webmain/css/rui.css">
    <script src="js/jquery.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        .test-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .test-header h2 {
            margin: 0 0 10px 0;
            font-size: 18px;
        }
        .test-header p {
            margin: 5px 0;
            opacity: 0.9;
            font-size: 14px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px;
            text-align: center;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-header">
        <h2>移动端客户详情测试</h2>
        <p>测试修复后的移动端标签页功能</p>
        <p>客户ID: 77 (测试数字ID)</p>
    </div>
    
    <!-- 状态显示 -->
    <div id="status" class="status info">正在初始化...</div>
    
    <!-- 移动端优化的标签页容器 -->
    <div id="mobileCustomerTabs" class="mobile-tabs-container">
        <!-- 标签页将通过JavaScript动态生成 -->
    </div>

    <!-- 原有的PC版标签页（在移动端隐藏） -->
    <div class="r-tabs desktop-only" tabid="a">
        <div index="0" class="r-tabs-item active">
        客户资料
        </div>
        <div index="7" custid="77" class="r-tabs-item">
        跟进计划
        </div>
        <div index="1" custid="77" class="r-tabs-item">
        销售机会
        </div>
        <div index="2" custid="77" class="r-tabs-item">
        客户合同
        </div>
        <div index="5" custid="77" class="r-tabs-item">
        售后单
        </div>
        <div index="3" custid="77" class="r-tabs-item">
        收款单
        </div>
        <div index="4" custid="77" class="r-tabs-item">
        付款单
        </div>
        <div index="6" custid="77" class="r-tabs-item">
        销售单
        </div>
    </div>

    <!--移动端结构化客户详情-->
    <div tabitem="0" tabid="a" class="customer-detail-content">

    <div class="section-title">基本信息</div>
    <table>
    <tr><td>客户编号</td><td>：</td><td>KH202501077</td></tr>
    <tr><td>客户类型</td><td>：</td><td>企业客户</td></tr>
    <tr><td>客户名称</td><td>：</td><td><strong>测试客户公司</strong></td></tr>
    <tr><td>单位名称</td><td>：</td><td>测试科技有限公司</td></tr>
    <tr><td>上级单位</td><td>：</td><td>测试集团</td></tr>
    <tr><td>客户状态</td><td>：</td><td>正常</td></tr>
    </table>

    <div class="section-title">联系信息</div>
    <table>
    <tr><td>联系人</td><td>：</td><td>张经理</td></tr>
    <tr><td>联系电话</td><td>：</td><td>13800138000</td></tr>
    <tr><td>邮箱地址</td><td>：</td><td><EMAIL></td></tr>
    <tr><td>详细地址</td><td>：</td><td>北京市朝阳区建国路88号</td></tr>
    <tr><td>交通路线</td><td>：</td><td>地铁1号线建国门站</td></tr>
    </table>

    <div class="section-title">业务信息</div>
    <table>
    <tr><td>客户来源</td><td>：</td><td>网络推广</td></tr>
    <tr><td>所属部门</td><td>：</td><td>销售部</td></tr>
    <tr><td>负责人</td><td>：</td><td>销售经理</td></tr>
    <tr><td>创建时间</td><td>：</td><td>2025-01-03 10:30:00</td></tr>
    </table>

    <div class="section-title">客户说明</div>
    <div class="customer-explain">
        这是一个重要的企业客户，主要从事软件开发和技术服务。有长期合作潜力，需要重点维护。
    </div>

    </div>

    <!-- 移动端标签页初始化脚本 -->
    <script>
    $(document).ready(function() {
        updateStatus('页面加载完成，正在检测设备类型...', 'info');
        
        // 检测是否为移动端
        var isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || window.innerWidth <= 768;

        if (isMobile) {
            updateStatus('检测到移动端设备，正在初始化标签页...', 'info');
            
            // 隐藏PC版标签页
            $('.desktop-only').hide();

            // 初始化移动端标签页
            var customerId = parseInt('77') || 0; // 测试客户ID
            initMobileCustomerTabs(customerId);
        } else {
            updateStatus('检测到桌面端设备，显示传统界面', 'info');
            
            // PC端隐藏移动端容器
            $('#mobileCustomerTabs').hide();
            $('.desktop-only').show();
        }
    });

    function updateStatus(message, type) {
        var status = document.getElementById('status');
        status.textContent = message;
        status.className = 'status ' + (type || 'info');
    }

    function initMobileCustomerTabs(customerId) {
        updateStatus('正在获取标签页配置...', 'info');
        
        // 获取标签页配置
        $.ajax({
            url: 'index.php?d=we&m=component&a=getMobileTabs&category_code=customer',
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.success && response.data.length > 0) {
                    updateStatus('标签页配置获取成功，共 ' + response.data.length + ' 个标签页', 'success');
                    createMobileCustomerTabs(response.data, customerId);
                } else {
                    updateStatus('标签页配置获取失败：' + response.message, 'error');
                    console.error('获取标签页配置失败：', response.message);
                    // 降级到原有显示方式
                    $('#mobileCustomerTabs').html('<div class="error">标签页配置加载失败</div>');
                }
            },
            error: function(xhr, status, error) {
                updateStatus('标签页配置请求失败：' + error, 'error');
                console.error('标签页配置请求失败：', error);
                // 降级到原有显示方式
                $('#mobileCustomerTabs').html('<div class="error">标签页配置请求失败</div>');
            }
        });
    }

    function createMobileCustomerTabs(tabsData, customerId) {
        var container = $('#mobileCustomerTabs');
        container.empty();

        // 创建标签页导航
        var tabNav = $('<div class="r-tabs mobile-tabs-nav">');

        // 创建内容容器
        var contentContainer = $('<div class="mobile-tabs-content">');

        tabsData.forEach(function(tab, index) {
            // 创建标签页按钮
            var tabItem = $('<div class="r-tabs-item mobile-tab-item">');
            tabItem.attr('data-index', index);

            // 添加图标
            if (tab.tab_icon) {
                tabItem.html('<i class="' + tab.tab_icon + '"></i> ' + tab.tab_name);
            } else {
                tabItem.text(tab.tab_name);
            }

            // 绑定点击事件
            tabItem.click(function() {
                switchMobileTab(index, tab, customerId);
            });

            tabNav.append(tabItem);

            // 创建标签页内容区域
            var tabContent = $('<div class="mobile-tab-content">');
            tabContent.attr('data-index', index);
            tabContent.html('<div class="loading">加载中...</div>');

            contentContainer.append(tabContent);
        });

        container.append(tabNav);
        container.append(contentContainer);

        // 激活第一个标签页
        switchMobileTab(0, tabsData[0], customerId);
        
        updateStatus('移动端标签页初始化完成！', 'success');
    }

    function switchMobileTab(index, tab, customerId) {
        // 更新导航状态
        $('.mobile-tab-item').removeClass('active');
        $('.mobile-tab-item[data-index="' + index + '"]').addClass('active');

        // 更新内容显示
        $('.mobile-tab-content').hide();
        var currentContent = $('.mobile-tab-content[data-index="' + index + '"]');
        currentContent.show();

        // 加载标签页内容
        loadMobileTabContent(tab, customerId, currentContent);
    }

    function loadMobileTabContent(tab, customerId, container) {
        if (tab.content_type === 'html') {
            // 静态HTML内容，替换客户数据
            loadStaticMobileContent(tab, customerId, container);
        } else if (tab.content_type === 'ajax') {
            // AJAX动态内容
            loadAjaxMobileContent(tab, customerId, container);
        }
    }

    function loadStaticMobileContent(tab, customerId, container) {
        // 获取客户详细信息（测试数据）
        var customerData = {
            name: '测试客户公司' || '',
            tel: '13800138000' || '',
            lxr: '张经理' || '',
            address: '北京市朝阳区建国路88号' || '',
            khlx: '企业客户' || '',
            khly: '网络推广' || '',
            khzt: '正常' || '',
            optdt: '2025-01-03 10:30:00' || '',
            explain: '这是一个重要的企业客户，主要从事软件开发和技术服务。' || ''
        };

        // 替换模板变量
        var content = tab.content_source;
        Object.keys(customerData).forEach(function(key) {
            var regex = new RegExp('\\{' + key + '\\}', 'g');
            content = content.replace(regex, customerData[key] || '');
        });

        // 处理条件显示
        if (customerData.explain && customerData.explain !== '') {
            content = content.replace(/\{if_explain\}/g, '').replace(/\{endif_explain\}/g, '');
        } else {
            content = content.replace(/\{if_explain\}[\s\S]*?\{endif_explain\}/g, '');
        }

        container.html(content);
    }

    function loadAjaxMobileContent(tab, customerId, container) {
        container.html('<div class="loading">正在加载...</div>');

        $.ajax({
            url: tab.content_source + '&customer_id=' + customerId,
            type: 'GET',
            success: function(data) {
                container.html(data);
            },
            error: function(xhr, status, error) {
                container.html('<div class="error">加载失败：' + error + '</div>');
            }
        });
    }
    </script>
</body>
</html>
