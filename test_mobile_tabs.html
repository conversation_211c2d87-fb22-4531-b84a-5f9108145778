<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>移动端标签页功能测试</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" type="text/css" href="webmain/css/rui.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 400px;
            margin: 0 auto;
            background: #fff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-header {
            background: #007bff;
            color: white;
            padding: 15px;
            text-align: center;
        }
        .test-info {
            padding: 15px;
            background: #f8f9fa;
            border-bottom: 1px solid #ddd;
        }
        .test-info h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .test-info p {
            margin: 5px 0;
            color: #666;
            font-size: 14px;
        }
        .debug-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            max-width: 300px;
            z-index: 1000;
        }
        .debug-toggle {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 50px;
            cursor: pointer;
            z-index: 1001;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h3>移动端标签页测试</h3>
        </div>
        
        <div class="test-info">
            <h4>测试客户信息</h4>
            <p><strong>客户ID:</strong> <span id="testCustomerId">123</span></p>
            <p><strong>客户名称:</strong> 测试客户公司</p>
            <p><strong>联系电话:</strong> ***********</p>
            <p><strong>联系人:</strong> 张经理</p>
        </div>
        
        <!-- 动态标签页容器 -->
        <div class="mobile-tabs-container" id="testTabs">
            <!-- 标签页将通过JavaScript动态生成 -->
        </div>
    </div>

    <!-- 调试信息 -->
    <div class="debug-info" id="debugInfo" style="display: none;">
        <div><strong>调试信息:</strong></div>
        <div id="debugContent">等待初始化...</div>
    </div>
    
    <button class="debug-toggle" onclick="toggleDebug()">调试</button>

    <!-- 引入必要的JavaScript文件 -->
    <script src="js/jquery.js"></script>
    <script>
        // 模拟js对象和方法
        if (typeof js === 'undefined') {
            window.js = {
                getajaxurl: function(action, controller, module) {
                    return 'index.php?d=' + module + '&m=' + controller + '&a=' + action;
                }
            };
        }


    </script>
    <script src="webmain/we/component/mobileTabs.js"></script>

    <script>
        var debugVisible = false;
        var tabsInstance = null;

        // 页面加载完成后初始化标签页
        $(document).ready(function() {
            initTestTabs();
        });

        function initTestTabs() {
            updateDebug('开始初始化标签页...');

            // 先检查配置
            $.ajax({
                url: 'check_config.php',
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    updateDebug('配置检查成功，标签页数量：' + response.data.length);

                    if (response.success && response.data.length > 0) {
                        // 使用配置检查返回的数据初始化标签页
                        initTabsWithData(response.data);
                    } else {
                        updateDebug('配置检查失败：' + response.message);
                        $('#testTabs').html('<div class="error">配置检查失败：' + response.message + '</div>');
                    }
                },
                error: function(xhr, status, error) {
                    updateDebug('配置检查请求失败：' + error);
                    // 尝试使用原始方法
                    initTabsOriginal();
                }
            });
        }

        function initTabsWithData(tabsData) {
            updateDebug('使用配置数据初始化标签页...');

            try {
                // 手动创建标签页结构
                var container = $('#testTabs');
                container.empty();

                // 创建标签页导航
                var tabNav = $('<div class="r-tabs" tabid="test-dynamic">');
                $.each(tabsData, function(index, tab) {
                    var tabItem = $('<div class="r-tabs-item" index="' + index + '">');

                    // 添加图标
                    if (tab.tab_icon) {
                        tabItem.append('<i class="' + tab.tab_icon + '"></i> ');
                    }

                    tabItem.append(tab.tab_name);
                    tabNav.append(tabItem);
                });

                container.append(tabNav);

                // 创建标签页内容区域
                $.each(tabsData, function(index, tab) {
                    var tabContent = $('<div class="tab-content" tabitem="' + index + '" tabid="test-dynamic" style="display:none;">');

                    if (tab.content_type === 'html') {
                        // 处理HTML内容
                        var content = tab.content_source;
                        // 替换测试数据
                        content = content.replace(/\{name\}/g, '测试客户公司');
                        content = content.replace(/\{tel\}/g, '***********');
                        content = content.replace(/\{lxr\}/g, '张经理');
                        content = content.replace(/\{contview\}/g, '<div>这是测试的客户详情内容</div>');
                        tabContent.html(content);
                    } else {
                        tabContent.html('<div class="info">这是 ' + tab.tab_name + ' 的内容（' + tab.content_type + '类型）</div>');
                    }

                    container.append(tabContent);
                });

                // 绑定点击事件
                container.on('click', '.r-tabs-item', function() {
                    var index = parseInt($(this).attr('index'));
                    switchTab(index);
                });

                // 激活第一个标签页
                switchTab(0);

                updateDebug('标签页初始化成功');

            } catch (error) {
                updateDebug('标签页初始化失败：' + error.message);
                console.error('标签页初始化失败：', error);
            }
        }

        function switchTab(index) {
            var container = $('#testTabs');

            // 更新导航状态
            container.find('.r-tabs-item').removeClass('active');
            container.find('.r-tabs-item[index="' + index + '"]').addClass('active');

            // 更新内容显示
            container.find('.tab-content').hide();
            container.find('.tab-content[tabitem="' + index + '"]').show();

            updateDebug('切换到标签页索引：' + index);
        }

        function initTabsOriginal() {
            updateDebug('使用原始方法初始化标签页...');

            // 获取测试客户ID
            var customerId = $('#testCustomerId').text();

            try {
                // 初始化标签页配置
                tabsInstance = initMobileTabs({
                    container: '#testTabs',
                    categoryCode: 'customer', // 客户详情分类
                    relationData: {
                        customer_id: customerId,
                        id: customerId,
                        name: '测试客户公司',
                        tel: '***********',
                        lxr: '张经理',
                        address: '北京市朝阳区',
                        khlx: '企业客户',
                        khly: '网络推广',
                        khzt: '正常',
                        optdt: new Date().toLocaleString(),
                        explain: '这是一个测试客户的说明信息'
                    },
                    defaultTab: 0,
                    onTabChange: function(index, tab) {
                        updateDebug('切换到标签页：' + tab.tab_name + ' (索引:' + index + ')');
                        console.log('切换到标签页：', tab.tab_name);
                    },
                    onTabLoad: function(index, tab) {
                        updateDebug('标签页加载完成：' + tab.tab_name);
                        console.log('标签页加载完成：', tab.tab_name);
                    }
                });

                updateDebug('标签页初始化成功');

                // 将实例保存到全局，方便调试
                window.testTabsInstance = tabsInstance;

            } catch (error) {
                updateDebug('标签页初始化失败：' + error.message);
                console.error('标签页初始化失败：', error);
            }
        }

        function updateDebug(message) {
            var now = new Date().toLocaleTimeString();
            var debugContent = $('#debugContent');
            var currentContent = debugContent.html();
            
            if (currentContent === '等待初始化...') {
                debugContent.html('[' + now + '] ' + message);
            } else {
                debugContent.html(currentContent + '<br>[' + now + '] ' + message);
            }
            
            // 限制调试信息行数
            var lines = debugContent.html().split('<br>');
            if (lines.length > 10) {
                lines = lines.slice(-10);
                debugContent.html(lines.join('<br>'));
            }
        }

        function toggleDebug() {
            debugVisible = !debugVisible;
            $('#debugInfo').toggle(debugVisible);
        }

        // 提供给外部调用的测试方法
        window.testMobileTabs = {
            refreshCurrentTab: function() {
                if (tabsInstance) {
                    tabsInstance.refreshCurrentTab();
                    updateDebug('刷新当前标签页');
                }
            },
            
            switchToTab: function(index) {
                if (tabsInstance) {
                    tabsInstance.switchTab(index);
                    updateDebug('切换到标签页索引：' + index);
                }
            },
            
            getCurrentTab: function() {
                if (tabsInstance) {
                    var tab = tabsInstance.getCurrentTab();
                    updateDebug('当前标签页：' + (tab ? tab.tab_name : '无'));
                    return tab;
                }
            },
            
            getTabsData: function() {
                if (tabsInstance) {
                    updateDebug('标签页数量：' + tabsInstance.tabs.length);
                    return tabsInstance.tabs;
                }
            }
        };

        // 监听错误
        window.addEventListener('error', function(e) {
            updateDebug('JavaScript错误：' + e.message);
        });

        // AJAX错误处理
        $(document).ajaxError(function(event, xhr, settings, error) {
            updateDebug('AJAX错误：' + error + ' (URL: ' + settings.url + ')');
        });
    </script>
</body>
</html>
