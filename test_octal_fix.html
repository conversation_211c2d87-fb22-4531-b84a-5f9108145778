<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>八进制数字问题测试</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        .test-header {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .test-case {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .test-case h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .code {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
        .result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="test-header">
        <h2>八进制数字问题测试</h2>
        <p>测试不同客户ID格式的JavaScript解析问题</p>
    </div>
    
    <div class="test-case">
        <h4>测试案例1：普通数字ID (77)</h4>
        <div class="code">var customerId = parseInt(77) || 0;</div>
        <div id="result1" class="result"></div>
    </div>
    
    <div class="test-case">
        <h4>测试案例2：八进制格式ID (077) - 会出错</h4>
        <div class="code">var customerId = parseInt(077) || 0;</div>
        <div id="result2" class="result"></div>
    </div>
    
    <div class="test-case">
        <h4>测试案例3：字符串格式ID ('77')</h4>
        <div class="code">var customerId = parseInt('77') || 0;</div>
        <div id="result3" class="result"></div>
    </div>
    
    <div class="test-case">
        <h4>测试案例4：字符串格式ID ('077')</h4>
        <div class="code">var customerId = parseInt('077') || 0;</div>
        <div id="result4" class="result"></div>
    </div>
    
    <div class="test-case">
        <h4>测试案例5：安全解析 ('077', 10)</h4>
        <div class="code">var customerId = parseInt('077', 10) || 0;</div>
        <div id="result5" class="result"></div>
    </div>
    
    <div class="test-case">
        <h4>测试案例6：我们的修复方案</h4>
        <div class="code">
            var customerId = '077';<br>
            customerId = parseInt(customerId, 10) || 0;
        </div>
        <div id="result6" class="result"></div>
    </div>

    <script>
        function testCase(caseNumber, testFunction, description) {
            var resultElement = document.getElementById('result' + caseNumber);
            try {
                var result = testFunction();
                resultElement.className = 'result success';
                resultElement.innerHTML = '<strong>成功:</strong> ' + description + ' = ' + result;
            } catch (error) {
                resultElement.className = 'result error';
                resultElement.innerHTML = '<strong>错误:</strong> ' + error.message;
            }
        }
        
        // 测试案例1：普通数字
        testCase(1, function() {
            var customerId = parseInt(77) || 0;
            return customerId;
        }, 'customerId');
        
        // 测试案例2：八进制数字（会出错）
        testCase(2, function() {
            // 这里会导致语法错误，因为077在严格模式下是非法的
            // 我们用eval来模拟这种情况
            try {
                eval('var customerId = parseInt(077) || 0;');
                return 'unexpected success';
            } catch (e) {
                throw new Error('八进制字面量在严格模式下不被允许');
            }
        }, 'customerId');
        
        // 测试案例3：字符串格式
        testCase(3, function() {
            var customerId = parseInt('77') || 0;
            return customerId;
        }, 'customerId');
        
        // 测试案例4：字符串八进制格式
        testCase(4, function() {
            var customerId = parseInt('077') || 0;
            return customerId + ' (注意：这会被解析为八进制，结果是63)';
        }, 'customerId');
        
        // 测试案例5：安全解析
        testCase(5, function() {
            var customerId = parseInt('077', 10) || 0;
            return customerId + ' (强制十进制解析)';
        }, 'customerId');
        
        // 测试案例6：我们的修复方案
        testCase(6, function() {
            var customerId = '077';
            customerId = parseInt(customerId, 10) || 0;
            return customerId + ' (安全的两步解析)';
        }, 'customerId');
    </script>
</body>
</html>
