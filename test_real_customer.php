<?php
/**
 * 真实客户数据测试页面
 * 创建时间：2025-01-03
 * 用途：使用真实的客户数据测试移动端标签页功能
 */

// 检查是否在正确的目录
if (!file_exists('config/config.php')) {
    die('请将此文件放在网站根目录下运行');
}

// 加载系统配置
require_once('config/config.php');

// 初始化数据库连接
if (!isset($db) || !$db) {
    // 创建数据库连接
    $dbClass = DB_DRIVE . 'Class';
    $dbFile = 'include/class/' . $dbClass . '.php';

    if (file_exists($dbFile)) {
        require_once($dbFile);
        $db = new $dbClass();
        $db->changeattr(DB_HOST, DB_USER, DB_PASS, DB_BASE);
        $db->connectdb();
    } else {
        die('数据库连接失败：找不到数据库类文件');
    }
}

if (!$db || !method_exists($db, 'isconnect') || !$db->isconnect()) {
    die('数据库连接失败');
}

// 获取客户ID
$customerId = intval($_GET['customer_id'] ?? $_GET['mid'] ?? 0);

// 如果没有指定客户ID，获取第一个客户
if ($customerId <= 0) {
    $customerList = $db->getall("SELECT id, name FROM " . PREFIX . "customer WHERE status != '已删除' ORDER BY id DESC LIMIT 5");
    if (empty($customerList)) {
        die('系统中没有客户数据，请先添加客户');
    }
    $customerId = $customerList[0]['id'];
}

// 获取客户详细信息
$customer = $db->getone("SELECT * FROM " . PREFIX . "customer WHERE id = {$customerId}");
if (!$customer) {
    die('客户不存在');
}

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>客户详情 - <?php echo htmlspecialchars($customer['name']); ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="webmain/css/rui.css">
    <script src="js/jquery.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        .customer-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .customer-header h2 {
            margin: 0 0 10px 0;
            font-size: 20px;
        }
        .customer-header p {
            margin: 5px 0;
            opacity: 0.9;
            font-size: 14px;
        }
        .customer-selector {
            background: white;
            padding: 15px;
            border-bottom: 1px solid #ddd;
        }
        .customer-selector select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px;
            text-align: center;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.loading {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="customer-header">
        <h2><?php echo htmlspecialchars($customer['name']); ?></h2>
        <p>客户ID: <?php echo $customer['id']; ?> | 类型: <?php echo htmlspecialchars($customer['type']); ?></p>
        <p>联系人: <?php echo htmlspecialchars($customer['linkname']); ?> | 电话: <?php echo htmlspecialchars($customer['tel']); ?></p>
    </div>
    
    <div class="customer-selector">
        <label for="customerSelect">切换客户测试：</label>
        <select id="customerSelect" onchange="switchCustomer(this.value)">
            <?php
            $customerList = $db->getall("SELECT id, name, type FROM " . PREFIX . "customer WHERE status != '已删除' ORDER BY id DESC LIMIT 20");
            foreach ($customerList as $cust) {
                $selected = ($cust['id'] == $customerId) ? 'selected' : '';
                echo '<option value="' . $cust['id'] . '" ' . $selected . '>' . htmlspecialchars($cust['name']) . ' (ID:' . $cust['id'] . ')</option>';
            }
            ?>
        </select>
    </div>
    
    <!-- 状态显示 -->
    <div id="status" class="status loading">正在加载标签页...</div>
    
    <!-- 标签页容器 -->
    <div id="customerTabs" class="mobile-tabs-container" style="display: none;">
        <!-- 标签页将通过JavaScript动态生成 -->
    </div>

    <script>
        var currentCustomerId = <?php echo $customerId; ?>;
        
        function switchCustomer(customerId) {
            if (customerId && customerId != currentCustomerId) {
                window.location.href = '?customer_id=' + customerId;
            }
        }
        
        function updateStatus(message, type) {
            var status = document.getElementById('status');
            status.textContent = message;
            status.className = 'status ' + (type || 'loading');
        }
        
        function createTabs(tabsData) {
            var container = document.getElementById('customerTabs');
            container.innerHTML = '';
            
            // 创建标签页导航
            var tabNav = document.createElement('div');
            tabNav.className = 'r-tabs mobile-tabs-nav';
            
            // 创建内容容器
            var contentContainer = document.createElement('div');
            contentContainer.className = 'mobile-tabs-content';
            
            tabsData.forEach(function(tab, index) {
                // 创建标签页按钮
                var tabItem = document.createElement('div');
                tabItem.className = 'r-tabs-item mobile-tab-item';
                tabItem.setAttribute('data-index', index);
                
                // 添加图标
                if (tab.tab_icon) {
                    tabItem.innerHTML = '<i class="' + tab.tab_icon + '"></i> ' + tab.tab_name;
                } else {
                    tabItem.textContent = tab.tab_name;
                }
                
                // 绑定点击事件
                tabItem.addEventListener('click', function() {
                    switchTab(index, tab);
                });
                
                tabNav.appendChild(tabItem);
                
                // 创建标签页内容区域
                var tabContent = document.createElement('div');
                tabContent.className = 'mobile-tab-content';
                tabContent.setAttribute('data-index', index);
                tabContent.innerHTML = '<div class="loading">点击加载内容...</div>';
                
                contentContainer.appendChild(tabContent);
            });
            
            container.appendChild(tabNav);
            container.appendChild(contentContainer);
            
            // 激活第一个标签页
            switchTab(0, tabsData[0]);
            
            // 显示标签页容器
            container.style.display = 'block';
        }
        
        function switchTab(index, tab) {
            // 更新导航状态
            var tabItems = document.querySelectorAll('.mobile-tab-item');
            tabItems.forEach(function(item, i) {
                if (i === index) {
                    item.classList.add('active');
                } else {
                    item.classList.remove('active');
                }
            });
            
            // 更新内容显示
            var tabContents = document.querySelectorAll('.mobile-tab-content');
            tabContents.forEach(function(content, i) {
                if (i === index) {
                    content.style.display = 'block';
                } else {
                    content.style.display = 'none';
                }
            });
            
            // 加载标签页内容
            loadTabContent(tab, index);
        }
        
        function loadTabContent(tab, index) {
            var container = document.querySelector('.mobile-tab-content[data-index="' + index + '"]');
            
            if (tab.content_type === 'html') {
                // 静态HTML内容，替换客户数据
                loadStaticContent(tab, container);
            } else if (tab.content_type === 'ajax') {
                // AJAX动态内容
                loadAjaxContent(tab, container);
            }
        }
        
        function loadStaticContent(tab, container) {
            // 客户数据（从PHP传递）
            var customerData = {
                name: '<?php echo addslashes($customer['name']); ?>',
                custid: '<?php echo addslashes($customer['custid']); ?>',
                tel: '<?php echo addslashes($customer['tel']); ?>',
                lxr: '<?php echo addslashes($customer['linkname']); ?>',
                address: '<?php echo addslashes($customer['address']); ?>',
                khlx: '<?php echo addslashes($customer['type']); ?>',
                khly: '<?php echo addslashes($customer['laiyuan']); ?>',
                khzt: '<?php echo addslashes($customer['status']); ?>',
                optdt: '<?php echo addslashes($customer['optdt']); ?>',
                optname: '<?php echo addslashes($customer['optname']); ?>',
                deptname: '<?php echo addslashes($customer['deptname']); ?>',
                email: '<?php echo addslashes($customer['email']); ?>',
                explain: '<?php echo addslashes($customer['explain']); ?>'
            };
            
            // 替换模板变量
            var content = tab.content_source;
            Object.keys(customerData).forEach(function(key) {
                var regex = new RegExp('\\{' + key + '\\}', 'g');
                content = content.replace(regex, customerData[key] || '');
            });
            
            // 处理条件显示
            if (customerData.explain && customerData.explain !== '') {
                content = content.replace(/\{if_explain\}/g, '').replace(/\{endif_explain\}/g, '');
            } else {
                content = content.replace(/\{if_explain\}[\s\S]*?\{endif_explain\}/g, '');
            }
            
            container.innerHTML = content;
        }
        
        function loadAjaxContent(tab, container) {
            container.innerHTML = '<div class="loading">正在加载...</div>';
            
            var url = tab.content_source + '&customer_id=' + currentCustomerId;
            
            var xhr = new XMLHttpRequest();
            xhr.open('GET', url, true);
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        var response = xhr.responseText;
                        container.innerHTML = response;
                    } else {
                        container.innerHTML = '<div class="error">加载失败：HTTP ' + xhr.status + '</div>';
                    }
                }
            };
            
            xhr.send();
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('正在加载标签页配置...', 'loading');
            
            var xhr = new XMLHttpRequest();
            xhr.open('GET', 'index.php?d=we&m=component&a=getMobileTabs&category_code=customer', true);
            xhr.setRequestHeader('Accept', 'application/json');
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            var response = JSON.parse(xhr.responseText);
                            if (response.success && response.data.length > 0) {
                                updateStatus('标签页加载成功，共 ' + response.data.length + ' 个标签页', 'success');
                                createTabs(response.data);
                            } else {
                                updateStatus('标签页配置失败：' + response.message, 'error');
                            }
                        } catch (e) {
                            updateStatus('标签页配置解析失败：' + e.message, 'error');
                        }
                    } else {
                        updateStatus('标签页配置请求失败：HTTP ' + xhr.status, 'error');
                    }
                }
            };
            
            xhr.send();
        });
    </script>
</body>
</html>
