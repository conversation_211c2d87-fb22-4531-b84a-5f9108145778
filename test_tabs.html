<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>客户标签页测试</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: #fff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background: #007bff;
            color: white;
            padding: 15px;
            text-align: center;
        }
        .customer-info {
            padding: 15px;
            background: #f8f9fa;
            border-bottom: 1px solid #ddd;
        }
        .customer-info h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .customer-info p {
            margin: 5px 0;
            color: #666;
            font-size: 14px;
        }
        
        /* 标签页样式 */
        .r-tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            overflow-x: auto;
        }
        .r-tabs-item {
            padding: 12px 16px;
            font-size: 14px;
            color: #495057;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            min-width: 80px;
            text-align: center;
            flex-shrink: 0;
            transition: all 0.2s ease;
        }
        .r-tabs-item:hover {
            background: #e9ecef;
            color: #212529;
        }
        .r-tabs-item.active {
            color: #007bff;
            border-bottom-color: #007bff;
            background: #fff;
            font-weight: 500;
        }
        .tab-content {
            padding: 15px;
            min-height: 200px;
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .info-item {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 8px;
            border-left: 3px solid #007bff;
            margin-bottom: 10px;
        }
        .info-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }
        .info-value {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            text-align: center;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.loading {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .btn {
            display: inline-block;
            padding: 8px 16px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 5px;
            border: none;
            cursor: pointer;
        }
        .btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h3>客户标签页测试</h3>
        </div>
        
        <div class="customer-info">
            <h4>测试客户信息</h4>
            <p><strong>客户ID:</strong> 123</p>
            <p><strong>客户名称:</strong> 测试客户公司</p>
            <p><strong>联系电话:</strong> ***********</p>
            <p><strong>联系人:</strong> 张经理</p>
        </div>
        
        <!-- 状态显示 -->
        <div id="status" class="status loading">正在加载配置...</div>
        
        <!-- 标签页容器 -->
        <div id="tabsContainer" style="display: none;">
            <!-- 标签页将通过JavaScript动态生成 -->
        </div>
        
        <!-- 测试按钮 -->
        <div style="padding: 15px; text-align: center;">
            <button class="btn" onclick="testConfig()">测试配置</button>
            <button class="btn" onclick="testAPI()">测试API</button>
        </div>
    </div>

    <script>
        function updateStatus(message, type) {
            var status = document.getElementById('status');
            status.textContent = message;
            status.className = 'status ' + (type || 'loading');
        }
        
        function createTabs(tabsData) {
            var container = document.getElementById('tabsContainer');
            container.innerHTML = '';
            
            // 创建标签页导航
            var tabNav = document.createElement('div');
            tabNav.className = 'r-tabs';
            
            // 创建内容容器
            var contentContainer = document.createElement('div');
            
            tabsData.forEach(function(tab, index) {
                // 创建标签页按钮
                var tabItem = document.createElement('div');
                tabItem.className = 'r-tabs-item';
                tabItem.setAttribute('data-index', index);
                tabItem.textContent = tab.tab_name;
                
                // 绑定点击事件
                tabItem.addEventListener('click', function() {
                    switchTab(index);
                });
                
                tabNav.appendChild(tabItem);
                
                // 创建标签页内容
                var tabContent = document.createElement('div');
                tabContent.className = 'tab-content';
                tabContent.setAttribute('data-index', index);
                
                if (tab.content_type === 'html') {
                    // 处理HTML内容
                    var content = tab.content_source;
                    // 替换测试数据
                    content = content.replace(/\{name\}/g, '测试客户公司');
                    content = content.replace(/\{tel\}/g, '***********');
                    content = content.replace(/\{lxr\}/g, '张经理');
                    content = content.replace(/\{address\}/g, '北京市朝阳区');
                    content = content.replace(/\{khlx\}/g, '企业客户');
                    content = content.replace(/\{khly\}/g, '网络推广');
                    content = content.replace(/\{khzt\}/g, '正常');
                    content = content.replace(/\{optdt\}/g, new Date().toLocaleString());
                    content = content.replace(/\{explain\}/g, '这是一个测试客户的说明信息');
                    
                    // 处理条件显示
                    content = content.replace(/\{if_explain\}[\s\S]*?\{endif_explain\}/g, function(match) {
                        return match.replace(/\{if_explain\}/g, '').replace(/\{endif_explain\}/g, '');
                    });
                    
                    tabContent.innerHTML = content;
                } else {
                    tabContent.innerHTML = '<div class="info-item"><div class="info-label">' + tab.tab_name + '</div><div class="info-value">这是 ' + tab.content_type + ' 类型的内容，需要AJAX加载</div></div>';
                }
                
                contentContainer.appendChild(tabContent);
            });
            
            container.appendChild(tabNav);
            container.appendChild(contentContainer);
            
            // 激活第一个标签页
            switchTab(0);
            
            // 显示标签页容器
            container.style.display = 'block';
        }
        
        function switchTab(index) {
            // 更新导航状态
            var tabItems = document.querySelectorAll('.r-tabs-item');
            tabItems.forEach(function(item, i) {
                if (i === index) {
                    item.classList.add('active');
                } else {
                    item.classList.remove('active');
                }
            });
            
            // 更新内容显示
            var tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(function(content, i) {
                if (i === index) {
                    content.classList.add('active');
                } else {
                    content.classList.remove('active');
                }
            });
        }
        
        function testConfig() {
            updateStatus('正在检查配置...', 'loading');
            
            var xhr = new XMLHttpRequest();
            xhr.open('GET', 'check_config.php', true);
            xhr.setRequestHeader('Accept', 'application/json');
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            var response = JSON.parse(xhr.responseText);
                            if (response.success && response.data.length > 0) {
                                updateStatus('配置检查成功，找到 ' + response.data.length + ' 个标签页', 'success');
                                createTabs(response.data);
                            } else {
                                updateStatus('配置检查失败：' + response.message, 'error');
                            }
                        } catch (e) {
                            updateStatus('配置解析失败：' + e.message, 'error');
                        }
                    } else {
                        updateStatus('配置检查请求失败：HTTP ' + xhr.status, 'error');
                    }
                }
            };
            
            xhr.send();
        }
        
        function testAPI() {
            updateStatus('正在测试API...', 'loading');
            
            var xhr = new XMLHttpRequest();
            xhr.open('GET', 'index.php?d=we&m=component&a=getMobileTabs&category_code=customer', true);
            xhr.setRequestHeader('Accept', 'application/json');
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            var response = JSON.parse(xhr.responseText);
                            if (response.success && response.data.length > 0) {
                                updateStatus('API测试成功，返回 ' + response.data.length + ' 个标签页', 'success');
                                createTabs(response.data);
                            } else {
                                var errorMsg = 'API测试失败：' + response.message;
                                if (response.debug) {
                                    errorMsg += '\n调试信息：' + JSON.stringify(response.debug, null, 2);
                                }
                                updateStatus(errorMsg, 'error');
                                console.log('API调试信息：', response);
                            }
                        } catch (e) {
                            updateStatus('API响应解析失败：' + e.message + '\n原始响应：' + xhr.responseText, 'error');
                        }
                    } else {
                        updateStatus('API请求失败：HTTP ' + xhr.status, 'error');
                    }
                }
            };
            
            xhr.send();
        }
        
        // 页面加载完成后自动测试配置
        document.addEventListener('DOMContentLoaded', function() {
            testConfig();
        });
    </script>
</body>
</html>
