<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>客户标签页测试</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: #fff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background: #007bff;
            color: white;
            padding: 15px;
            text-align: center;
        }
        .customer-info {
            padding: 15px;
            background: #f8f9fa;
            border-bottom: 1px solid #ddd;
        }
        .customer-info h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .customer-info p {
            margin: 5px 0;
            color: #666;
            font-size: 14px;
        }
        
        /* 标签页样式 */
        .r-tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            overflow-x: auto;
        }
        .r-tabs-item {
            padding: 12px 16px;
            font-size: 14px;
            color: #495057;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            min-width: 80px;
            text-align: center;
            flex-shrink: 0;
            transition: all 0.2s ease;
        }
        .r-tabs-item:hover {
            background: #e9ecef;
            color: #212529;
        }
        .r-tabs-item.active {
            color: #007bff;
            border-bottom-color: #007bff;
            background: #fff;
            font-weight: 500;
        }
        .tab-content {
            padding: 15px;
            min-height: 200px;
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .info-item {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 8px;
            border-left: 3px solid #007bff;
            margin-bottom: 10px;
        }
        .info-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }
        .info-value {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            text-align: center;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.loading {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .btn {
            display: inline-block;
            padding: 8px 16px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 5px;
            border: none;
            cursor: pointer;
        }
        .btn:hover {
            background: #0056b3;
        }

        /* AJAX内容样式 */
        .lazy-load {
            text-align: center;
            padding: 30px 20px;
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .lazy-load:hover {
            background: #e9ecef;
            border-color: #007bff;
            color: #007bff;
        }

        /* 记录项样式 */
        .record-item, .opportunity-item, .contract-item, .service-item, .plan-item, .payment-item, .timeline-item {
            background: #fff;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 10px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .record-time, .opportunity-amount, .contract-amount, .service-date, .plan-date, .payment-date, .timeline-date {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }

        .record-content, .opportunity-title, .contract-title, .service-title, .plan-title, .payment-title, .timeline-content {
            font-size: 14px;
            color: #333;
            font-weight: 500;
            margin-bottom: 4px;
        }

        .record-person, .opportunity-stage, .contract-date, .service-description, .plan-content, .payment-amount {
            font-size: 13px;
            color: #666;
        }

        .opportunity-probability, .contract-status, .service-status {
            background: #007bff;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            margin-left: 8px;
        }

        .contract-status.已完成, .service-status.已完成 {
            background: #28a745;
        }

        .contract-status.执行中, .service-status.进行中 {
            background: #ffc107;
            color: #333;
        }

        .no-data {
            text-align: center;
            padding: 40px 20px;
            color: #999;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h3>客户标签页测试</h3>
        </div>
        
        <div class="customer-info">
            <h4>测试客户信息</h4>
            <p><strong>客户ID:</strong> 123</p>
            <p><strong>客户名称:</strong> 测试客户公司</p>
            <p><strong>联系电话:</strong> ***********</p>
            <p><strong>联系人:</strong> 张经理</p>
            <p><strong>客户类型:</strong> 企业客户</p>
            <p><strong>客户状态:</strong> 正常</p>
        </div>
        
        <!-- 状态显示 -->
        <div id="status" class="status loading">正在加载配置...</div>
        
        <!-- 标签页容器 -->
        <div id="tabsContainer" style="display: none;">
            <!-- 标签页将通过JavaScript动态生成 -->
        </div>
        
        <!-- 测试按钮 -->
        <div style="padding: 15px; text-align: center;">
            <button class="btn" onclick="testConfig()">测试配置</button>
            <button class="btn" onclick="testAPI()">测试API</button>
        </div>
    </div>

    <script>
        function updateStatus(message, type) {
            var status = document.getElementById('status');
            status.textContent = message;
            status.className = 'status ' + (type || 'loading');
        }
        
        function createTabs(tabsData) {
            var container = document.getElementById('tabsContainer');
            container.innerHTML = '';
            
            // 创建标签页导航
            var tabNav = document.createElement('div');
            tabNav.className = 'r-tabs';
            
            // 创建内容容器
            var contentContainer = document.createElement('div');
            
            tabsData.forEach(function(tab, index) {
                // 创建标签页按钮
                var tabItem = document.createElement('div');
                tabItem.className = 'r-tabs-item';
                tabItem.setAttribute('data-index', index);
                tabItem.textContent = tab.tab_name;
                
                // 绑定点击事件
                tabItem.addEventListener('click', function() {
                    switchTab(index);
                });
                
                tabNav.appendChild(tabItem);
                
                // 创建标签页内容
                var tabContent = document.createElement('div');
                tabContent.className = 'tab-content';
                tabContent.setAttribute('data-index', index);
                
                if (tab.content_type === 'html') {
                    // 处理HTML内容
                    var content = tab.content_source;

                    // 测试数据
                    var testData = {
                        name: '测试客户公司',
                        custid: 'KH202501001',
                        tel: '***********',
                        lxr: '张经理',
                        address: '北京市朝阳区建国路88号',
                        khlx: '企业客户',
                        khly: '网络推广',
                        khzt: '正常',
                        optdt: new Date().toLocaleString(),
                        optname: '销售经理',
                        deptname: '销售部',
                        email: '<EMAIL>',
                        explain: '这是一个重要的企业客户，有长期合作潜力',
                        total_contracts: '5',
                        total_amount: '500000.00',
                        paid_amount: '300000.00',
                        unpaid_amount: '200000.00',
                        payment_progress: '60'
                    };

                    // 替换所有测试数据
                    Object.keys(testData).forEach(function(key) {
                        var regex = new RegExp('\\{' + key + '\\}', 'g');
                        content = content.replace(regex, testData[key]);
                    });

                    // 处理条件显示
                    if (testData.explain) {
                        content = content.replace(/\{if_explain\}/g, '').replace(/\{endif_explain\}/g, '');
                    } else {
                        content = content.replace(/\{if_explain\}[\s\S]*?\{endif_explain\}/g, '');
                    }

                    tabContent.innerHTML = content;
                } else if (tab.content_type === 'ajax') {
                    // AJAX类型的内容，显示加载按钮
                    tabContent.innerHTML = '<div class="lazy-load" onclick="loadAjaxContent(' + index + ', \'' + tab.tab_code + '\')"><i class="icon-refresh"></i> 点击加载 ' + tab.tab_name + '</div>';
                } else {
                    tabContent.innerHTML = '<div class="info-item"><div class="info-label">' + tab.tab_name + '</div><div class="info-value">这是 ' + tab.content_type + ' 类型的内容</div></div>';
                }
                
                contentContainer.appendChild(tabContent);
            });
            
            container.appendChild(tabNav);
            container.appendChild(contentContainer);
            
            // 激活第一个标签页
            switchTab(0);
            
            // 显示标签页容器
            container.style.display = 'block';
        }
        
        function switchTab(index) {
            // 更新导航状态
            var tabItems = document.querySelectorAll('.r-tabs-item');
            tabItems.forEach(function(item, i) {
                if (i === index) {
                    item.classList.add('active');
                } else {
                    item.classList.remove('active');
                }
            });
            
            // 更新内容显示
            var tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(function(content, i) {
                if (i === index) {
                    content.classList.add('active');
                } else {
                    content.classList.remove('active');
                }
            });
        }
        
        function testConfig() {
            updateStatus('正在检查配置...', 'loading');
            
            var xhr = new XMLHttpRequest();
            xhr.open('GET', 'check_config.php', true);
            xhr.setRequestHeader('Accept', 'application/json');
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            var response = JSON.parse(xhr.responseText);
                            if (response.success && response.data.length > 0) {
                                updateStatus('配置检查成功，找到 ' + response.data.length + ' 个标签页', 'success');
                                createTabs(response.data);
                            } else {
                                updateStatus('配置检查失败：' + response.message, 'error');
                            }
                        } catch (e) {
                            updateStatus('配置解析失败：' + e.message, 'error');
                        }
                    } else {
                        updateStatus('配置检查请求失败：HTTP ' + xhr.status, 'error');
                    }
                }
            };
            
            xhr.send();
        }
        
        function testAPI() {
            updateStatus('正在测试API...', 'loading');
            
            var xhr = new XMLHttpRequest();
            xhr.open('GET', 'index.php?d=we&m=component&a=getMobileTabs&category_code=customer', true);
            xhr.setRequestHeader('Accept', 'application/json');
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            var response = JSON.parse(xhr.responseText);
                            if (response.success && response.data.length > 0) {
                                updateStatus('API测试成功，返回 ' + response.data.length + ' 个标签页', 'success');
                                createTabs(response.data);
                            } else {
                                var errorMsg = 'API测试失败：' + response.message;
                                if (response.debug) {
                                    errorMsg += '\n调试信息：' + JSON.stringify(response.debug, null, 2);
                                }
                                updateStatus(errorMsg, 'error');
                                console.log('API调试信息：', response);
                            }
                        } catch (e) {
                            updateStatus('API响应解析失败：' + e.message + '\n原始响应：' + xhr.responseText, 'error');
                        }
                    } else {
                        updateStatus('API请求失败：HTTP ' + xhr.status, 'error');
                    }
                }
            };
            
            xhr.send();
        }
        
        function loadAjaxContent(tabIndex, tabCode) {
            var container = document.querySelector('.mobile-tab-content[data-index="' + tabIndex + '"]');
            container.innerHTML = '<div class="loading">正在加载...</div>';

            var customerId = 123;
            var url = 'index.php?d=we&m=component&a=getCustomerTabs&customer_id=' + customerId + '&tab_type=' + tabCode;

            var xhr = new XMLHttpRequest();
            xhr.open('GET', url, true);

            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        var response = xhr.responseText;

                        // 如果返回的是空内容或错误，显示模拟数据
                        if (!response || response.trim() === '' || response.indexOf('error') !== -1) {
                            response = generateMockData(tabCode);
                        }

                        container.innerHTML = response;
                    } else {
                        container.innerHTML = generateMockData(tabCode);
                    }
                }
            };

            xhr.send();
        }

        function generateMockData(tabCode) {
            switch(tabCode) {
                case 'contact_record':
                    return '<div class="contact-records"><div class="record-item"><div class="record-time">2025-01-03 10:30</div><div class="record-content">电话沟通产品需求，客户对我们的解决方案很感兴趣</div><div class="record-person">联系人：张经理</div></div><div class="record-item"><div class="record-time">2025-01-02 14:20</div><div class="record-content">发送产品资料和报价单</div><div class="record-person">联系人：张经理</div></div></div>';

                case 'sales_opportunity':
                    return '<div class="sales-opportunities"><div class="opportunity-item"><div class="opportunity-title">ERP系统采购项目</div><div class="opportunity-amount">预计金额：¥500,000</div><div class="opportunity-stage">当前阶段：需求确认 <span class="opportunity-probability">70%</span></div></div><div class="opportunity-item"><div class="opportunity-title">办公设备采购</div><div class="opportunity-amount">预计金额：¥80,000</div><div class="opportunity-stage">当前阶段：方案设计 <span class="opportunity-probability">40%</span></div></div></div>';

                case 'contract_info':
                    return '<div class="contracts"><div class="contract-item"><div class="contract-title">软件开发服务合同</div><div class="contract-amount">合同金额：¥300,000</div><div class="contract-date">签订日期：2024-12-15 <span class="contract-status">执行中</span></div></div><div class="contract-item"><div class="contract-title">技术支持服务合同</div><div class="contract-amount">合同金额：¥50,000</div><div class="contract-date">签订日期：2024-11-20 <span class="contract-status">已完成</span></div></div></div>';

                case 'service_record':
                    return '<div class="service-records"><div class="service-item"><div class="service-title">系统升级服务</div><div class="service-description">为客户ERP系统进行版本升级和功能优化</div><div class="service-date">服务日期：2025-01-02 <span class="service-status">进行中</span></div></div><div class="service-item"><div class="service-title">技术培训服务</div><div class="service-description">为客户员工提供系统操作培训</div><div class="service-date">服务日期：2024-12-28 <span class="service-status">已完成</span></div></div></div>';

                case 'follow_plan':
                    return '<div class="follow-plans"><div class="plan-item"><div class="plan-title">下周产品演示</div><div class="plan-content">安排产品演示，重点展示ERP模块功能</div><div class="plan-date">计划时间：2025-01-08 14:00</div></div><div class="plan-item"><div class="plan-title">方案优化讨论</div><div class="plan-content">根据客户反馈优化技术方案</div><div class="plan-date">计划时间：2025-01-10 10:00</div></div></div>';

                case 'payment_record':
                    return '<div class="payment-records"><div class="payment-item"><div class="payment-title">首期款项</div><div class="payment-amount">收款金额：¥150,000</div><div class="payment-date">收款日期：2024-12-20</div></div><div class="payment-item"><div class="payment-title">二期款项</div><div class="payment-amount">收款金额：¥100,000</div><div class="payment-date">收款日期：2024-12-30</div></div></div>';

                case 'customer_timeline':
                    return '<div class="customer-timeline"><div class="timeline-item"><div class="timeline-date">2025-01-03</div><div class="timeline-content">客户咨询新产品功能</div></div><div class="timeline-item"><div class="timeline-date">2025-01-02</div><div class="timeline-content">发送技术方案文档</div></div><div class="timeline-item"><div class="timeline-date">2024-12-30</div><div class="timeline-content">收到二期项目款项</div></div></div>';

                default:
                    return '<div class="no-data">暂无' + tabCode + '数据</div>';
            }
        }

        // 页面加载完成后自动测试配置
        document.addEventListener('DOMContentLoaded', function() {
            testConfig();
        });
    </script>
</body>
</html>
