*{font-family:Verdana, Geneva, sans-serif;list-style-type:none;padding:0px;margin:0px;word-wrap:break-word;word-break:break-all;}

body{
	--main-color:#1389D3;
	--font-size:14px;
	--border:0.5px rgba(0,0,0,0.1) solid;
	--main-bgcolor:white;
	--main-hgcolor:white;
	--main-border:rgba(0,0,0,0.1);
	--rgb-r:0;
	--rgb-g:0;
	--rgb-b:0;
}

body{color:#000000;margin:0px;border:0;font-size:var(--font-size);}
a,.cursor{cursor:pointer;}
p{text-indent:24pt; margin:5px 0px}
input,textarea,a,button{resize: none;outline:none}

.zhu{ color:#1389D3;color:var(--main-color)}
.hui{ color:#888888}
.red{ color:#ff0000}
.blue{ color:blue}
table{border-spacing:0;border-collapse: collapse;}
a:link,a:visited{TEXT-DECORATION:none;color:#1389D3;color:var(--main-color)}
a:hover{TEXT-DECORATION:none;color:red;}

a.zhu{color:#1389D3;color:var(--main-color)}
img{border:0}


a.blue:link,a.blue:visited{color:blue;TEXT-DECORATION:none;}
a.blue:hover{TEXT-DECORATION:underline;color:red;}

a.red:link,a.red:visited{color:red;TEXT-DECORATION:underline;}
a.red:hover{TEXT-DECORATION:underline;color:red;}

a.a:link,a.a:visited{color:#0441b0;TEXT-DECORATION:underline;}
a.a:hover{TEXT-DECORATION:underline;color:red;}


.white{color:white;}
a.white:link,a.white:visited{color:white;TEXT-DECORATION:none;}
a.white:hover{TEXT-DECORATION:underline;color:white;}

.blank1{ height:1px; overflow:hidden; border-bottom:var(--border)}
.blank10{ height:10px; overflow:hidden}
.blank20{ height:20px; overflow:hidden;line-height:20px}
.blank5{ height:5px; overflow:hidden}
.blank25{ height:25px; line-height:25px;overflow:hidden;}
.blank30{ height:30px; line-height:30px; overflow:hidden}
.blank40{ height:40px; line-height:40px; overflow:hidden}
ul,li,a{ list-style-type:none}
.h1{ font-size:24px;font-weight:bold;}
.h2{ font-size:20px;font-weight:bold;}


.inputs{height:28px; line-height:24px; border:var(--border);padding:0px 2px; overflow:hidden;}
input.checkbox,input.radio{ border:none;padding:0;margin-right:5px; width:16px; height:16px}
.icons{ height:16px; width:16px; padding-right:3px}
.icons:hover{box-shadow:0px 0px 5px rgba(0,0,0,0.3);}

.barinput{padding:0px 2px;width:150px;height:23px}
.icon{ height:16px; width:16px;margin-right:5px}

.input,.select,.textarea{height:30px; line-height:28px; border:var(--border); padding:0px 5px;font-size:14px;}
.input:focus{box-shadow:0px 0px 5px rgba(0,0,0,0.3); border:1px #1389D3 solid; border:1px var(--main-color) solid;color:#000000}

.btn{height:30px;line-height:30px; background-color:#1389D3; background-color:var(--main-color);border:none;color:#f1f1f1;padding:0px 20px; cursor:pointer;opacity:0.9;border-radius:5px;}
.btn:hover{opacity:1;color:#ffffff;}
.btn[disabled]{ background-color:#aaaaaa}
.btn-danger{background:#d9534f}

.webbtn:link,.webbtn:visited,.webbtn{color:#f1f1f1; opacity:1;background-color:#1389D3;background-color:var(--main-color); padding:3px 8px; border:none; cursor:pointer}
.webbtn:hover{opacity:0.8;color:#ffffff;}
.box{box-shadow:0px 0px 5px rgba(var(--rgb-r),var(--rgb-g),var(--rgb-b),0.3);}


.alert{ padding:3px 10px; border-radius:0px; text-align:center; }
.alert_msg{ background-color:#fbe3cf;border:0px #f6a15d solid; color:#f86f00}
.alert_success{ background-color:#e3f6d1;border:0px #78b146 solid;color:green}
.alert_wait{ background-color:#f8f8f8;border:0px #cccccc solid;color:#555555}

.title{height:40px;line-height:40px;overflow:hidden;text-align:left;border-bottom:var(--border);color:#55555;font-weight:bold; background-color:#545e6a}
.title li{height:40px;line-height:40px;float:left; }
.title li.more{text-align:right;float:right;font-size:12px;font-weight:100}



.gradient{
	background:#1389D3;color:white;overflow:hidden;
	background:-moz-linear-gradient(top, #1389D3, #13B292,#0DAA8B);
	background:-webkit-linear-gradient(top,#3399CC, #3399CC,#3399CC);
	background:-ms-linear-gradient(top, #1389D3, #13B292,#0DAA8B);/*IE9以上*/
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#1389D3', centerColorstr='#13B292',endColorstr='#0DAA8B', GradientType='0'); /* IE8以下*/
}

.gradienth{
	background:#585858;color:white;overflow:hidden;
	background:-moz-linear-gradient(top, #666666, #585858,#454545);
	background:-webkit-linear-gradient(top,#666666, #585858,#454545);
	background:-ms-linear-gradient(top, #666666, #585858,#454545);
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#666666', centerColorstr='#585858',endColorstr='#454545', GradientType='0'); /* IE8以下*/
}

.reimlabel{background:#93cdf2;color:white;padding:1px 2px;font-size:12px;border-radius:2px}
.reimlabel1{background:#f9af7e;color:white;padding:1px 2px;font-size:12px;border-radius:2px}
.reimlabel2{background:#99CCCC;color:white;padding:1px 2px;font-size:12px;border-radius:2px}
.reimlabel3{background:#CC99CC;color:white;padding:1px 2px;font-size:12px;border-radius:2px}
.reimlabelonline{background:#99CC99;color:white;padding:1px 2px;font-size:12px;border-radius:2px}

@keyframes mymove{0%{width:140px;}100%{width:170px;}} 
.msousou{ background-color:rgba(0,0,0,0.1);height:26px;border:0px;border-radius:15px;margin-right:15px;padding:0px 20px;color:#eeeeee;width:140px;}
.msousou::-webkit-input-placeholder{color:#dddddd}
.msousou::-moz-input-placeholder{color:#dddddd}
.msousou:focus{background-color:white;color:#333333;-webkit-animation:mymove 0.3s;-moz-animation:mymove 0.3s;width:170px;}

.lists{padding:10px;cursor:pointer;}
.lists img,.lists .img{height:30px;width:30px;border-radius:5px;overflow:hidden}
.lists .close{position:absolute;right:3px;top:5px;display:none;color:#aaaaaa}
.lists .bqs{position:absolute;right:3px;top:5px;}
.lists.active{background-color:rgba(0,0,0,0.1)}
.lists:hover{background-color:rgba(0,0,0,0.05)}
.lists:hover .close{display:block}
.lists:hover .bqs{display:none}

.lists .name{height:24px;line-height:24px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}
.huicont{color:#888888;font-size:12px;height:20px;line-height:20px; overflow:hidden;word-wrap:break-word;word-break:break-all;white-space:normal;}



.content{width:100%;height:100%;border:none;overflow:auto;padding:0px;color:inherit}
.content:focus{border:0px #dddddd solid}

.chattitbtn{color:#aaaaaa;cursor:pointer}
.toolsliao{height:34px;overflow:hidden;border-top:var(--border);color:#aaaaaa;line-height:34px;font-size:16px;padding:0px 10px}
.toolsliao span{margin-right:10px}
.toolsliao span:hover,.chattitbtn:hover{color:#1389D3;color:var(--main-color)}


.progresscls{height:12px;overflow:hidden;line-height:12px;border:0px #eeeeee solid; position:relative;;width:99%;background-color:#f1f1f1;margin-top:3px}
.progressclssse{background-color:#B0D6FC;height:12px;overflow:hidden;width:5%;position:absolute;z-index:0;left:0px;top:0px}
.progressclstext{font-size:10px;color:#0556A8;height:12px;overflow:hidden;line-height:12px;text-align:left;position:absolute;z-index:1;left:5px;top:0px}


.badge {
  display: inline-block;
  min-width: 10px;
  padding: 3px 5px;
  font-size: 12px;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  background-color:red;
  border-radius: 10px;
}
.badge:empty {
  display: none;
}

.agenhclsdiv{display:inline-block}
.agenhcls{min-width:70px;height:70px;text-align:center;cursor:pointer;float:left;padding:0px 5px;margin-left:5px;position:relative}
.agenhcls img{height:34px;width:34px;border-radius:5px}
.agenhcls:hover{background-color:rgba(0,0,0,0.05);border-radius:5px}
.agenhcls span{position:absolute;right:1px;top:1px}

.lefticons{position:relative;line-height:50px;height:50px;overflow:hidden;font-size:20px;color:#cccccc}
.lefticons span{position:absolute;right:1px;top:1px}

.showblanks{padding:10px;color:#aaaaaa;font-size:12px;text-align:center}

.rock-loading {
  display: inline-block;
  height:16px;
  width:16px;
  vertical-align: middle;
  -webkit-mask: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='80px' height='80px' viewBox='0 0 80 80' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Eloading%3C/title%3E%3Cdefs%3E%3ClinearGradient x1='94.0869141%25' y1='0%25' x2='94.0869141%25' y2='90.559082%25' id='linearGradient-1'%3E%3Cstop stop-color='%23606060' stop-opacity='0' offset='0%25'%3E%3C/stop%3E%3Cstop stop-color='%23606060' stop-opacity='0.3' offset='100%25'%3E%3C/stop%3E%3C/linearGradient%3E%3ClinearGradient x1='100%25' y1='8.67370605%25' x2='100%25' y2='90.6286621%25' id='linearGradient-2'%3E%3Cstop stop-color='%23606060' offset='0%25'%3E%3C/stop%3E%3Cstop stop-color='%23606060' stop-opacity='0.3' offset='100%25'%3E%3C/stop%3E%3C/linearGradient%3E%3C/defs%3E%3Cg stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' opacity='0.9'%3E%3Cg%3E%3Cpath d='M40,0 C62.09139,0 80,17.90861 80,40 C80,62.09139 62.09139,80 40,80 L40,73 C58.2253967,73 73,58.2253967 73,40 C73,21.7746033 58.2253967,7 40,7 L40,0 Z' fill='url(%23linearGradient-1)'%3E%3C/path%3E%3Cpath d='M40,0 L40,7 C21.7746033,7 7,21.7746033 7,40 C7,58.2253967 21.7746033,73 40,73 L40,80 C17.90861,80 0,62.09139 0,40 C0,17.90861 17.90861,0 40,0 Z' fill='url(%23linearGradient-2)'%3E%3C/path%3E%3Ccircle id='Oval' fill='%23606060' cx='40.5' cy='3.5' r='3.5'%3E%3C/circle%3E%3C/g%3E%3CanimateTransform attributeName='transform' begin='0s' dur='1s' type='rotate' values='0 40 40;360 40 40' repeatCount='indefinite'/%3E%3C/g%3E%3C/svg%3E%0A") 0 0 no-repeat;
  mask-size: cover;
  -webkit-mask-size: cover;
  mask-size: cover;
  background-color: currentColor;
}

::-webkit-scrollbar{
  width: 8px;
  height: 8px;
  background-color: transparent;
}
::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background-color: rgba(0, 0, 0, 0.2);
}

::-webkit-scrollbar-track:hover {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 5px;
}