/**
*	createname：雨中磐石
*	homeurl：http://www.rockoa.com/
*	签名调用函数
*/
function autographClass(cans){
	var me = this;
	this.fid = 'absss';
	if(cans)for(var i in cans)this[i] = cans[i];
	this.qmimgstr = '';
	this.onsuccess= function(){}
	this.create=function(){
		// 设置签名区域宽度100%，高度45%
		var w = '100%',h = '45%';
		// 获取屏幕尺寸用于计算实际像素值
		var screenWidth = window.innerWidth || document.documentElement.clientWidth;
		var screenHeight = window.innerHeight || document.documentElement.clientHeight;
		var actualWidth = screenWidth;
		var actualHeight = Math.floor(screenHeight * 0.4);
		
		js.tanbody('qianming','请在空白区域写上签名',actualWidth,actualHeight,{
			html:'<div style="height:'+(actualHeight-50)+'px;overflow:hidden;touch-action:none;-webkit-overflow-scrolling:touch;"><iframe src="" name="qianmingiframe" width="100%" height="100%" frameborder="0" style="touch-action:none;"></iframe></div>',
			btn:[{text:'确定签名'},{text:'重写'}],
			// 添加防止屏幕滑动的样式
			onshow: function(){
				// 禁用页面滚动
				document.body.style.overflow = 'hidden';
				document.body.style.position = 'fixed';
				document.body.style.width = '100%';
				document.body.style.height = '100%';
			},
			onclose: function(){
				// 恢复页面滚动
				document.body.style.overflow = '';
				document.body.style.position = '';
				document.body.style.width = '';
				document.body.style.height = '';
			}
		});
		qianmingiframe.location.href='index.php?m=view&a=autograph&d=main&ism='+ismobile+'';
		$('#qianming_btn0').click(function(){
			me.qianmingok();
		});	
		$('#qianming_btn1').click(function(){
			me.qianmingre();
		});
	}
	this.qianmingok=function(){
		var str = qianmingiframe.autographok();
		if(str){
			this.showqian(str);
			// 恢复页面滚动
			document.body.style.overflow = '';
			document.body.style.position = '';
			document.body.style.width = '';
			document.body.style.height = '';
			js.tanclose('qianming');
		}
	}
	this.showqian=function(str){
		var s = '<img id="imgqianming_'+this.fid+'" src="'+str+'"  height="50" style="margin-right:10px;">';
		this.qmimgstr = str;
		$('#imgqianming_'+this.fid+'').parent().remove();
		// 修改容器样式为flex布局
		$('#graphview_'+this.fid+'').css({
			'display': 'flex',
			'align-items': 'center',
			'gap': '10px'
		});
		// 签名完成后，隐藏手写和引入按钮，只保留x按钮
		$('#graphview_'+this.fid+' button').each(function(index){
			if(index < 2) { // 隐藏前两个按钮（手写和引入）
				$(this).hide();
			}
		});
		// 将签名图片插入到容器的最前面，确保图片在左侧，按钮在右侧
		$('#graphview_'+this.fid+'').prepend(s);
		if(form(this.fid))form(this.fid).value = str;
		this.onsuccess(str);
	}
	this.qianmingre=function(){
		qianmingiframe.autographre();
		
	}
	this.getqmimgstr=function(){
		return this.qmimgstr;
	}
	
	//引入
	this.imports=function(){
		js.msg('wait','引入中...');
		js.ajax('?a=qianyin&m=flowopt&d=flow&ajaxbool=true',{},function(a){
			if(a.success){
				js.msg('success', '引入成功');
				$('#imgqianming').remove();
				var dataUrl = a.data;
				me.showqian(dataUrl);
			}else{
				js.msg('msg', a.msg);
			}
		},'get,json',function(s){
			js.msg('msg','操作失败');
		});
	}
	
	this.clear=function(){
		$('#imgqianming_'+this.fid+'').remove();
		this.qmimgstr = '';
		// 恢复容器原始样式
		$('#graphview_'+this.fid+'').css({
			'display': '',
			'align-items': '',
			'gap': ''
		});
		// 清除签名时，重新显示手写和引入按钮
		$('#graphview_'+this.fid+' button').each(function(index){
			if(index < 2) { // 显示前两个按钮（手写和引入）
				$(this).show();
			}
		});
		if(form(this.fid))form(this.fid).value = '';
	}
}
