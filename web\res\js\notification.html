<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="viewport" content="maximum-scale=1.0,minimum-scale=1.0,user-scalable=0,width=device-width,initial-scale=1.0"/>
<title>消息提醒</title>
<script src="../../../js/jquery.js"></script>
<style>
body,html{padding:0px;margin:0px;font-size:14px}
.close{height:20px;width:20px;display:block;text-align:center;line-height:18px;cursor:pointer;font-size:16px}
.close:hover{background:#ff1118;color:white;border-radius:10px}
table {
  border-spacing: 0;
  border-collapse: collapse;
}
</style>
<script>
$(document).ready(function(){
	winobj = nw.Window.get();
	winobj.moveTo(screen.width-360,screen.height-150);
	winobj.show();
	var nr = localStorage.getItem('xinhuoa_notification');
	if(nr){
		nr = unescape(nr);
		var da = JSON.parse(nr);
		var src = da.icon;
		if(src.substr(0,4)!='http')src='../../../'+src+'';
		$('#logodiv').attr('src', src);
		$('#titlediv').html(da.title);
		$('#messdiv').html(da.body);
	}
});
function winclose(){
	winobj.close(true);
}
function clickbody(){
	localStorage.setItem('xinhuoa_closelx', 'yes');
	winclose();
}
</script>
</head>
<body style="overflow:hidden;cursor:pointer; " >
<div style="border-radius:10px;background:white;border:rgba(0,0,0,0.1) 0.5px solid;margin:1px;box-shadow:0px 5px 10px 1px rgba(0,0,0,0.05);margin:5px">
	<div style="padding:8px">
	<div style="position:absolute;right:8px;top:7px;-webkit-app-region:no-drag;z-index:5">
	<span onclick="winclose();" class="close">×</span>
	</div>
	<table border="0" width="100%" onclick="clickbody()">
	<tr valign="top">
	<td width="60px">
	<div style="width:70px;height:70px;overflow:hidden;margin:5px" align="center">
	<img src="../../../images/logo.png" style="border-radius:10px;" id="logodiv" height="70px" width="70px">
	</div>
	</td>
	<td style="padding-left:5px" align="left">
	<div id="titlediv" style="font-size:16px;font-size:bold;line-height:20px;height:20px;overflow:hidden;margin-top:10px"></div>
	<div id="messdiv" style="font-size:14px;color:gray;line-height:18px;height:40px;overflow:hidden;margin-top:5px"></div>
	</td>
	</tr>
	</table>


	</div>
</div>
</body>
</html>