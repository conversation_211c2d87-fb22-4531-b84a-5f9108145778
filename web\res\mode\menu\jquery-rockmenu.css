.rockmenu{ position:absolute;display:none; z-index:9999;background:white;border-radius:5px}
.rockmenuli{
	border:var(--border);left:0px; top:0px; 
	background:var(--main-bgcolor);
	box-shadow:0px 0px 5px rgba(0,0,0,0.3);
	box-shadow:0px 0px 5px rgba(var(--rgb-r),var(--rgb-g),var(--rgb-b),0.3);
	border-radius:5px;
}
.rockmenulijt{ padding:0px; text-align:center}
.rockmenu ul{padding:0px;margin:0px}
.rockmenu li{ list-style-type:none; padding:10px 12px; cursor:pointer; text-align:left; border-bottom:var(--border);}
.rockmenu li.li01{ 
	background-color:rgba(0,0,0,0.05);
	background-color:rgba(var(--rgb-r),var(--rgb-g),var(--rgb-b),0.05);
}
.rockmenu li span{ font-size:10px; }
.rockmenu li.li01 span{}
.rockmenuli li img.iconsa{ vertical-align:middle; width:16px; height:16px; margin-right:8px}

.rockmenu .arrow-up{
	width: 0;
	height: 0;
	border-left: 10px solid transparent; /* 左边框的宽 */
	border-right: 10px solid transparent; /* 右边框的宽 */
	border-bottom: 10px solid #cccccc; /* 下边框的长度|高,以及背景色 */
	font-size: 0;
	line-height: 0;
} 