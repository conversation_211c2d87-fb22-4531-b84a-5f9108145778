<?php
/**
 * 移动端标签页配置文件
 * 创建时间：2025-01-03
 * 说明：基于配置文件的标签页管理，无需数据库
 */

if (!defined('HOST')) die('not access');

return [
    // 标签页分类配置
    'categories' => [
        'general' => [
            'name' => '通用标签',
            'description' => '适用于所有页面的通用标签页',
            'sort' => 1,
            'status' => 1
        ],
        'customer' => [
            'name' => '客户详情',
            'description' => '客户详情页面专用标签页',
            'sort' => 2,
            'status' => 1
        ],
        'project' => [
            'name' => '项目详情',
            'description' => '项目详情页面专用标签页',
            'sort' => 3,
            'status' => 1
        ],
        'flow' => [
            'name' => '流程详情',
            'description' => '流程详情页面专用标签页',
            'sort' => 4,
            'status' => 1
        ]
    ],
    
    // 标签页配置
    'tabs' => [
        // 客户详情标签页
        'customer' => [
            [
                'tab_name' => '基本信息',
                'tab_code' => 'basic_info',
                'tab_icon' => 'icon-info',
                'content_type' => 'html',
                'content_source' => '
                    <div class="basic-info-grid">
                        <div class="info-item">
                            <div class="info-label">客户名称</div>
                            <div class="info-value">{name}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">联系电话</div>
                            <div class="info-value">{tel}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">联系人</div>
                            <div class="info-value">{lxr}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">客户地址</div>
                            <div class="info-value">{address}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">客户类型</div>
                            <div class="info-value">{khlx}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">客户来源</div>
                            <div class="info-value">{khly}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">客户状态</div>
                            <div class="info-value">{khzt}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">创建时间</div>
                            <div class="info-value">{optdt}</div>
                        </div>
                        {if_explain}
                        <div class="info-item" style="grid-column: 1 / -1; margin-top: 15px;">
                            <div class="info-label">客户说明</div>
                            <div class="info-value">{explain}</div>
                        </div>
                        {endif_explain}
                    </div>
                ',
                'load_method' => 'immediate',
                'sort' => 1,
                'status' => 1,
                'is_default' => 1,
                'permissions' => ''
            ],
            [
                'tab_name' => '联系记录',
                'tab_code' => 'contact_record',
                'tab_icon' => 'icon-phone',
                'content_type' => 'ajax',
                'content_source' => 'we,component,getCustomerTabs',
                'load_method' => 'lazy',
                'sort' => 2,
                'status' => 1,
                'is_default' => 0,
                'permissions' => ''
            ],
            [
                'tab_name' => '销售机会',
                'tab_code' => 'sales_opportunity',
                'tab_icon' => 'icon-dollar',
                'content_type' => 'ajax',
                'content_source' => 'we,component,getCustomerTabs',
                'load_method' => 'lazy',
                'sort' => 3,
                'status' => 1,
                'is_default' => 0,
                'permissions' => ''
            ],
            [
                'tab_name' => '合同信息',
                'tab_code' => 'contract_info',
                'tab_icon' => 'icon-file-text',
                'content_type' => 'ajax',
                'content_source' => 'we,component,getCustomerTabs',
                'load_method' => 'lazy',
                'sort' => 4,
                'status' => 1,
                'is_default' => 0,
                'permissions' => ''
            ]
        ],
        
        // 项目详情标签页
        'project' => [
            [
                'tab_name' => '项目详情',
                'tab_code' => 'project_info',
                'tab_icon' => 'icon-info',
                'content_type' => 'html',
                'content_source' => '{contview}',
                'load_method' => 'immediate',
                'sort' => 1,
                'status' => 1,
                'is_default' => 1,
                'permissions' => ''
            ],
            [
                'tab_name' => '项目联系人',
                'tab_code' => 'project_contacts',
                'tab_icon' => 'icon-group',
                'content_type' => 'html',
                'content_source' => '{subcontview_1}',
                'load_method' => 'lazy',
                'sort' => 2,
                'status' => 1,
                'is_default' => 0,
                'permissions' => ''
            ],
            [
                'tab_name' => '项目远程',
                'tab_code' => 'project_remote',
                'tab_icon' => 'icon-desktop',
                'content_type' => 'html',
                'content_source' => '{subcontview_2}',
                'load_method' => 'lazy',
                'sort' => 3,
                'status' => 1,
                'is_default' => 0,
                'permissions' => ''
            ]
        ],
        
        // 流程详情标签页
        'flow' => [
            [
                'tab_name' => '流程详情',
                'tab_code' => 'flow_info',
                'tab_icon' => 'icon-info',
                'content_type' => 'html',
                'content_source' => '{contview}',
                'load_method' => 'immediate',
                'sort' => 1,
                'status' => 1,
                'is_default' => 1,
                'permissions' => ''
            ],
            [
                'tab_name' => '处理记录',
                'tab_code' => 'flow_records',
                'tab_icon' => 'icon-list',
                'content_type' => 'ajax',
                'content_source' => 'task,mode,getFlowRecords',
                'load_method' => 'lazy',
                'sort' => 2,
                'status' => 1,
                'is_default' => 0,
                'permissions' => ''
            ],
            [
                'tab_name' => '相关文件',
                'tab_code' => 'flow_files',
                'tab_icon' => 'icon-file',
                'content_type' => 'ajax',
                'content_source' => 'task,mode,getFlowFiles',
                'load_method' => 'lazy',
                'sort' => 3,
                'status' => 1,
                'is_default' => 0,
                'permissions' => ''
            ]
        ]
    ],
    
    // 关联配置（可选）
    'relations' => [
        // 根据模块自动匹配标签页
        'customer' => 'customer',
        'project' => 'project',
        'flow' => 'flow'
    ],
    
    // 全局设置
    'settings' => [
        'enable_stats' => false,  // 是否启用统计（需要数据库）
        'cache_enabled' => true,  // 是否启用缓存
        'default_load_method' => 'immediate',  // 默认加载方式
        'max_tabs' => 10,  // 最大标签页数量
        'enable_permissions' => false  // 是否启用权限检查
    ]
];
?>
