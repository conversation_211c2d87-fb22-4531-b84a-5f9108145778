/* 流程审核字段布局样式 */

/* PC端审核字段样式 */
.checkfield-column {
    border: 1px solid #ddd;
    border-radius: 5px;
    margin: 2px;
    background: #f9f9f9;
}

.checkfield-label {
    background: #e8f4f8;
    padding: 8px;
    border-bottom: 1px solid #ddd;
    border-radius: 5px 5px 0 0;
    font-size: 12px;
    font-weight: bold;
    color: #555555;
    text-align: center;
}

.checkfield-input {
    padding: 8px;
}

.checkfield-input .form-control,
.checkfield-input .inputs,
.checkfield-input input,
.checkfield-input select,
.checkfield-input textarea {
    width: 100% !important;
    box-sizing: border-box;
}

/* 移动端审核字段样式 */
.mobile-checkfields-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.mobile-checkfield-item {
    flex: 1;
    min-width: 45%;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 8px;
    background: #f9f9f9;
    box-sizing: border-box;
}

.mobile-checkfield-label {
    background: #e8f4f8;
    padding: 6px;
    margin: -8px -8px 8px -8px;
    border-bottom: 1px solid #ddd;
    border-radius: 5px 5px 0 0;
    font-size: 11px;
    font-weight: bold;
    color: #555555;
    text-align: center;
}

.mobile-checkfield-input {
    padding: 2px 0;
}

.mobile-checkfield-input .inputb,
.mobile-checkfield-input input,
.mobile-checkfield-input select,
.mobile-checkfield-input textarea {
    width: 100% !important;
    box-sizing: border-box;
    font-size: 14px;
}

/* 分组标题样式 */
.checkfields-group-title {
    font-weight: bold;
    color: #333;
    border-bottom: 1px solid #ddd;
    padding-bottom: 5px;
    margin-bottom: 10px;
    font-size: 14px;
}

/* 单独一行的字段样式 */
.checkfield-column.single-field {
    width: 100% !important;
}

/* 响应式布局 */
@media (max-width: 768px) {
    .checkfield-column {
        width: 100% !important;
        margin-bottom: 10px;
    }
    
    .mobile-checkfield-item {
        min-width: 100%;
        margin-bottom: 8px;
    }
    
    .checkfields-group-title {
        font-size: 13px;
    }
} 