@charset "UTF-8";

*{font-family:Verdana, Geneva, sans-serif;list-style-type:none;padding:0px;margin:0px;-webkit-tap-highlight-color: transparent;}

html{
	font-family: sans-serif;
}
body{
	--main-color:#1389D3;
	--font-size:14px;
	--border:0.5px rgba(0,0,0,0.1) solid;
	--main-bgcolor:white;
	--main-hgcolor:rgba(0,0,0,0.15);
	--main-vgcolor:rgba(0,0,0,0.1);
	--main-border:rgba(0,0,0,0.1);
	--rgb-r:0;
	--rgb-g:0;
	--rgb-b:0;
}
body{color:#000000;margin:0px;border:0;font-size:var(--font-size); background-color:white}
.mbody{background-color: #f1f1f1;}
td,button{ font-size:var(--font-size)}
a,.cursor{cursor:pointer;}
p{text-indent:24pt; margin:12px 0px;line-height:25px}
input,textarea,select,button{resize: none;outline:none;font-size:var(--font-size);}
input[type=button], input[type=submit], input[type=file], button {-webkit-appearance: none;}

.zhu{ color:#1389D3;color:var(--main-color)}
.hui{ color:#888888}
.red{ color:#ff0000}
.blue{ color:blue}
table{border-spacing: 0;border-collapse: collapse;}
a:link,a:visited{TEXT-DECORATION:none;color:#1389D3;color:var(--main-color)}
a:hover{TEXT-DECORATION:none;color:red;}
.touch{-webkit-overflow-scrolling:touch;overflow-scrolling:touch;}
a.zhu{color:#046DA5}
img{border:0}


a.blue:link,a.blue:visited{color:blue;TEXT-DECORATION:none;}
a.blue:hover{TEXT-DECORATION:underline;color:red;}

a.red:link,a.red:visited{color:red;TEXT-DECORATION:underline;}
a.red:hover{TEXT-DECORATION:underline;color:red;}




.white{color:white;}
a.white:link,a.white:visited{color:white;TEXT-DECORATION:none;}
a.white:hover{TEXT-DECORATION:underline;color:white;}

.blank1{ height:1px; overflow:hidden; border-bottom:1px #dddddd solid}
.blank10{ height:10px; overflow:hidden}
.blank20{ height:20px; overflow:hidden}
.blank5{ height:5px; overflow:hidden}
.blank25{ height:25px; line-height:25px;overflow:hidden;}
.blank30{ height:30px; line-height:30px; overflow:hidden}
.blank46{ height:46px; line-height:46px; overflow:hidden}
ul,li,a{ list-style-type:none}
.h1{ font-size:24px;font-weight:bold;}
.h2{ font-size:20px;font-weight:bold;}

.input,.inputs,.textarea{height:30px;line-height:25px;background-color:white;padding:5px;width:94%;border:var(--border);border-radius:5px;}
select.inputs{height:40px}

.inputb{height:34px;line-height:20px;background-color:white;padding:5px;border:var(--border);border-radius:5px}
.inputb:focus,.inputs:focus,.textarea:focus{border:0.5px var(--main-color) solid;}

.title{height:40px;line-height:40px;overflow:hidden;font-size:18px;text-align:left;border-bottom:var(--border);color:#55555;font-weight:bold; background-color:var(--main-color)}

.inputs[readonly]{ background-color:#f1f1f1;box-shadow:none}

.btn{width:100%;height:44px;line-height:44px;opacity:0.8; background-color:var(--main-color);border:none;color:white;font-size:var(--font-size); cursor:pointer}
.btn:active,.btn:hover{opacity:1;color:white;TEXT-DECORATION:none;}
.btn:disabled{ background-color:#cccccc;color:#888888}
a.btn{padding:5px 8px}

.header{height:50px; overflow:hidden;line-height:50px; text-align:center; border-bottom:var(--border);font-size:18px; position:fixed;width:100%;top:0px;left:0px; background:white;z-index:8}
.header .back{position:absolute;top:5px;left:5px}
.header .back img{height:24px;width:24px}
.header-back{width:50px;height:50px;display:block; background:url(../../images/back.png) no-repeat 7px 7px;position:absolute;left:0px;bottom:0px; cursor:default}
.header-back:active{opacity:0.5}
.divinput{padding:8px;}
.padding10{padding:10px;}

.imgbtn{padding:0px 5px}
.imgbtn:active{ background-color:#eeeeee}

.tdinpu{text-align:right;color:#555555;}
.tdinput{padding:8px 5px}
.inborder{border:var(--border);}

a.webbtn:link,a.webbtn:visited,.webbtn{color:#ffffff;opacity:1; background-color:var(--main-color); padding:8px 10px; border:none; cursor:pointer;font-size:var(--font-size);border-radius:5px}
.webbtn:disabled{background-color:#aaaaaa; color:#eeeeee}
.webbtn:hover,.webbtn:active{box-shadow:0px 0px 5px rgba(0,0,0,0.3);opacity:0.8}
.radius5{border-radius:5px;}

.upload_items{border:var(--border);height:60px;overflow:hidden;float:left;margin-top:5px;margin-bottom:5px;margin-right:10px;cursor:pointer;position:relative}
.upload_items:active{border:1px var(--main-color) solid}
.upload_items img.imgs{width:50px;height:50px;margin:5px}
.upload_items_items{padding:5px;text-align:center}
.upload_items_meng{ background:rgba(0,0,0,0.5);position:absolute;left:0px;top:0px;height:60px;overflow:hidden;line-height:60px;text-align:center;width:100%;color:white}

.list-itemv:active{color:var(--main-color);cursor:pointer}


.btn-group{display: flex;align-items: stretch;}
.btn-group .btn{float:left}
.btn-group>.active{box-shadow: inset 0 3px 5px rgba(var(--rgb-r),var(--rgb-g),var(--rgb-b), .125);}

.btn-group :first-child:not(:last-child){
	border-top-right-radius:0px;
	border-bottom-right-radius:0px;
}
.btn-group :last-child:not(:first-child){
	border-top-left-radius:0px;
	border-bottom-left-radius:0px;
}
.btn-group :not(:last-child):not(:first-child){
	border-radius:0px;
}
.btn-group :not(:first-child){
	border-left-width:0px;
}

.rock-loading {
  display: inline-block;
  height:16px;
  width:16px;
  vertical-align: middle;
  -webkit-mask: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='80px' height='80px' viewBox='0 0 80 80' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Eloading%3C/title%3E%3Cdefs%3E%3ClinearGradient x1='94.0869141%25' y1='0%25' x2='94.0869141%25' y2='90.559082%25' id='linearGradient-1'%3E%3Cstop stop-color='%23606060' stop-opacity='0' offset='0%25'%3E%3C/stop%3E%3Cstop stop-color='%23606060' stop-opacity='0.3' offset='100%25'%3E%3C/stop%3E%3C/linearGradient%3E%3ClinearGradient x1='100%25' y1='8.67370605%25' x2='100%25' y2='90.6286621%25' id='linearGradient-2'%3E%3Cstop stop-color='%23606060' offset='0%25'%3E%3C/stop%3E%3Cstop stop-color='%23606060' stop-opacity='0.3' offset='100%25'%3E%3C/stop%3E%3C/linearGradient%3E%3C/defs%3E%3Cg stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' opacity='0.9'%3E%3Cg%3E%3Cpath d='M40,0 C62.09139,0 80,17.90861 80,40 C80,62.09139 62.09139,80 40,80 L40,73 C58.2253967,73 73,58.2253967 73,40 C73,21.7746033 58.2253967,7 40,7 L40,0 Z' fill='url(%23linearGradient-1)'%3E%3C/path%3E%3Cpath d='M40,0 L40,7 C21.7746033,7 7,21.7746033 7,40 C7,58.2253967 21.7746033,73 40,73 L40,80 C17.90861,80 0,62.09139 0,40 C0,17.90861 17.90861,0 40,0 Z' fill='url(%23linearGradient-2)'%3E%3C/path%3E%3Ccircle id='Oval' fill='%23606060' cx='40.5' cy='3.5' r='3.5'%3E%3C/circle%3E%3C/g%3E%3CanimateTransform attributeName='transform' begin='0s' dur='1s' type='rotate' values='0 40 40;360 40 40' repeatCount='indefinite'/%3E%3C/g%3E%3C/svg%3E%0A") 0 0 no-repeat;
  mask-size: cover;
  -webkit-mask-size: cover;
  mask-size: cover;
  background-color: currentColor;
}

::-webkit-scrollbar{
  width: 8px;
  height: 8px;
  background-color: transparent;
}
::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background-color: rgba(0, 0, 0, 0.2);
}

::-webkit-scrollbar-track:hover {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 5px;
}