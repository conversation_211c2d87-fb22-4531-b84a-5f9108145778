/**
*	最新版的css样式
*	开发者：雨中磐石
*	地址：www.rockoa.com
*	日期：2024-10-10
*/

*{font-family:Verdana, Geneva, sans-serif;padding:0px;margin:0px;-webkit-tap-highlight-color: transparent;list-style-type:none}

body{
	--main-color:#1389D3;
	--font-size:14px;
	--border:0.5px rgba(0,0,0,0.1) solid;
	--main-bgcolor:white;
	--main-hgcolor:rgba(0,0,0,0.15);
	--main-vgcolor:rgba(0,0,0,0.1);
	--main-border:rgba(0,0,0,0.1);
	--input-height:34px;
	--btn-height:36px;
	--rgb-r:0;
	--rgb-g:0;
	--rgb-b:0;
}

body{font-size:var(--font-size)}
table{border-spacing:0;border-collapse: collapse;}
button,.cursor,a{cursor:pointer}
a:link,a:visited{color:var(--main-color);TEXT-DECORATION:none;font-size:var(--font-size)}
a:hover{TEXT-DECORATION:underline;color:red;}
input,button,textarea,select,div{resize:none;outline:none;font-size:var(--font-size);}
input[type=button], input[type=submit], input[type=file], button {-webkit-appearance: none;}

::placeholder{color:#999999;}
.wrap{word-wrap:break-word;word-break:break-all;white-space:normal;}

.input,.inputs,.form-control{border:var(--border);padding:0px 5px;overflow:hidden;border-radius:5px;background:white;height:34px;height:var(--input-height)}
.textarea{line-height:20px; border:var(--border);padding:5px; border-radius:5px;overflow:auto}
.input:focus,.textarea:focus,.form-control:focus,.inputs:focus{border:.5px var(--main-color) solid;}
.input:disabled,.inputs:disabled,.textarea:disabled,.form-control:disabled{cursor:not-allowed;opacity:0.8}
.input[readonly],.inputs[readonly],.textarea[readonly],.form-control[readonly]{background-color:rgba(0,0,0,0.1);}

.webbtn,.webbtn:link,.webbtn:visited,.btn,.btn:link,.btn:visited{color:#ffffff;background-color:var(--main-color);padding:6px 10px;border:none; cursor:pointer;border-radius:5px;white-space:nowrap;height:36px;height:var(--btn-height)}
.webbtn:active,.webbtn:hover,.btn:hover{box-shadow:0px 0px 5px rgba(0,0,0,0.3);box-shadow:0px 0px 5px rgba(var(--rgb-r),var(--rgb-g),var(--rgb-b),0.3);opacity:0.9;TEXT-DECORATION:none}
.webbtn:disabled,.btn:disabled{background-color:#dddddd;color:#888888;cursor:not-allowed}
.webbtn:disabled:hover,.btn:disabled:hover{background-color:none;box-shadow:none;opacity:1}
.webbtn-default,.webbtn-default:link,.webbtn-default:visited,.btn-default{background-color:white;color:black;border:var(--border);}
.webbtn-main,.webbtn-main:link,.webbtn-main:visited{background-color:white;color:black;border:.5px var(--main-color) solid;}

.btn-danger{background-color:#d9534f}
.form-control{width:98%}
textarea.form-control{overflow:auto;padding-top:5px;padding-bottom:5px}
.box{box-shadow:0px 0px 5px rgba(var(--rgb-r),var(--rgb-g),var(--rgb-b),0.3);}

.hover:hover,.active{
	background-color:rgba(0,0,0,0.1);
	cursor:pointer;
}
.border{border:var(--border)}
.mainbody{background:var(--main-bgcolor);border:var(--border);}
.btn-xs,.btn-xs:link,.btn-xs:visited{padding:3px 5px;font-size:12px;height:26px}

.zhu{color:var(--main-color)}
.hui{color:#888888}
.blank10{height:10px;overflow:hidden}
.blank20{height:20px;overflow:hidden}
.blank30{height:30px;overflow:hidden}
.notsel{-moz-user-select: none;-o-user-select:none;-webkit-user-select:none;-ms-user-select:none;user-select:none;cursor:default;}

.btn-group{display: flex;align-items: center;}
.btn-group .btn{float:left}
.btn-group>.active{box-shadow: inset 0 3px 5px rgba(var(--rgb-r),var(--rgb-g),var(--rgb-b), .125);}

.btn-group :first-child:not(:last-child),
.input-group :first-child:not(:last-child){
	border-top-right-radius:0px;
	border-bottom-right-radius:0px;
}
.btn-group :last-child:not(:first-child),
.input-group :last-child:not(:first-child){
	border-top-left-radius:0px;
	border-bottom-left-radius:0px;
}
.btn-group :not(:last-child):not(:first-child){
	border-radius:0px;
}
.btn-group :not(:first-child){
	border-left-width:0px;
}
.input-group-btn{
	display:flex;
}
.input-group-btn .btn:first-child{
	border-top-left-radius:0px;
	border-bottom-left-radius:0px;
	border-left-width:0px;
}

.rock-loading {
  display: inline-block;
  vertical-align: middle;
  height:16px;
  width:16px;
  -webkit-mask: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='80px' height='80px' viewBox='0 0 80 80' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Eloading%3C/title%3E%3Cdefs%3E%3ClinearGradient x1='94.0869141%25' y1='0%25' x2='94.0869141%25' y2='90.559082%25' id='linearGradient-1'%3E%3Cstop stop-color='%23606060' stop-opacity='0' offset='0%25'%3E%3C/stop%3E%3Cstop stop-color='%23606060' stop-opacity='0.3' offset='100%25'%3E%3C/stop%3E%3C/linearGradient%3E%3ClinearGradient x1='100%25' y1='8.67370605%25' x2='100%25' y2='90.6286621%25' id='linearGradient-2'%3E%3Cstop stop-color='%23606060' offset='0%25'%3E%3C/stop%3E%3Cstop stop-color='%23606060' stop-opacity='0.3' offset='100%25'%3E%3C/stop%3E%3C/linearGradient%3E%3C/defs%3E%3Cg stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' opacity='0.9'%3E%3Cg%3E%3Cpath d='M40,0 C62.09139,0 80,17.90861 80,40 C80,62.09139 62.09139,80 40,80 L40,73 C58.2253967,73 73,58.2253967 73,40 C73,21.7746033 58.2253967,7 40,7 L40,0 Z' fill='url(%23linearGradient-1)'%3E%3C/path%3E%3Cpath d='M40,0 L40,7 C21.7746033,7 7,21.7746033 7,40 C7,58.2253967 21.7746033,73 40,73 L40,80 C17.90861,80 0,62.09139 0,40 C0,17.90861 17.90861,0 40,0 Z' fill='url(%23linearGradient-2)'%3E%3C/path%3E%3Ccircle id='Oval' fill='%23606060' cx='40.5' cy='3.5' r='3.5'%3E%3C/circle%3E%3C/g%3E%3CanimateTransform attributeName='transform' begin='0s' dur='1s' type='rotate' values='0 40 40;360 40 40' repeatCount='indefinite'/%3E%3C/g%3E%3C/svg%3E%0A") 0 0 no-repeat;
  mask-size: cover;
  -webkit-mask-size: cover;
  mask-size: cover;
  background-color: currentColor;
  opacity:0.2;
}

::-webkit-scrollbar{
  width: 8px;
  height: 8px;
  background-color: transparent;
}
::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background-color: rgba(0, 0, 0, 0.2);
}

::-webkit-scrollbar-track:hover {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 5px;
}

.badge {
  display: inline-block;min-width: 10px; padding: 3px 5px;font-size: 12px;line-height: 1;color: #fff;
  text-align: center;white-space: nowrap;vertical-align: baseline;background-color:red;border-radius: 12px;
}
.badge:empty {display: none;}



.rock-table{
	width:100%;
	background:var(--main-bgcolor)
}

.rock-table .rock-table-th{
	height:40px;
	border:var(--border);
	background:var(--main-hgcolor);
	padding:3px;
}

.rock-table .rock-table-td{
	height:36px;
	border:var(--border);
	padding:3px;
	min-width:35px;
	word-wrap:break-word;word-break:break-all;white-space:normal;
}

.rock-table thead{}

.rock-table .rock-table-tr{}

.rock-table .rock-table-tr:nth-child(odd){
	background:rgba(0,0,0,0.03);
	background:rgba(var(--rgb-r),var(--rgb-g),var(--rgb-b),0.03);
}

.rock-table .rock-table-tr:hover{
	background:rgba(0,0,0,0.1);
	background:rgba(var(--rgb-r),var(--rgb-g),var(--rgb-b),0.1);
}

.rock-table-edit{
	border:var(--border);
	background:var(--main-bgcolor);
	padding:10px;
	box-shadow:0px 0px 10px rgba(0,0,0,0.3);
	box-shadow:0px 0px 10px rgba(var(--rgb-r),var(--rgb-g),var(--rgb-b),0.3);
	border-radius:5px;
}

.rock-table-list{
	padding:8px 10px;
	border:var(--border);
	border-radius:5px;
	margin:10px;
	background:white
}
.rock-table-list:active{
	background:rgba(0,0,0,0.05);
	background:rgba(var(--rgb-r),var(--rgb-g),var(--rgb-b),0.05);
}

.jquery-calendar .tdtext{border:var(--border);padding:3px}
.jquery-calendar .tdtext:hover{background-color:rgba(0,0,0,0.1);background-color:rgba(var(--rgb-r),var(--rgb-g),var(--rgb-b),0.1);}
.jquery-calendar .thtext{border:var(--border); background-color:rgba(0,0,0,0.1); background-color:rgba(var(--rgb-r),var(--rgb-g),var(--rgb-b),0.1);height:34px}

.list-group,.panel{background:var(--main-bgcolor);border:var(--border);margin-bottom:20px;border-radius:5px;}
.list-group-item{display:block;padding:12px;}
.list-group>a:hover{
	background-color:rgba(0,0,0,0.1);
	background-color:var(--main-vgcolor);
	color:inherit;
}

.list-group .list-group-item:not(:last-child){
	border-bottom:var(--border);
}
.list-group .list-group-item:first-child,.panel-heading{
	padding:12px;
	background:var(--main-hgcolor);
	border-top-right-radius:5px;
	border-top-left-radius:5px;
}

.nav{ display:flex}
.nav li{padding:10px 15px;}
.nav .active{background-color:rgba(var(--rgb-r), var(--rgb-g), var(--rgb-b), 0.1)}

.input-group{display: flex}
.floats{display: flex}
.floats30{flex:1;text-align:right}
.floats50{flex:0.5;}
.tishi{color:#888888;padding:10px 0px}

.tdinput{padding:5px 0px;text-align:left;padding-right:15px}
.inputtitle{line-height:34px;text-align:center;background-color:var(--main-hgcolor)}

.upload_items{border:var(--border);height:60px;overflow:hidden;float:left;margin-top:5px;margin-bottom:5px;margin-right:10px;cursor:pointer;position:relative}
.upload_items:active{border:1px var(--main-color) solid}
.upload_items img.imgs{width:50px;height:50px;margin:5px}
.upload_items_items{padding:5px;text-align:center}
.upload_items_meng{ background:rgba(0,0,0,0.5);position:absolute;left:0px;top:0px;height:60px;overflow:hidden;line-height:60px;text-align:center;width:100%;color:white}

.bootstree ul{height:36px;overflow:hidden;border-top:var(--border)}
.bootstree ul li{height:35px;overflow:hidden;line-height:35px;float:left;border-right:var(--border)}
.bootstree ul li:last-child{border-right-width:0px}
.bootstree ul:nth-of-type(even) {background-color:rgba(0,0,0,0.03);background-color:rgba(var(--rgb-r), var(--rgb-g), var(--rgb-b), 0.03);}
.bootstree ul:hover{background-color:rgba(0,0,0,0.1);background-color:rgba(var(--rgb-r), var(--rgb-g), var(--rgb-b), 0.1);}

.panel-footer{padding:10px;background-color:var(--main-bgcolor);border-top:var(--border)}

.select-list .div01{padding:8px 10px;border-bottom:var(--border)}
.select-list .div01:hover{background-color:rgba(var(--rgb-r), var(--rgb-g), var(--rgb-b), 0.1);}
.select-list .div02{padding:8px 10px;border-bottom:var(--border);background-color:rgba(var(--rgb-r), var(--rgb-g), var(--rgb-b), 0.1)}
.list-itemv:hover{color:var(--main-color);cursor:pointer;background-color:rgba(var(--rgb-r), var(--rgb-g), var(--rgb-b), 0.1)}

.editortools div{width:26px;height:26px;overflow:hidden;margin-left:8px;line-height:26px;border-radius:5px;cursor:pointer;}
.editortools div:hover{background-color:rgba(0,0,0,0.1);}

.label{border-radius:5px;padding:2px 3px}
.label-success{background:green;color:white}
.label-default{background:#aaaaaa;color:white}