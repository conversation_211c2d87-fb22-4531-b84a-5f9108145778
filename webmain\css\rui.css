/*
*	主颜色：#1389D3，可替换成你想要的颜色
*/

body,html{height:100%;-webkit-tap-highlight-color:transparent;}
body{overflow-x:hidden;background-color:#f1f1f1;-webkit-overflow-scrolling:touch;--main-color:#1389D3}

table{border-spacing: 0;border-collapse: collapse;}

input,select,textarea{resize: none;outline:none}
input:focus{border-color:#1389D3;border-color:var(--main-color)}

a:link,a:visited{TEXT-DECORATION:none;color:var(--main-color)}

.blank1{ height:1px; overflow:hidden; border-bottom:1px #dddddd solid}
.blank10{ height:10px; overflow:hidden;width:100%}
.blank5{ height:5px; overflow:hidden;width:100%}
.blank15{ height:15px;overflow:hidden;width:100%}
.blank20{ height:20px;overflow:hidden;width:100%}
.blank25{ height:25px;overflow:hidden;width:100%}
.blank30{ height:30px;  overflow:hidden;width:100%}
.blank40{ height:40px;  overflow:hidden;width:100%}

.r-wrap{word-wrap:break-word;word-break:break-all;white-space:normal;}

.r-touch{-webkit-overflow-scrolling:touch;overflow-scrolling:touch;overflow-y:auto;overflow-x:hidden}

.r-zhu-bgcolor{background-color:#1389D3;background-color:var(--main-color)}
.r-success-bgcolor{background-color:#449d44;}
.r-info-bgcolor{background-color:#d9edf7;}
.r-error-bgcolor{background-color:#f2dede;}

.r-header{height:50px;line-height:50px; background-color:white;overflow:hidden;color:#000000;font-size:18px;text-align:center;position:fixed;left:0px;top:0px;width:100%;z-index:10;border-bottom:0.5px #dddddd solid}
.r-header-text{text-align:center;font-size:18px;color:#000000}
.r-float-right{float:right;}
.r-float-left{float:left;}
.r-position-right{position:absolute;right:0px;bottom:0px}
.r-position-left{position:absolute;left:0px;bottom:0px}

.r-padding10{padding:10px}
.r-padding20{padding:20px}

.r-header-btn{display:block;height:50px;width:40px;text-align:center;line-height:50px;font-size:16px}

.r-header-btn:active{color:#aaaaaa}

.r-label{padding:3px;color:white;font-size:12px;border-radius:2px}

.blank50{height:50px;overflow:hidden}
.r-input{height:30px;width:97%;border:1px #cccccc solid;padding:2px}

.r-border-t,.r-border-b,.r-border-l,.r-border-r,.r-border{position: relative;}

.r-border-t:after {
  content: " ";
  position: absolute;
  display: block;
  left: 0;
  top: 0;
  width: 100%;
  height: 1px;
  border-top: 1px solid #cccccc;
  color: #cccccc;
  -webkit-transform-origin: 0 0;
          transform-origin: 0 0;
  -webkit-transform: scaleY(0.5);
          transform: scaleY(0.5);
}

.r-border:after {
  content: '';
  position: absolute;
  display: block;
  left: 0;
  top: 0;
  width: 200%;
  height: 200%;
  border: 1px solid #cccccc;
  color: #cccccc;
  -webkit-transform-origin: 0 0;
          transform-origin: 0 0;
  -webkit-transform: scale(0.5);
		transform: scale(0.5);
    pointer-events: none;
}

.r-border-b:before {
  content: " ";
  position: absolute;
  display: block;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 1px;
  border-bottom: 1px solid #cccccc;
  color: #cccccc;
  -webkit-transform-origin: 0 0;
          transform-origin: 0 0;
  -webkit-transform: scaleY(0.5);
          transform: scaleY(0.5);
}

.r-border-r:after {
  content: " ";
  position: absolute;
  display: block;
  right: 0;
  top: 0;
  width: 1px;
  height: 100%;
  border-right: 1px solid #cccccc;
  color: #cccccc;
  -webkit-transform-origin: 100% 0;
          transform-origin: 100% 0;
  -webkit-transform: scaleX(0.5);
          transform: scaleX(0.5);
}
.r-border-l:before {
  content: " ";
  position: absolute;
  display: block;
  left: 0;
  top: 0;
  width: 1px;
  height: 100%;
  border-left: 1px solid #cccccc;
  color: #cccccc;
  -webkit-transform-origin: 0 100%;
          transform-origin: 0 100%;
  -webkit-transform: scaleX(0.5);
          transform: scaleX(0.5);
}

.badge {
  display: inline-block;
  min-width: 10px;
  padding: 3px 5px;
  font-size: 12px;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  background-color:red;font-size:12px;
  border-radius: 10px;
}
.badge:empty {
  display: none;
}

.r-btn-active:active{opacity:0.8}

.r-chat{height:50px;overflow:hidden;background-color:#f1f1f1;position:fixed;left:0px;bottom:0px;width:100%}
.r-chat-input{height:30px;overflow:hidden;border:none;width:100%;background:none;border-bottom:1px #cccccc solid;font-size:16px;}
.r-chat-btn{height:30px;font-size:16px;overflow:hidden;border:none;width:50px;color:white; background-color:var(--main-color);display:block}

.r-tab{display: -webkit-box;display: -webkit-flex;display: flex;width: 100%;background-color:white;height:50px;overflow:hidden}
.r-tab-item{height:50px;overflow:hidden;line-height:49px;display: block;-webkit-box-flex: 1;-webkit-flex: 1; flex: 1;text-align:center}
.r-tab-item.active{color:var(--main-color);border-bottom:1px var(--main-color) solid;height:49px;}

.r-subtitle{color:#888888;padding:5px;font-size:14px;margin-top:5px}




.menulist{background-color:white;user-select:none;border-radius:10px}
.menulist div{padding:12px 0px;font-size:16px;text-align:center}
.menulist div:first-child{border-radius:10px 10px 0px 0px}
.menulist div:last-child{border-radius:0px 0px 10px 10px}
.menulist div:active{ background-color:#f1f1f1}

.webbtn{color:#ffffff;opacity:1; background-color:#336699;background-color:var(--main-color); padding:5px 10px; border:none; cursor:pointer;font-size:14px;border-radius:5px;white-space:nowrap;text-overflow:ellipsis;}
.webbtn:hover,.webbtn:active{box-shadow:0px 0px 5px rgba(0,0,0,0.3);opacity:0.8}
.input:focus{box-shadow:0px 0px 5px rgba(0,0,0,0.3); border:1px #0887CC solid;border:1px var(--main-color) solid; color:#000000}

.rock-loading {
  display: inline-block;
  height:16px;
  width:16px;
  vertical-align: middle;
  -webkit-mask: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='80px' height='80px' viewBox='0 0 80 80' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Eloading%3C/title%3E%3Cdefs%3E%3ClinearGradient x1='94.0869141%25' y1='0%25' x2='94.0869141%25' y2='90.559082%25' id='linearGradient-1'%3E%3Cstop stop-color='%23606060' stop-opacity='0' offset='0%25'%3E%3C/stop%3E%3Cstop stop-color='%23606060' stop-opacity='0.3' offset='100%25'%3E%3C/stop%3E%3C/linearGradient%3E%3ClinearGradient x1='100%25' y1='8.67370605%25' x2='100%25' y2='90.6286621%25' id='linearGradient-2'%3E%3Cstop stop-color='%23606060' offset='0%25'%3E%3C/stop%3E%3Cstop stop-color='%23606060' stop-opacity='0.3' offset='100%25'%3E%3C/stop%3E%3C/linearGradient%3E%3C/defs%3E%3Cg stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' opacity='0.9'%3E%3Cg%3E%3Cpath d='M40,0 C62.09139,0 80,17.90861 80,40 C80,62.09139 62.09139,80 40,80 L40,73 C58.2253967,73 73,58.2253967 73,40 C73,21.7746033 58.2253967,7 40,7 L40,0 Z' fill='url(%23linearGradient-1)'%3E%3C/path%3E%3Cpath d='M40,0 L40,7 C21.7746033,7 7,21.7746033 7,40 C7,58.2253967 21.7746033,73 40,73 L40,80 C17.90861,80 0,62.09139 0,40 C0,17.90861 17.90861,0 40,0 Z' fill='url(%23linearGradient-2)'%3E%3C/path%3E%3Ccircle id='Oval' fill='%23606060' cx='40.5' cy='3.5' r='3.5'%3E%3C/circle%3E%3C/g%3E%3CanimateTransform attributeName='transform' begin='0s' dur='1s' type='rotate' values='0 40 40;360 40 40' repeatCount='indefinite'/%3E%3C/g%3E%3C/svg%3E%0A") 0 0 no-repeat;
  mask-size: cover;
  -webkit-mask-size: cover;
  mask-size: cover;
  background-color: currentColor;
}

::-webkit-scrollbar{
  width: 8px;
  height: 8px;
  background-color: transparent;
}
::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background-color: rgba(0, 0, 0, 0.2);
}

::-webkit-scrollbar-track:hover {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 5px;
}

/* 移动端客户详情页面专用样式 */
.r-tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    overflow-x: auto;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;
}

.r-tabs-item {
    padding: 12px 16px;
    font-size: 14px;
    color: #495057;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    min-width: 80px;
    text-align: center;
    flex-shrink: 0;
    transition: all 0.2s ease;
    background: transparent;
    border: none;
    text-decoration: none;
}

.r-tabs-item:hover {
    background: #e9ecef;
    color: #212529;
}

.r-tabs-item.active {
    color: #007bff;
    border-bottom-color: #007bff;
    background: #fff;
    font-weight: 500;
}

/* 移动端详情页面内容样式 */
.customer-detail-content {
    background: #fff;
    padding: 15px;
    margin: 0;
}

.customer-detail-content div b {
    color: #333;
    font-weight: bold;
    font-size: 14px;
    display: block;
    margin: 15px 0 8px 0;
    padding-bottom: 5px;
    border-bottom: 1px solid #eee;
}

.customer-detail-content table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 15px;
    background: white;
}

.customer-detail-content td {
    padding: 8px 12px;
    vertical-align: top;
    border-bottom: 1px solid #f5f5f5;
    font-size: 14px;
}

.customer-detail-content tr:last-child td {
    border-bottom: none;
}

.customer-detail-content td:first-child {
    width: 100px;
    text-align: right;
    color: #666;
    font-size: 13px;
    background: #fafafa;
}

.customer-detail-content td:last-child {
    color: #333;
    font-size: 14px;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
    .r-tabs-item {
        font-size: 13px;
        padding: 10px 8px;
        min-width: 70px;
    }
}