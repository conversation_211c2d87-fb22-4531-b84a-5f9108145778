/**
*	审核流程自定义使用
*/

var inputtwp={
	flow5data:[],
	flow5init:function(){
		this.flow5show();
	},
	flow5initdata:function(){
		var str = alldata.flow5str;
		if(str){
			this.flow5data = JSON.parse(str);
			this.flow5show();
		}
	},
	flow5add:function(lx){
		var str = '<div style="padding:10px">';
		str+='<div>步骤名称：<input class="inputs" autocomplete="off" id="flow5_name" placeholder="选填" style="width:200px"></div>';
		str+='<div class="blank10"></div>';
		str+='<div>审核人员：<input class="inputs" id="flow5_checktypename" placeholder="必须可选多人" readonly style="width:165px"><input type="hidden" id="flow5_checktypename_id"><input onclick="js.changeuser(\'flow5_checktypename\',\'changeusercheck\',\'选择审核人员\')" class="webbtn" type="button" value="选"></div>';
		str+='<div class="blank10"></div>';
		str+='<div>审核人数：<select class="inputs" id="flow5_checkshu" style="width:200px"><option value="1" selected>至少一人</option><option value="0">需全部审核</option></select></div>';
		str+='<div class="blank10"></div>';
		str+='</div>';
		var btn = [{text:'确定'}];
		var nas = '创建审核人';
		if(lx>-1){
			btn.push({text:'删除此步骤'});
			nas = '编辑审核人';
		}
		js.tanbody('flow5',nas,310,300,{
			html:str,
			btn:btn
		});
		this.flow5lx = lx;
		$('#flow5_btn0').click(function(){c.flow5click();});
		$('#flow5_btn1').click(function(){c.flow5shan();});
	},
	flow5show:function(){
		var da = this.flow5data,i,str='';
		for(i=0;i<da.length;i++){
			if(!da[i].isdel)str+='<div onclick="c.flow5items('+i+', this)" id="itemtepsa'+i+'" style="border-radius:5px;height:50px" class="upload_items"><div style="line-height:35px" class="upload_items_items"><b>'+da[i].na+'</b><font color="#aaaaaa">('+da[i].cna+')</font></div></div><div id="itemteps'+i+'" class="upload_items" style="border:0;line-height:50px;color:#aaaaaa">→</div>';
		}
		str+='<div onclick="c.flow5add(-1)" style="border:dashed 1px #cccccc;border-radius:5px;height:50px" class="upload_items"><img class="imgs" src="images/jia.png" style="height:40px;width:40px"></div>';
		$('#flow5div').html(str);
		//console.log(da);
	},
	flow5click:function(){
		var da = {na:get('flow5_name').value,cna:get('flow5_checktypename').value,cnaid:get('flow5_checktypename_id').value,shu:get('flow5_checkshu').value}
		if(!da.cna){js.msgerror('请选择审核人员');return;}
		if(!da.na)da.na='审核';
		if(this.flow5lx>-1){
			for(var i in da)this.flow5data[this.flow5lx][i]=da[i];
		}else{
			this.flow5data.push(da);
		}
		this.flow5show();
		js.tanclose('flow5');
	},
	flow5items:function(i,o1){
		var da = this.flow5data[i];
		this.flow5add(i);
		get('flow5_name').value = da.na;
		get('flow5_checktypename').value = da.cna;
		get('flow5_checktypename_id').value = da.cnaid;
		get('flow5_checkshu').value = da.shu;	
	},
	flow5shan:function(){
		var i = this.flow5lx;
		$('#itemtepsa'+i+'').remove();
		$('#itemteps'+i+'').remove();
		this.flow5data[i].isdel = true;
		js.tanclose('flow5');
	},
	flow5get:function(){
		var da = this.flow5data,i,ds=[];
		for(i=0;i<da.length;i++){
			if(!da[i].isdel)ds.push(da[i]);
		}
		return ds;
	},
	flow5import:function(){
		js.loading('加载中...');
		js.ajax(geturlact('flow5import'),{modeid:moders.id},function(ret){
			js.msgok(ret.msg);
			var str = ret.data;
			if(str){
				c.flow5data = JSON.parse(str);
				c.flow5show();
			}
		}, 'get,json');
	}
}