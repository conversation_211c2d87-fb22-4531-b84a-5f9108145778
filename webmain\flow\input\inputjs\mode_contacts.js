//流程模块【contacts.联系人】下录入页面自定义js页面,初始函数
function initbodys(){
	// 从URL参数获取custid并自动设置到表单
	var custid = js.request('custid');
	if(custid && custid != '0' && custid != '') {
		// 立即添加custid隐藏字段，不等待Ajax
		addCustidField(custid);
		
		// 拦截表单提交，确保custid字段被包含
		interceptFormSubmit(custid);
		
		// 延时执行，确保表单已经加载完成
		setTimeout(function(){
			setCustomerInfo(custid);
		}, 1000);
	}
}

/**
 * 拦截表单提交，确保custid被包含
 */
function interceptFormSubmit(custid) {
	// 重写系统的savesss函数
			if (typeof savesss === 'function') {
			var originalSavesss = savesss;
			window.savesss = function() {
				// 确保custid字段存在
				var custidField = document.querySelector('input[name="custid"]');
				if (!custidField) {
					addCustidField(custid);
				} else {
					custidField.value = custid;
					custidField.disabled = false;
				}
				
				// 调用原始提交函数
				return originalSavesss.apply(this, arguments);
			};
		}
}

/**
 * 立即添加custid隐藏字段到表单
 */
function addCustidField(custid) {
	// 立即查找并添加custid字段
	var form = document.forms['myform'] || document.querySelector('form[name="myform"]') || document.querySelector('form');
	if (form) {
		// 检查是否已存在
		var existingField = form.querySelector('input[name="custid"]');
		if (!existingField) {
			// 创建隐藏字段
			var custidField = document.createElement('input');
			custidField.type = 'hidden';
			custidField.name = 'custid';
			custidField.value = custid;
			custidField.disabled = false;
			
			// 插入到表单开头
			if (form.firstChild) {
				form.insertBefore(custidField, form.firstChild);
			} else {
				form.appendChild(custidField);
			}
			

		}
	}
}

/**
 * 设置联系人的客户信息（特殊处理：联系人使用关联表）
 * @param {string} custid 客户ID
 */
function setCustomerInfo(custid) {
	if (!custid || custid == '0') return;
	
	// 获取客户信息
	js.ajax(geturlact('getcustinfo'), {custid: custid}, function(ret) {
		if (ret && ret.success) {
	
			
			// 联系人模块特殊处理：需要添加一个隐藏的custid字段来传递客户ID
			// 因为联系人模块本身没有custid字段，是通过关联表custcontrel来建立关系的
			
			// 确认custid字段存在并更新值
			var custidField = document.querySelector('input[name="custid"]');
			if (!custidField) {
				// 如果之前没有添加成功，再次尝试添加
				addCustidField(custid);
				custidField = document.querySelector('input[name="custid"]');
			}
			
			if (custidField) {
				// 更新字段值并确保不被禁用
				custidField.value = custid;
				custidField.disabled = false;
				custidField.removeAttribute('disabled');
				
			}
			
			
			
			// 在页面显示客户信息提示
			var infoHtml = '<div style="padding: 8px; background: #f0f8ff; border: 1px solid #007cba; margin: 10px 0; border-radius: 3px;">';
			infoHtml += '<i class="fa fa-info-circle" style="color: #007cba;"></i> ';
			infoHtml += '关联客户：<strong>' + ret.name + '</strong> (客户编号: ' + custid + ')';
			infoHtml += '</div>';
			
			// 尝试插入到表单顶部
			var formContainer = document.querySelector('.bootstable') || document.querySelector('form');
			if (formContainer) {
				var existingInfo = formContainer.querySelector('.customer-info');
				if (existingInfo) {
					existingInfo.remove();
				}
				
				var infoDiv = document.createElement('div');
				infoDiv.className = 'customer-info';
				infoDiv.innerHTML = infoHtml;
				formContainer.insertBefore(infoDiv, formContainer.firstChild);
			}
			
		} else {
			console.error('获取客户信息失败:', ret ? ret.msg : '未知错误');
		}
	}, 'get,json');
}

function changesubmit(d){
	// 检查客户ID是否设置
	if(!d.custid || d.custid == '0') {
		return '请选择客户';
	}
	
	if(!d.given_name){
		return '联系人姓名不能为空';
	}
	if(!d.mobile){
		return '手机号不能为空';
	}
}