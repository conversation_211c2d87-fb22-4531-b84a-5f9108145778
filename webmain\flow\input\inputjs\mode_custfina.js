var kexuan = true;
function initbodys(){
	
	c.onselectdata['custname']=function(){
		form('htid').value = '0';
	}
	
	var defe = js.request('def_htid');
	var defsource = js.request('def_source'); // 接收业务类型参数：electwork=电子服务单，contract=合同
	// 处理所有非零def_htid：负数(销售单)、正数(合同)、电子服务单(通过source区分)
	if(defe && defe != '0' && defe != 0)salechange(defe, defsource);
	
	if(mid>0){
		if(data.xgid && data.xgid>0){
			form('htid').length=2;
			form('money').readOnly=true;
			$(form('money')).click(function(){
				js.alert('关联了其他单据，金额不能修改');
			})
			kexuan = false;
		}
	}else{
		if(!defe)form('htid').selectedIndex =1;
	}
	
	if(kexuan){
		$(form('htid')).change(function(){
			var val = this.value,txt='';
			salechange(val);
		});
	}
}

c.onselectdatabefore=function(fid){
	if(fid=='custname' && !kexuan)return '已关联其他单据不可选择';
}

function salechange(v, source){
	if(!kexuan)return;
	if(v=='' || v=='0'){
		form('custid').value='';
		return;
	}
	// 将source参数传递给后端，用于区分业务类型
	var params = {ractid:v};
	if(source) params.source = source;
	js.ajax(geturlact('ractchange'),params,function(a){
		form('custid').value=a.custid;
		form('custname').value=a.custname;
		form('money').value=a.money;
		if(form('type'))form('type').value=a.type;
		form('htnum').value=a.num;
		form('dt').value=a.signdt;
		
		// 自动填充收款人信息：如果payee字段没有值，则自动获取电子服务单的dist到payee，distid到payeeid
		if(form('payee') && !form('payee').value && a.payee) {
			form('payee').value = a.payee;
		}
		if(form('payeeid') && !form('payeeid').value && a.payeeid) {
			form('payeeid').value = a.payeeid;
		}
		
		form('htid').value = v;
	},'get,json');
}

function changesubmit(d){
	if(d.ispay=='1'){
		if(form('paytype') && !d.paytype)return '已收款了，收款类型不能为空';
		if(!d.paydt)return '已收款了，收款时间不能为空';
	}
}
