//初始函数
function initbodys(){
	
	c.onselectdata['sheng'] = function(d){
		if(!d.shengname)return;
		if(form('sheng'))form('sheng').value = d.shengname;
		if(form('shi'))form('shi').value = d.cityname;
	}
	// 如果存在type字段，为其添加change事件监听器
	if(form('type'))$(form('type')).change(function(){
		changetype(true);
	});
	// 页面加载时初始化调用changetype,传入false表示仅初始化显示/隐藏，不执行额外操作
	changetype(false);
}

//地图选择
c.onselectmap=function(sna,res){
	var info = res.addressinfo;
	if(form('sheng'))form('sheng').value = info.province;
	if(form('shi'))form('shi').value = info.city;
	if(!res.address){
		var dz = info.town;
		dz+=(info.streetNumber)?info.streetNumber:info.street;
		form(sna).value=dz;
	}
}
//根据客户类型控制上级单位名称字段的显示与隐藏
function changetype(bo){
	if(!form('type'))return;
	var v = form('type').value;
	var o = $('#div_supername').parent().parent();
	if(v=='1'){
		o.show();
		if(bo)changetotal();
	}else{
		o.hide();
		if(bo)form('supername').value='0';
	}
}

//客户模块的计算函数（根据业务需求可以在此添加相关计算逻辑）
function changetotal(){
	// 客户模块暂时不需要特殊计算，保留空函数以避免JS错误
	// 如果后续需要添加客户相关的计算逻辑，可以在此处实现
	return;
}