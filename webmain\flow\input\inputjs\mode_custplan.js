//流程模块【custplan.跟进计划】下录入页面自定义js页面,初始函数
function initbodys(){
	// 从URL参数获取custid并自动设置到表单
	var custid = js.request('custid');
	if(custid && custid != '0' && custid != '') {
		// 延时执行，确保表单已经加载完成
		setTimeout(function(){
			setCustomerInfo(custid);
		}, 1000);
	}
}

// 设置客户信息
function setCustomerInfo(custid) {
	// 设置客户ID
	if(form('custid')) {
		form('custid').value = custid;
	}
	
	// 获取并设置客户名称
	js.ajax(geturlact('getcustinfo'), {custid: custid}, function(ret) {
		if(ret && ret.success && ret.name) {
			if(form('custname')) {
				form('custname').value = ret.name;
			}
		} else {
			// 如果获取失败，设置默认提示
			if(form('custname')) {
				form('custname').value = '客户编号:' + custid;
			}
		}
	}, 'post,json');
}

function changesubmit(d){
	// 检查客户ID是否设置
	if(!d.custid || d.custid == '0') {
		return '请选择客户';
	}
	
	if(d.status=='0' && !d.plandt){
		return '计划时间不能为空';
	}
	if(d.status=='1' && !d.findt){
		return '完成时间不能为空';
	}
}

function oninputblur(na){
	var zt = form('status').value;
	if((zt=='0' || zt=='5') && form('findt'))form('findt').value='';
}