function initbodys(){
	// 初始化合同设置
	if(mid==0){
		$(form('signdt')).change(function(){
			changedate(this);
		});
		$(form('months')).change(function(){
			changedate(this);
		});
	}
	
	// 从URL参数获取custid并自动设置到表单
	var custid = js.request('custid');
	if(custid && custid != '0' && custid != '') {
		// 延时执行，确保表单已经加载完成
		setTimeout(function(){
			setCustomerInfo(custid);
		}, 1000);
	}

	$(form('custid')).change(function(){
		var val = this.value,txt='';
		if(val>0){
			js.ajax(geturlact('getother'),{id:val},function(a){
				if(a.linkname)form('linkname').value=a.linkname;
			},'get,json');
		}
	});
	
	$(form('custid')).change(function(){
		var val = this.value,txt='';
		if(val!=''){
			txt = this.options[this.selectedIndex].text;
		}
		form('custname').value=txt;
		if(form('saleid'))form('saleid').value = '';
	});
	
	if(form('saleid'))$(form('saleid')).change(function(){
		salechange(this.value);
	});
	
	if(isinput==1){
		if(form('htfileid') && form('htfileid').value==''){
			$('#fileview_htfileid').after('<div><a href="javascript:;" onclick="xuanwenj(this)" class="blue">＋选择模版文件</a></div>');
		}
	}
}

// 设置客户信息
function setCustomerInfo(custid) {
	// 设置客户ID
	if(form('custid')) {
		form('custid').value = custid;
	}
	
	// 获取并设置客户名称
	js.ajax(geturlact('getcustinfo'), {custid: custid}, function(ret) {
		if(ret && ret.success && ret.name) {
			if(form('custname')) {
				form('custname').value = ret.name;
			}
		} else {
			// 如果获取失败，设置默认提示
			if(form('custname')) {
				form('custname').value = '客户编号:' + custid;
			}
		}
	}, 'post,json');
}

function changesubmit(d){
	// 检查客户ID是否设置
	if(!d.custid || d.custid == '0') {
		return '请选择客户';
	}
	
	if(!d.contractno){
		return '合同编号不能为空';
	}
	if(!d.money || d.money == '0'){
		return '合同金额不能为空或0';
	}
}

function changedate(o){
	if(isempt(o.value))return;
	var months = intval(form('months').value);
	if(months==0)months=12;
	js.ajax(geturlact('getenddt'),{date:o.value,month:months},function(a){
		form('enddt').value=a.enddt;
	},'get,json');
}

c.onselectdata['custname']=function(d){
	var nae = d.subname;
	if(isempt(nae))nae=d.name;
	form('custname').value=nae;
}

function salechange(v){
	if(v=='' || v=='0'){
		return;
	}
	js.ajax(geturlact('salechange'),{saleid:v},function(a){
		form('custid').value=a.custid;
		form('custname').value=a.custname;
		form('money').value=a.money;
		$(form('custid')).change();
	},'get,json');
}

function xuanwenj(o1){
	var ne = form('custname').value;
	if(!ne){
		js.msg('msg','请先选择客户');
		return;
	}
	var bh = form('num').value;
	c.xuanfile('htfileid','客户合同',''+ne+'('+bh+')的合同',o1);
}

c.uploadfileibefore=function(sna){
	if(sna=='htfileid'){
		var val = form(sna).value;
		if(val)return '最多只能上传一个文件，其他文件可到相关文件添加';
	}
}

