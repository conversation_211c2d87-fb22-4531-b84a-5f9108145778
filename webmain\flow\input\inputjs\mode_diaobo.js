//流程模块【diaobo.调拨单】下录入页面自定义js页面,初始函数
function initbodys(){
	//记录原来选择的
	c.daossdts=[];
	
	// 页面加载后立即加载仓库选项
	setTimeout(function(){
		loadCustidOptions();
	}, 500);

	
	c.onselectdatabefore=function(fid,zb){
		// 选择物品前必须先选择仓库
		if(fid.indexOf('temp_aid')==0){
			if(form('custid').value=='')return '请先选择仓库';
			this.daossdts = this.getsubdata(0);
			return {'ckid':form('custid').value};
		}
	}
	
	/**
 * 处理物品选择后的数据更新逻辑
 * @param {string} fid - 表单字段ID
 * @param {object} seld - 选择的数据
 * @param {string} sna - 选择的数据名称
 * @param {string} sid - 选择的数据ID
 * 功能：
 * 1. 更新选中物品到表单
 * 2. 设置物品的最大库存限制
 * 3. 处理重复选择的情况
 */
	c.onselectdataall=function(fid,seld,sna,sid){
		if(!seld || !sna)return;
		var da = [];
		if(!seld[0]){
			da[0]=seld;
		}else{
			da = seld;
		}
		var nam = this.getxuandoi(fid),snua;
		var dao=this.daossdts,i,j,bo,d,oi=parseFloat(nam[1]),oii=-1;
		for(i=0;i<da.length;i++){
			d  = da[i];
			bo = false;
			for(j=0;j<dao.length;j++)if(dao[j].aid==d.value)bo=true;
			oii++;
			if(!bo){
				if(oii>0){
					snua= ''+nam[3]+''+nam[0]+'_'+(oi+oii)+'';
					if(!form(snua) || form(snua).value!=''){
						nam = this.insertrow(0,{},true);
					}else{
						nam[1]=parseFloat(nam[1])+1;
					}
				}
				this.setrowdata(nam[0],nam[1],{
					temp_aid:d.name,
					aid:d.value
				});
				$(form('count'+nam[2]+'')).attr('max', d.stock);
			}else{
				oii--;
				if(i==0){
					this.setrowdata(nam[0],nam[1],{
						temp_aid:'',
						aid:'0'
					});
				}
			}	
		}
	}

	// 监听 stamp(radio) 变化
	$('input[name="stamp"]').change(function(){
		refreshCustid();
	});
	
	// 清空并刷新custid字段
	function refreshCustid(){
		if(form('custid')) {
			form('custid').value='';
			// 重新加载选项
			loadCustidOptions();
		}
	}
	
	// 加载custid选项的函数
	function loadCustidOptions(){
		var stv = $('input[name="stamp"]:checked').val() || '0';
		
		// 使用Ajax获取仓库数据
		var ajaxUrl = geturlact('getCustidOptions') + '&stamp=' + stv + '&rnd=' + new Date().getTime();
		
		$.ajax({
			url: ajaxUrl,
			type: 'GET',
			dataType: 'json',
			success: function(ret){
				var custidSelect = form('custid');
				if(custidSelect) {
					// 清空现有选项，保留第一个空选项
					custidSelect.length = 1;
					
					if(ret && ret.length > 0) {
						for(var i = 0; i < ret.length; i++){
							var option = new Option(ret[i].name, ret[i].value);
							custidSelect.add(option);
						}
					}
				}
			},
			error: function(xhr, status, error){
				js.msg('msg', '加载仓库数据失败，请重试');
			}
		});
	}
}

function changesubmit(){
	if(get('tablesub0')){
		var da = c.getsubdata(0),d1;
		for(var i=0;i<da.length;i++){
			d1 = da[i];
			if(!d1.aid)return '行['+(i+1)+']必须选择物品';
			if(d1.count<=0)return '行['+(i+1)+']数量必须大于0';
		}
	}
}


function eventaddsubrows(xu,oj){
	c.setrowdata(xu,oj,{
		aid:'0'
	});
}