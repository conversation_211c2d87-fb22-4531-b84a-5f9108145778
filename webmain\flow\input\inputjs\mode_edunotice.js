//流程模块【edunotice.学校通知】下录入页面自定义js页面,初始函数
function initbodys(){
	if(form('title'))form('title').readOnly=false;
	
	c.onselectdata['title']=function(d){
		js.ajax(geturlact('gonginfo'),{id:d.id},function(ret){
			c.editorobj['content'].html(ret.content);
			if(form('url'))form('url').value=ret.url;
			if(form('zstart'))form('zstart').value=ret.zstart;
			if(form('zsend'))form('zsend').value=ret.zsend;
			if(form('fengmian'))form('fengmian').value=ret.fengmian;
			if(get('imgview_fengmian') && ret.fengmian)get('imgview_fengmian').src=ret.fengmian;
		},'get,json');
	}
}