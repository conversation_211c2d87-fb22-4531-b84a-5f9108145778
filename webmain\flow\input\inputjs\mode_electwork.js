//流程模块【electwork.电子服务单】下录入页面自定义js页面,初始函数
function initbodys(){
	// 设置子表最少行数为0，允许子表无数据
	if(typeof subdataminlen === 'undefined') {
		window.subdataminlen = [];
	}
	subdataminlen[0] = 0;
	
	// 设置字段默认值
	setTimeout(function(){
		// 如果receipt字段存在且为空，设置默认值
		if(form('receipt') && form('receipt').value === '') {
			form('receipt').value = '0'; // 设置默认值为0（或其他默认值）
		}
		
		// 如果stamp字段存在且为空，设置默认值
		if(form('stamp') && form('stamp').value === '') {
			form('stamp').value = '0'; // 设置默认值为0（或其他默认值）
		}
	}, 100);
	
	/**
	 * 用于存储原来选择的数据，避免重复添加
	 * 在选择数据前会先记录当前已有的数据
	 */
	c.daossdts=[];
	

	
	/**
	 * 处理选择数据后的操作，实现重复检查功能
	 * @param {string} fid 字段ID
	 * @param {array|object} seld 选择的数据
	 * @param {string} sna 字段名称
	 * @param {string} sid 选择ID
	 */
	c.onselectdataall=function(fid,seld,sna,sid){
		if(!seld || !sna)return;
		var da = [];
		if(!seld[0]){
			da[0]=seld;
		}else{
			da = seld;
		}
		var nam = this.getxuandoi(fid),snua;
		var dao=this.daossdts,i,j,bo,d,oi=parseFloat(nam[1]),oii=-1;
		
		// 遍历所有选择的数据，进行重复检查
		for(i=0;i<da.length;i++){
			d = da[i];
			bo = false;
			
			// 检查是否已存在相同的物品ID
			for(j=0;j<dao.length;j++){
				if(dao[j].aid==d.value){
					bo=true;
					break;
				}
			}
			
			oii++;
			
			// 如果不是重复项，则添加到子表
			if(!bo){
				// 如果不是第一行，需要插入新行
				if(oii>0){
					snua= ''+nam[3]+''+nam[0]+'_'+(oi+oii)+'';
					if(!form(snua) || form(snua).value!=''){
						nam = this.insertrow(0,{},true);
					}else{
						nam[1]=parseFloat(nam[1])+1;
					}
				}
				
				// 设置行数据
				this.setrowdata(nam[0],nam[1],{
					unit:d.unit,
					price:d.price,
					temp_aid:d.name,
					aid:d.value,
					cost:d.cost,
					count:1  // 默认数量为1
				});
				
				// 设置数量输入框的最大值为库存数量，限制用户不能输入超过库存的数量
				$(form('count'+nam[2]+'')).attr('max', d.stock);
			}else{
				// 如果是重复项，减少计数器
				oii--;
				// 如果是第一个选择项且重复，清空当前行
				if(i==0){
					// 清空当前行数据，包括cost字段
				this.setrowdata(nam[0],nam[1],{
					unit:'',
					price:'0',
					temp_aid:'',
					aid:'0',
                    cost: ''
				});
				}
				// 物品已存在，跳过重复添加，并提示用户
				if(typeof js !== 'undefined' && js.msg) {
					js.msg('msg', '物品"' + d.name + '"已存在，不能重复添加！');
				} else {
					alert('物品"' + d.name + '"已存在，不能重复添加！');
				}
			}
		}
	}
    $(form('workid')).change(function(){
		var val = this.value,txt='';
		workidchange(val);
	});
	
	// 监听status字段变化，动态更新workid下拉选项
	$(form('status')).change(function(){
		refreshWorkidOptions();
	});
	
	// 页面加载时初始加载workid选项
	setTimeout(function(){
		refreshWorkidOptions();
		// 页面加载时如果时间字段已有值，自动计算时间差
		calculateTimeDifference();
	}, 500);
	
	// 添加开始时间和结束时间的change事件监听器，实现自动计算时间差
	$(form('startdt')).change(function(){
		calculateTimeDifference();
	});
	
	$(form('enddt')).change(function(){
		calculateTimeDifference();
	});
	
	// 重新定义onselectdatabefore函数，保持重复检查功能
	c.onselectdatabefore=function(fid){
		// 无论选择什么字段，都要记录当前子表数据以供重复检查
		this.daossdts = this.getsubdata(0);
		
		if(fid=='prname'){
			var val=form('workid').value;
			if(val>'0' && form('prname').value!='')return '任务已关联不需要重新选择';
		}
	}
}

/**
 * 计算开始时间和结束时间的时间差，并格式化为**时**分的格式
 */
function calculateTimeDifference(){
	// 检查recont字段是否存在
	if(!form('recont')) {
		return;
	}
	
	var startdt = form('startdt') ? form('startdt').value : '';
	var enddt = form('enddt') ? form('enddt').value : '';
	
	// 检查两个时间字段是否都有值
	if(!startdt || !enddt){
		form('recont').value = '';
		return;
	}
	
	try {
		// 处理不同的时间格式
		var startDate, endDate;
		
		// 如果包含空格，说明有时间部分
		if(startdt.indexOf(' ') > -1) {
			startDate = new Date(startdt.replace(/-/g, '/'));
		} else {
			// 只有日期，添加00:00:00
			startDate = new Date(startdt.replace(/-/g, '/') + ' 00:00:00');
		}
		
		if(enddt.indexOf(' ') > -1) {
			endDate = new Date(enddt.replace(/-/g, '/'));
		} else {
			// 只有日期，添加23:59:59
			endDate = new Date(enddt.replace(/-/g, '/') + ' 23:59:59');
		}
		
		// 检查日期是否有效
		if(isNaN(startDate.getTime()) || isNaN(endDate.getTime())){
			form('recont').value = '';
			return;
		}
		
		// 计算时间差（毫秒）
		var timeDiff = endDate.getTime() - startDate.getTime();
		
		// 如果结束时间早于开始时间，清空结果
		if(timeDiff < 0){
			form('recont').value = '';
			return;
		}
		
		// 转换为分钟
		var totalMinutes = Math.floor(timeDiff / (1000 * 60));
		
		// 计算天数、小时和分钟
		var days = Math.floor(totalMinutes / (24 * 60));
		var hours = Math.floor((totalMinutes % (24 * 60)) / 60);
		var minutes = totalMinutes % 60;
		
		// 格式化结果
		var result = '';
		if(days > 0) {
			result += days + '天';
		}
		if(hours > 0) {
			result += hours + '小时';
		}
		if(minutes > 0) {
			result += minutes + '分';
		}
		
		// 如果结果为空（时间差为0），显示0分钟
		if(result === '') {
			result = '0分钟';
		}
		
		// 设置到recont字段
		form('recont').value = result;
		
	} catch(e) {
		// 时间差计算异常时设置为空
		form('recont').value = '';
	}
}

function changesubmit(){
    /*
    if(get('tablesub0')){
        var da = c.getsubdata(0),d1;
        var chargeValue = form('charge') ? form('charge').value : '1';
        
        for(var i=0;i<da.length;i++){
            d1 = da[i];
            if(!d1.aid)return '行['+(i+1)+']必须选择物品';
            // 当charge字段值为0时，不执行数量大于0的验证逻辑
            if(chargeValue != '0' && d1.count<=0)return '行['+(i+1)+']数量必须大于0';
        }
    }
    */
}
function eventaddsubrows(xu,oj){
	c.setrowdata(xu,oj,{
		aid:'0'
	});
}
// 检查status状态，设置charge和money字段的状态
function checkStatusAndSetFields(){
	var status = form('status') ? form('status').value : '0';
	if(status === '1') {
		// status=1时，charge和money设为0且只读
		if(form('charge')) {
			form('charge').value = '0';
			form('charge').readOnly = true;
			form('charge').style.backgroundColor = '#f5f5f5';
		}
		if(form('money')) {
			form('money').value = '0';
			form('money').readOnly = true;
			form('money').style.backgroundColor = '#f5f5f5';
		}
	} else {
		// status不为1时，恢复charge和money字段的正常状态
		if(form('charge')) {
			form('charge').readOnly = false;
			form('charge').style.backgroundColor = '';
		}
		if(form('money')) {
			form('money').readOnly = false;
			form('money').style.backgroundColor = '';
		}
	}
	
	// 重新运行公式计算，确保按新的status状态执行
	if(typeof c !== 'undefined' && c.rungongsi) {
		c.rungongsi();
	}
}

function workidchange(v){
	if(v=='' || v=='0'){
		form('projectid').value='';
		form('prname').value='';
		form('address').value='';
        form('dist').value='';
		form('distid').value='';
		form('linkname').value='';
		form('fault').value='';
		form('startdt').value='';
		form('enddt').value='';
        // 维修前后图片字段清空
        if(form('imglod')) form('imglod').value='';
        if(form('imgnew')) form('imgnew').value='';
        // 同步更新图片上传控件的UI状态
        if(typeof c !== 'undefined' && typeof c.updateUploadImgButtons === 'function'){
            c.updateUploadImgButtons('imglod');
            c.updateUploadImgButtons('imgnew');
            if(typeof c.centerImage === 'function'){
                c.centerImage('imglod');
                c.centerImage('imgnew');
            }
        }
        // 清空图片预览
        if(get('imgview_imglod')) get('imgview_imglod').src = 'images/noimg.jpg';
        if(get('imgview_imgnew')) get('imgview_imgnew').src = 'images/noimg.jpg';
        form('process').value='';
		form('num').value='';
        form('custid').value='';
		// 清空时间差计算结果
		form('recont').value='';
		// 清空charge字段
		if(form('charge')) form('charge').value='';
		return;
	}
	js.ajax(geturlact('ractchange'),{ractid:v},function(a){
		form('projectid').value=a.projectid || '';
		form('prname').value=a.prname || '';
		form('address').value=a.address || '';
        form('dist').value=a.dist || '';
		form('distid').value=a.distid || '';
		form('linkname').value=a.linkname || '';
		form('fault').value=a.explain || '';
		form('startdt').value=a.startdt || '';
		form('enddt').value=a.enddt || '';
        // 维修前后图片字段处理
		if(form('imglod')) form('imglod').value = a.imglod || '';
		if(form('imgnew')) form('imgnew').value = a.imgnew || '';
        // 选择工单后，自动关联并更新图片上传控件的UI状态
        if(typeof c !== 'undefined' && typeof c.updateUploadImgButtons === 'function'){
            c.updateUploadImgButtons('imglod');
            c.updateUploadImgButtons('imgnew');
            if(typeof c.centerImage === 'function'){
                c.centerImage('imglod');
                c.centerImage('imgnew');
            }
        }
        // 实时更新图片预览
        if(a.imglod && get('imgview_imglod')){
            var lodSrc = a.imglod.split(',')[0];
            get('imgview_imglod').src = lodSrc;
        }
        if(a.imgnew && get('imgview_imgnew')){
            var newSrc = a.imgnew.split(',')[0];
            get('imgview_imgnew').src = newSrc;
        }
        form('process').value=a.workgc || '';
		form('num').value=a.num || '';
        form('custid').value=a.custid || '';
		
		// 将项目的workqy值填入charge字段
		if(form('charge') && a.workqy !== undefined) {
			form('charge').value = a.workqy || '';
		}
		
		// 当工单数据加载完成后，自动计算时间差
		calculateTimeDifference();
		
		// 检查status状态，设置字段状态
		checkStatusAndSetFields();
	},'get,json');
}
/**
 * 刷新workid下拉选项
 * 根据status字段的值来决定读取不同grade的工单
 */
function refreshWorkidOptions(){
	var status = form('status') ? form('status').value : '0';
	var currentWorkid = form('workid') ? form('workid').value : '0';
	
	// 检查status状态，设置字段状态
	checkStatusAndSetFields();
	
	// 使用Ajax获取工单数据
	var mid = '0';
	if(typeof c !== 'undefined' && c.mid) {
		mid = c.mid;
	} else if(typeof formid !== 'undefined') {
		mid = formid;
	}
	var ajaxUrl = geturlact('getMyWorkByStatus') + '&status=' + status + '&mid=' + mid + '&rnd=' + new Date().getTime();
	
	$.ajax({
		url: ajaxUrl,
		type: 'GET',
		dataType: 'json',
		success: function(ret){
			var workidSelect = form('workid');
			if(workidSelect) {
				// 保存当前选中的值
				var selectedValue = currentWorkid;
				
				// 清空现有选项，保留第一个空选项
				workidSelect.length = 1;
				
				if(ret && ret.length > 0) {
					for(var i = 0; i < ret.length; i++){
						var option = new Option(ret[i].name, ret[i].value);
						workidSelect.add(option);
					}
				}
				
				// 恢复之前选中的值
				if(selectedValue && selectedValue != '0') {
					workidSelect.value = selectedValue;
					// 如果恢复失败（值不在新的选项中），说明当前关联的工单不符合新的条件
					if(workidSelect.value != selectedValue) {
						// 通过Ajax获取当前工单的详细信息
						$.ajax({
							url: geturlact('ractchange') + '&ractid=' + selectedValue,
							type: 'GET',
							dataType: 'json',
							success: function(workInfo) {
								var workTitle = workInfo.title || '工单' + selectedValue;
								var workNum = workInfo.num || '';
								var displayName = workTitle + (workNum ? '.' + workNum : '');
								
								// 添加包含工单信息的选项
								var tempOption = new Option(displayName, selectedValue);
								workidSelect.add(tempOption);
								workidSelect.value = selectedValue;
							},
							error: function() {
								// 如果获取失败，使用默认显示
								var tempOption = new Option('工单' + selectedValue, selectedValue);
								workidSelect.add(tempOption);
								workidSelect.value = selectedValue;
							}
						});
					}
				}
			}
		},
		error: function(xhr, status, error){
			js.msg('msg', '加载工单数据失败，请重试');
		}
	});
}

// 重写子表操作方法，在操作后检查status状态
if(typeof c !== 'undefined' && c.setrowdata) {
	var originalSetRowData = c.setrowdata;
	c.setrowdata = function(){
		var result = originalSetRowData.apply(this, arguments);
		setTimeout(function(){
			checkStatusAndSetFields();
		}, 100);
		return result;
	};
}

if(typeof c !== 'undefined' && c.insertrow) {
	var originalInsertRow = c.insertrow;
	c.insertrow = function(){
		var result = originalInsertRow.apply(this, arguments);
		setTimeout(function(){
			checkStatusAndSetFields();
		}, 100);
		return result;
	};
}

if(typeof c !== 'undefined' && c.deleterow) {
	var originalDeleteRow = c.deleterow;
	c.deleterow = function(){
		var result = originalDeleteRow.apply(this, arguments);
		setTimeout(function(){
			checkStatusAndSetFields();
		}, 100);
		return result;
	};
}

// 重写公式运行函数，在status=1时完全禁用子表计算公式
if(typeof c !== 'undefined' && c.rungongsi) {
	var originalRungongsi = c.rungongsi;
	c.rungongsi = function(){
		var status = form('status') ? form('status').value : '0';
		
		if(status === '1') {
			// 保内服务单：完全禁用子表的计算公式，只运行主表非金额字段的公式
			if(typeof gongsiarr !== 'undefined' && gongsiarr.length > 0) {
				var i, len = gongsiarr.length, d;
				for(i = 0; i < len; i++){
					d = gongsiarr[i];
					// 跳过所有子表(iszb>0)的公式计算
					if(d.iszb > 0) {
						continue;
					}
					// 跳过主表的money和charge字段计算
					if(d.fields === 'money' || d.fields === 'charge') {
						continue;
					}
					// 只运行主表其他字段的公式
					if(d.iszb == 0 && form(d.fields)) {
						this.inputblur(form(d.fields), 0);
					}
				}
			}
			// 强制设置money和charge为0
			if(form('money')) form('money').value = '0';
			if(form('charge')) form('charge').value = '0';
		} else {
			// 非保内服务单：正常运行所有公式
			originalRungongsi.apply(this, arguments);
		}
	};
}

// 重写inputblur函数，在status=1时阻止子表字段的所有计算
if(typeof c !== 'undefined' && c.inputblur) {
	var originalInputblur = c.inputblur;
	c.inputblur = function(o1, zb){
		var status = form('status') ? form('status').value : '0';
		
		if(status === '1' && zb > 0) {
			// 保内服务单：完全阻止子表字段的计算
			return;
		}
		
		// 其他情况正常执行
		originalInputblur.apply(this, arguments);
	};
}
