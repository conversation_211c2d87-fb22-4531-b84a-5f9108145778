
function initbodys(){
	if(mid=='0'){
		if(form('cardid'))js.ajax(geturlact('getlast'),{},function(d){
			if(d){
				if(form('paytype'))form('paytype').value=d.paytype;
				if(form('cardid'))form('cardid').value=d.cardid;
				if(form('openbank'))form('openbank').value=d.openbank;
				if(form('fullname'))form('fullname').value=d.fullname;
			}
		},'get,json');
	}
}
function changesubmit(){
	var jg = parseFloat(form('money').value);
	if(jg<=0)return '申请金额不能小于0';
}
