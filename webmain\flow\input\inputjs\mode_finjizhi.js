//流程模块【finjishou.记账收入】下录入页面自定义js页面,初始函数
function initbodys(){
	var o1 = form('zhangid');
	if(mid==0){
		if(o1.length>1){
			o1.selectedIndex =1;
			changefinouts(0);
		}
	}else{
		changefinouts(1);
	}
	$(o1).change(function(){
		changefinouts(0);
	});
}
js.changeuser_after=function(na,o,sna,sid){
	if(na=='xgname' && form('xgdeptname')){
		js.ajax(geturlact('getuinfo'),{uid:sid}, function(ret){
			form('xgdeptname').value = ret.deptname;
			if(form('xgdeptid'))form('xgdeptid').value = ret.deptid;
		},'get,json');
	}
}
function changefinouts(lx){
	var id1 = form('zhangid').value;
	if(!id1)return;
	var o1  = form('accountid');
	js.ajax(geturlact('getaccount'),{id1:id1}, function(ret){
		var da = ret.data;
		o1.length = 1;
		js.setselectdata(o1,da,'value');
		if(lx==1)o1.value = data.accountid;
	},'get,json');
}