//流程模块【finkai.开票申请】下录入页面自定义js页面,初始函数
var oldcustid = '0';
var oldfullval = ''; // 声明全局变量
var kexuan = true; // 添加kexuan变量，控制是否可以选择

function initbodys(){
	try {
		// 安全检查表单字段是否存在
		if(form('fullname')){
			form('fullname').readOnly=false;
			form('fullname').onblur = function(){
				changeheits();
			};
			oldfullval = form('fullname').value || '';
		}
		
		if(form('custid')){
			oldcustid = form('custid').value || '0';
		}
		
		// 添加num字段的change事件监听（在此模块中，num字段即htid功能）
		if(form('num') && kexuan){
			$(form('num')).change(function(){
				var val = this.value;
				salechange(val);
			});
		}
		
		// 销售方公司选择 - 支持select下拉框和rockselect选择器
		if(form('name')){
			if(form('name').type === 'select-one') {
				// 下拉选择框类型，监听change事件
				$(form('name')).change(function(){
					var companyId = this.value;
					if(companyId && companyId !== '0') {
						// 调用自动填充功能
						fillCompanyInfo(companyId, this.options[this.selectedIndex].text);
					}
				});
				
				// 如果name字段有默认值，自动触发获取销售方信息
				var defaultValue = form('name').value;
				if(defaultValue && defaultValue !== '0' && defaultValue !== '') {
					// 延迟执行，确保页面完全加载
					setTimeout(function(){
						var nameField = form('name');
						if(nameField && nameField.selectedIndex >= 0) {
							var selectedText = nameField.options[nameField.selectedIndex].text;
							fillCompanyInfo(defaultValue, selectedText);
						}
					}, 100);
				}
			}
		}
		
	} catch(e) {
		console.error('initbodys初始化错误:', e);
	}
}

/**
 * 当选择合同/销售单/电子服务单时触发
 * 参考custfina模块的salechange函数实现
 */
function salechange(v){
	try {
		if(!kexuan) return;
		if(v=='' || v=='0'){
			if(form('custid')) form('custid').value='';
			if(form('fullname')) form('fullname').value='';
			return;
		}
		
		js.ajax(geturlact('ractchange'),{ractid:v},function(a){
			if(a && a.id){
				// 设置客户信息
				if(form('custid')) form('custid').value = a.id;
				if(form('fullname')) form('fullname').value = a.subname || a.name;
				
				// 更新全局变量
				oldcustid = a.id;
				oldfullval = a.subname || a.name;
				
				// 触发详细信息填充
				changegetother();
			}
		},'get,json');
		
	} catch(e) {
		console.error('salechange错误:', e);
	}
}

function changesubmit(){
	var jg = parseFloat(form('money').value);
	if(jg<=0)return '开票金额不能小于0';
}

c.onselectdata['fullname']=function(d){
	// 发票抬头应该保存unitname，即d.subname
	var nae = d.subname; // unitname作为subname返回
	if(isempt(nae))nae=d.name; // 如果unitname为空，才使用name
	form('fullname').value=nae;
	oldcustid = d.id;
	oldfullval = nae;
	// 设置客户ID并触发详细信息填充
	if(form('custid')){
		form('custid').value = d.id;
	}
	changegetother();
}

function changegetother(){
	if(!form('custid'))return;
	js.ajax(geturlact('getother'),{id:form('custid').value},function(d){
		if(d){
			if(form('address'))form('address').value=d.kpdzdh;
			if(form('cardid'))form('cardid').value=d.cardid;
			if(form('openbank')){
				// 组合开户行和账号
				var bankstr = '';
				if(d.openbank) bankstr += d.openbank;
				if(d.cardid){
					if(bankstr !== '') bankstr += ' ';
					bankstr += d.cardid;
				}
				form('openbank').value = bankstr;
			}
			if(form('shibieid'))form('shibieid').value=d.shibieid;
			if(form('paytype'))form('paytype').value=d.routeline;
		}
	},'get,json');
}

//判断是否用自己输入的
function changeheits(){
	if(!form('custid'))return;
	var val = form('fullname').value;
	if(oldfullval!=val){
		form('custid').value='0';
	}else{
		form('custid').value=oldcustid;
	}
}

/**
 * 程序化选择合同/销售单/电子服务单的函数
 * @param {string|number} ractid - 合同ID(正数)、销售单ID(负数)或电子服务单ID(正数)
 */
function selectFromRact(ractid) {
	try {
		if(!ractid || ractid == '0') {
			return;
		}
		
		// 如果有num字段，设置其值并触发change事件
		if(form('num')) {
			form('num').value = ractid;
			salechange(ractid);
		} else {
			// 如果没有num字段，直接调用ractchange接口
			js.ajax(geturlact('ractchange'),{ractid:ractid},function(a){
				if(a && a.id){
					// 触发fullname的选择回调
					if(c.onselectdata && c.onselectdata['fullname']){
						c.onselectdata['fullname'](a);
					}
				}
			},'get,json');
		}
		
	} catch(e) {
		console.error('selectFromRact错误:', e);
	}
}

/**
 * 销售方公司信息自动填充函数
 * @param {string|number} companyId - 公司ID
 * @param {string} companyName - 公司名称（可选）
 */
function fillCompanyInfo(companyId, companyName) {
	try {
		if(!companyId || companyId === '0') return;
		
		// 调用后端接口获取公司详细信息
		js.ajax(geturlact('companyinfo'), {id:companyId}, function(ret){
			if(ret){
				// 自动填充纳税人识别号
				if(form('xshibieid') && ret.shibieid){
					form('xshibieid').value = ret.shibieid;
				}
				
				// 自动填充开户行和账号（组合显示）
				/* if(form('xopenbank')){
					var bankstr = '';
					if(ret.openbank) bankstr += ret.openbank;
					if(ret.cardid){
						if(bankstr !== '') bankstr += ' ';
						bankstr += ret.cardid;
					}
					if(bankstr !== '') {
						form('xopenbank').value = bankstr;
					}
				} */
				// 自动填充开户行
				if(form('xopenbank') && ret.openbank){
					form('xopenbank').value = ret.openbank;
				}
				// 自动填充账号
				if(form('cardid') && ret.cardid){
					form('cardid').value = ret.cardid;
				}
			}
		}, 'get,json');
	} catch(e) {
		console.error('填充公司信息错误:', e);
	}
}

/**
 * 销售方公司选择回调函数（用于rockselect选择器）
 * 当用户选择销售方公司时，自动填充相关信息
 * 包括：销售方名称、纳税人识别号、开户行账号等
 */
c.onselectdata['name'] = function(d){
	try{
		// 验证选择数据的有效性
		if(d && (d.value || d.id)){
			var companyId = d.value || d.id;
			
			// 首先设置选择的公司名称
			if(form('name') && d.name){
				form('name').value = d.name;
			}
			
			// 调用通用填充函数
			fillCompanyInfo(companyId, d.name);
		}
	}catch(e){
		console.error('销售方公司选择回调错误:', e);
	}
}

