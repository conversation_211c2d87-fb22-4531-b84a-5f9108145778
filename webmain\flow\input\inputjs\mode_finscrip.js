//流程模块【finscrip.凭证管理】下录入页面自定义js页面,初始函数
var jizhangmoney = 0,jizhangid='';
function initbodys(){
	if(mid==0){
		c.addrow(false,0);
		var o1 = form('zhangid');
		jizhangid = js.request('jizhangid');
		if(o1.length>1){
			o1.selectedIndex =1;
			if(!jizhangid)changebianhao();
		}
		if(jizhangid)loadjizhanginfo();
	}
	changetitle();
	$(form('type')).change(function(){
		changetitle();
		changebianhao();
	});
	$(form('zhangid')).change(function(){
		changebianhao();
	});
}

function changetitle(){
	var str = '记账凭证';
	var val = form('type').value;
	if(val=='1')str='收款凭证';
	if(val=='2')str='付款凭证';
	if(val=='3')str='转账凭证';
	document.title = str;
	$('#inputtitle').html(str);
}

function changesubmit(d){
	if(jizhangmoney>0 && parseFloat(d.moneyjie)!=jizhangmoney)return '借方金额跟记账金额不一样';
	if(parseFloat(d.moneyjie)<0)return '借方金额必须大于0';
	if(parseFloat(d.moneyjie)!=parseFloat(d.moneydai))return '借方金额和贷方金额不一样';
}

var yunboolstr = false;
function oninputblur(na,zb,o1,lx1,lx2){
	if(yunboolstr)return;
	if(zb==1 && (na=='moneysjie' || na=='moneysdai')){
		var ovj1 = form('moneysjie'+lx1+'_'+lx2+'');
		var ovj2 = form('moneysdai'+lx1+'_'+lx2+'');
		if(parseFloat(o1.value)>0 && na=='moneysjie')ovj2.value='0';
		if(parseFloat(o1.value)>0 && na=='moneysdai')ovj1.value='0';
		if(!yunboolstr){
			yunboolstr = true;
			c.inputblur(ovj1, zb);
			c.inputblur(ovj2, zb);
			form('moneycn').value=AmountInWords(form('moneyjie').value);
			yunboolstr = false;
		}
	}
	if(zb==0 && na=='dt')changebianhao();
}

//生成编号
function changebianhao(){
	var da = {dt:form('dt').value,type:form('type').value,zhangid:form('zhangid').value};
	if(!da.type || !da.zhangid)return;
	js.ajax(geturlact('createbh'),da, function(ret){
		if(ret.success){
			form('dt').value = ret.data.dt;
			form('bh').value = ret.data.bh;
		}
	},'get,json');
}

c.onselectdatabefore=function(zd){
	if(zd=='temp_yinru'){
		if(jizhangmoney>0)return '记账信息不需要选模版';
		if(mid>0)return '已保存凭证就不需要选模版了';
	}
}

c.onselectdataall=function(zd,seld){
	if(zd=='temp_yinru'){
		if(seld.lista)addzibiao(seld.lista);
	}
}

function addzibiao(da){
	var snua;
	for(var i=0;i<da.length;i++){
		snua= 'zhaiyao0_'+i+'';
		if(!form(snua))c.insertrow(0,{},true);
		c.setrowdata(0,i,da[i]);
	}
	form('moneycn').value=AmountInWords(form('moneyjie').value);
}

//加载记账信息
function loadjizhanginfo(){
	js.ajax(geturlact('jizhanginfo'),{id:jizhangid}, function(ret){
		if(ret.success){
			var da = ret.data;
			form('moneyjie').value = da.money;
			form('moneydai').value = da.money;
			jizhangmoney = parseFloat(da.money);
			form('zhangid').value = da.zhangid;
			if(form('jzid'))form('jzid').value = da.id;
			form('dt').value = da.applydt;
			if(da.lista)addzibiao(da.lista);
			changebianhao();
		}else{
			js.msgerror(ret.msg);
			c.formdisabled();
			$('#AltSspan').hide();
		}
	},'get,json');	
}