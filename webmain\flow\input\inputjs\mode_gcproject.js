//流程模块【gcproject.项目管理】下录入页面自定义js页面,初始函数
var tbxmid = '0';
function initbodys(){
	if(isinput==1){
		var proid = form('pgcid').value;
		if(mid==0){
			if(proid && proid>0)changxuan(proid);
		}
		
		var tbxm = js.request('tbxm');
		if(tbxm && mid==0)loadtbxm(tbxm);
	}
}

function changesubmit(){
	return {sstbxmid:tbxmid}
}

c.onselectdata['pgcname']=function(da,sna,sid){
	if(sid>0)changxuan(sid);
}

function changxuan(sid){
	js.ajax(geturlact('proinfo'),{sid:sid},function(ret){
		for(var i in ret)if(form(i))form(i).value=ret[i];
	},'get,json');
}

function loadtbxm(id1){
	js.ajax(geturlact('tbxminfo'),{tbid:id1},function(ret){
		tbxmid = id1;
		for(var i in ret)if(form(i))form(i).value=ret[i];
	},'get,json');
}