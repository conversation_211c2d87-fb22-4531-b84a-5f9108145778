//流程模块【gczlxun.质量巡检】下录入页面自定义js页面,初始函数
function initbodys(){
	if(isinput==1){
		if(form('qingkuan') && form('qingkuan').value==''){
			$('#fileview_qingkuan').after('<div><a href="javascript:;" onclick="xuanwenj(this)" class="blue">＋选择模版文件</a>(选的是文档协作中分类为工程文件的)</div>');
		}
	}
}


function xuanwenj(o1){
	var ne = form('weizhi').value;
	if(!ne){
		js.msg('msg','请先填写标题');
		return;
	}
	c.xuanfile('qingkuan','工程文件',''+ne+'的文件',o1);
}

c.uploadfileibefore=function(sna){
	if(sna=='qingkuan'){
		var val = form(sna).value;
		if(val)return '最多只能上传一个文件哦';
	}
}
