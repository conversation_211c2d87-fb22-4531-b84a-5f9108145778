//初始函数
function initbodys(){
	// 恢复上次选择的分类
	restoreTypeSelection();
	// 恢复上次选择的hjid和hjbh
	restoreHjSelection();
	
	$(form('typeid')).change(function(){
		getfilenum();
		// 保存分类选择
		saveTypeSelection();
	});
	
	// 监听hjid字段变化
	if(form('hjid')){
		$(form('hjid')).change(function(){
			// 保存hjid选择
			saveHjSelection();
			// 生成num字段
			generateNumField();
		});
	}
	
	// 监听hjbh字段变化
	if(form('hjbh')){
		$(form('hjbh')).change(function(){
			// 保存hjbh选择
			saveHjSelection();
			// 生成num字段
			generateNumField();
		});
	}
}

// 保存分类选择到localStorage
function saveTypeSelection(){
	if(typeof(Storage) !== 'undefined'){
		var typeid = form('typeid').value;
		if(typeid){
			// 使用用户ID隔离localStorage
			var storageKey = 'goods_input_typeid_' + (typeof adminid !== 'undefined' ? adminid : '0');
			localStorage.setItem(storageKey, typeid);
		}
	}
}

// 恢复分类选择
function restoreTypeSelection(){
	if(typeof(Storage) !== 'undefined' && form('typeid')){
		// 使用用户ID隔离localStorage
		var storageKey = 'goods_input_typeid_' + (typeof adminid !== 'undefined' ? adminid : '0');
		
		// 检查是否有def_typeid参数（来自列表页的默认值）
		var defTypeId = js.request('def_typeid');
		if(defTypeId){
			// 如果有默认值，使用默认值并保存
			form('typeid').value = defTypeId;
			localStorage.setItem(storageKey, defTypeId);
			getfilenum(); // 生成文件编号
			return;
		}
		
		// 如果没有默认值，检查当前表单是否已有值
		var currentValue = form('typeid').value;
		if(!currentValue || currentValue === ''){
			// 如果当前没有值，尝试恢复上次选择
			var savedTypeId = localStorage.getItem(storageKey);
			if(savedTypeId){
				form('typeid').value = savedTypeId;
				getfilenum(); // 生成文件编号
			}
		}
	}
}

//得到文件编号：类别+年份+三位编号
function getfilenum(){
	var type = form('typeid').value;
	if(type==''){
		return;
	}
	
	js.ajax(geturlact('getfilenum'),{type:type},function(s){
		form('xinghao').value=s;
	},'get');
}

// 保存hjid和hjbh选择到localStorage
function saveHjSelection(){
	if(typeof(Storage) !== 'undefined'){
		var hjid = form('hjid') ? form('hjid').value : '';
		var hjbh = form('hjbh') ? form('hjbh').value : '';
		
		// 使用用户ID隔离localStorage
		var storageKeyHjid = 'goods_input_hjid_' + (typeof adminid !== 'undefined' ? adminid : '0');
		var storageKeyHjbh = 'goods_input_hjbh_' + (typeof adminid !== 'undefined' ? adminid : '0');
		
		if(hjid){
			localStorage.setItem(storageKeyHjid, hjid);
		}
		if(hjbh){
			localStorage.setItem(storageKeyHjbh, hjbh);
		}
	}
}

// 恢复hjid和hjbh选择
function restoreHjSelection(){
	if(typeof(Storage) !== 'undefined'){
		// 使用用户ID隔离localStorage
		var storageKeyHjid = 'goods_input_hjid_' + (typeof adminid !== 'undefined' ? adminid : '0');
		var storageKeyHjbh = 'goods_input_hjbh_' + (typeof adminid !== 'undefined' ? adminid : '0');
		
		// 检查是否有def_hjid参数（来自列表页的默认值）
		var defHjid = js.request('def_hjid');
		var defHjbh = js.request('def_hjbh');
		
		// 恢复hjid
		if(form('hjid')){
			if(defHjid){
				// 如果有默认值，使用默认值并保存
				form('hjid').value = defHjid;
				localStorage.setItem(storageKeyHjid, defHjid);
			} else {
				// 如果没有默认值，检查当前表单是否已有值
				var currentHjid = form('hjid').value;
				if(!currentHjid || currentHjid === ''){
					// 如果当前没有值，尝试恢复上次选择
					var savedHjid = localStorage.getItem(storageKeyHjid);
					if(savedHjid){
						form('hjid').value = savedHjid;
					}
				}
			}
		}
		
		// 恢复hjbh
		if(form('hjbh')){
			if(defHjbh){
				// 如果有默认值，使用默认值并保存
				form('hjbh').value = defHjbh;
				localStorage.setItem(storageKeyHjbh, defHjbh);
			} else {
				// 如果没有默认值，检查当前表单是否已有值
				var currentHjbh = form('hjbh').value;
				if(!currentHjbh || currentHjbh === ''){
					// 如果当前没有值，尝试恢复上次选择
					var savedHjbh = localStorage.getItem(storageKeyHjbh);
					if(savedHjbh){
						form('hjbh').value = savedHjbh;
					}
				}
			}
		}
		
		// 初始化时生成num字段
		generateNumField();
	}
}

// 生成num字段内容为hjbh+hjid的值
function generateNumField(){
	if(form('num')){
		var hjbh = form('hjbh') ? form('hjbh').value : '';
		var hjid = form('hjid') ? form('hjid').value : '';
		
		// 组合hjbh和hjid的值，中间用-连接
		var numValue = '';
		if(hjbh && hjid){
			numValue = hjbh + '-' + hjid;
		} else if(hjbh){
			numValue = hjbh;
		} else if(hjid){
			numValue = hjid;
		}
		
		// 设置num字段的值
		form('num').value = numValue;
	}
}