//流程模块【jxcstock.商品出入库】下录入页面自定义js页面,初始函数
function initbodys(){
	
	//记录原来选择的
	c.daossdts=[];
	c.onselectdatabefore=function(){
		this.daossdts = this.getsubdata(0);
	}
	
	//这个是很复杂的叠加关系，时间久了谁也不知道是干嘛用的
	c.onselectdataall=function(fid,seld,sna,sid){
		if(!seld || !sna)return;
		var da = [];
		if(!seld[0]){
			da[0]=seld;
		}else{
			da = seld;
		}
		var nam = this.getxuandoi(fid),snua;
		var dao=this.daossdts,i,j,bo,d,oi=parseFloat(nam[1]),oii=-1;
		for(i=0;i<da.length;i++){
			d  = da[i];
			bo = false;
			//for(j=0;j<dao.length;j++)if(dao[j].aid==d.value)bo=true;
			oii++;
			if(!bo){
				if(oii>0){
					snua= ''+nam[3]+''+nam[0]+'_'+(oi+oii)+'';
					if(!form(snua) || form(snua).value!=''){
						nam = this.insertrow(0,{},true);
					}else{
						nam[1]=parseFloat(nam[1])+1;
					}
				}
				this.setrowdata(nam[0],nam[1],{
					unit:d.unit,
					price:d.price,
					temp_aid:d.name,
					aid:d.value
				});
				
			}else{
				oii--;
				if(i==0){
					this.setrowdata(nam[0],nam[1],{
						unit:'',
						price:'0',
						temp_aid:'',
						aid:'0'
					});
				}
			}	
		}
	}
	
	//读取客户
	$(form('htid')).change(function(){
		readkehugs(this.value)
	});
	
	//读取计划里面的信息
	if(mid==0 && form('otherid')){
		var otherid = js.request('otherid');
		if(otherid)readjihua(otherid);
	}
}


function eventaddsubrows(xu,oj){
	c.setrowdata(xu,oj,{
		aid:'0'
	});
}

function readkehugs(id1){
	if(!id1)return;
	js.ajax(geturlact('gethtinfo'),{id:id1},function(ret){
		if(form('custid'))form('custid').value = ret.custid;
		if(form('custname'))form('custname').value = ret.custname;
		if(form('money'))form('money').value = ret.money;
	},'get,json')
}

function readjihua(id1){
	if(!id1)return;
	js.ajax(geturlact('getjhinfo'),{id:id1},function(ret){
		var mrs = ret.mrs;
		if(!mrs)return;
		if(form('custid'))form('custid').value = mrs.custid;
		if(form('custname'))form('custname').value = mrs.custname;
		if(form('money'))form('money').value = mrs.money;
		if(form('discount'))form('discount').value = mrs.discount;
		if(form('explain'))form('explain').value = mrs.explain;
		if(form('otherid'))form('otherid').value = mrs.id;
		var zbd = ret.zbrow
		if(zbd){
			for(var i=0;i<zbd.length;i++){
				if(i>0)c.insertrow(0,{},true);
				c.setrowdata(0,i,{
					unit:zbd[i].unit,
					price:zbd[i].price,
					count:zbd[i].count,
					temp_aid:zbd[i].temp_aid,
					aid:zbd[i].aid
				});
			}
		}
	},'get,json')
}