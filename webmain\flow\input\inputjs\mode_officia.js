//初始函数
function initbodys(){
	
	if(form('zinum'))$(form('zinum')).change(function(){
		getfilenum();
	});
	
	if(isinput==1){
		$('#inputtitle').css('color','red');
		$('body').append('<style>.ys1,.ys2{border-color:red;color:red}</style>');
		if(form('unitname'))form('unitname').readOnly=false;
		if(form('chaoname'))form('chaoname').readOnly=false;
		
		if(form('filecontid') && ismobile==0 && form('filecontid').value==''){
			$('#fileview_filecontid').after('<div><a href="javascript:;" onclick="editwords(this)" class="blue">＋使用Word在线编辑</a></div>');
		}
	}
}

//最多只能1个正文
c.uploadfileibefore=function(sna){
	if(sna=='filecontid'){
		var val = form(sna).value;
		//if(val)return '最多只能1个正文文件哦';
	}
}

function editwords(o1){
	var tit = form('title').value;
	if(!tit){
		js.msg('msg','请先填写标题');
		return;
	}
	o1.remove();
	js.ajax(geturlact('createword'),{tit:tit,mid:mid},function(ret){
		c.showfileup('filecontid',ret);
		c.showupid('filecontid');
		js.fileopt(ret.id,2);
	},'post,json');
}

//得到文件编号：类别+年份+三位编号
function getfilenum(){
	var type = form('zinum').value;
	if(!form('num'))return;
	if(type==''){
		form('num').value='';
		return;
	}
	
	js.ajax(geturlact('getfilenum'),{type:type},function(s){
		form('num').value=s;
	},'post');
}
