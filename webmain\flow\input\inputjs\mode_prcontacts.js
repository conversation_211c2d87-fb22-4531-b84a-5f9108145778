//流程模块【prcontacts.项目联系人】下录入页面自定义js页面,初始函数
function initbodys(){
	// 监听姓名输入框的 input 事件，当姓名被清空时，清空电话、称呼和职位
	const givenNameInput = document.querySelector('input[name="given_name"]');
	if(givenNameInput){
		givenNameInput.addEventListener('input', function(event) {
			if (event.target.value === '') {
				const mobileInput = document.querySelector('input[name="mobile"]');
				if(mobileInput) mobileInput.value = '';

				const honorificInput = document.querySelector('input[name="honorific"]');
				if(honorificInput) honorificInput.value = '';

				const positionInput = document.querySelector('input[name="position"]');
				if(positionInput) positionInput.value = '';
			}
		});
	}
	// 获取姓名输入框，并绑定 click 事件以触发展示联系人列表
	const contactNameInputElement = document.querySelector('input[name="given_name"]');
	// 绑定click事件
    contactNameInputElement.addEventListener('click', function(event) {
        autocomplete(this, 'contactsNameData');
    });

    //获取客户名称
    const input = document.querySelector('input[name="projectid"]');
    
    // 确保custid隐藏字段存在，如果不存在则创建
    let custidInput = document.querySelector('input[name="custid"]');
    if(!custidInput) {
        custidInput = document.createElement('input');
        custidInput.type = 'hidden';
        custidInput.name = 'custid';
        custidInput.value = '';
        // 将隐藏字段添加到表单中
        const form = input.closest('form');
        if(form) {
            form.appendChild(custidInput);
        }
    }

// 获取原生 value 属性的描述符
    const descriptor = Object.getOwnPropertyDescriptor(HTMLInputElement.prototype, 'value');
// 重写 value 的 setter
    Object.defineProperty(input, 'value', {
        set: function(newValue) {
            const oldValue = this.value;
            descriptor.set.call(this, newValue); // 调用原生 setter
            if (oldValue !== newValue) {
                this.dispatchEvent(new Event('change')); // 手动触发事件
            }
        },
        get: function() {
            return descriptor.get.call(this); // 保持原生 getter
        }
    });
// 监听 change 事件
input.addEventListener('change', function(e) {
    if(typeof rockselectdata !== 'undefined'){
        rockselectdata['given_name'] = []; // 清空rockselect的缓存
    }
    
    // 检查是否为编辑模式（通过检查是否已有联系人姓名来判断）
    var given_nameInput = document.querySelector('input[name="given_name"]');
    var isEditMode = given_nameInput && given_nameInput.value.trim() !== '';
    
    // 只在非编辑模式下清空联系人信息
    if(!isEditMode) {
        if(given_nameInput) given_nameInput.value = '';
        var mobileInput = document.querySelector('input[name="mobile"]');
        if(mobileInput) mobileInput.value = '';
        var honorificInput = document.querySelector('input[name="honorific"]');
        if(honorificInput) honorificInput.value = '';
        var positionInput = document.querySelector('input[name="position"]');
        if(positionInput) positionInput.value = '';
    }
    
    // 获取项目对应的客户ID
    var projectid = this.value;
    if(projectid && projectid !== '') {
        // 调用后端方法获取项目信息，包含客户ID
        var param = {'pid': projectid};
        var url = js.getajaxurl('getprojectoneAjax', 'mode_prcontacts|input', 'flow', param);
        js.ajax(url, function(data) {
            console.log('[项目选择] 返回数据:', data);
            if(data && data.custid) {
                // 设置客户ID到隐藏字段
                var custidInput = document.querySelector('input[name="custid"]');
                if(custidInput) {
                    custidInput.value = data.custid;
                    console.log('[项目选择] 已设置custid:', data.custid);
                    
                    // 触发custid字段的change事件，以便其他逻辑能感知到变化
                    custidInput.dispatchEvent(new Event('change'));
                } else {
                    console.warn('[项目选择] 未找到custid字段');
                }
                
                // 清空rockselect缓存，以便重新加载联系人列表
                if(typeof rockselectdata !== 'undefined') {
                    rockselectdata['given_name'] = [];
                }
            } else {
                console.warn('[项目选择] 返回数据无效或无custid:', data);
            }
        });
    } else {
        // 如果项目ID为空，清空客户ID
        var custidInput = document.querySelector('input[name="custid"]');
        if(custidInput) {
            custidInput.value = '';
        }
    }
});

function autocomplete(o1,s1){
    var a1 	= s1.split(',');
    const custid = $("input[name='custid']").val(); // 获取客户ID
    const projectid = $("input[name='projectid']").val(); // 获取项目ID
    var gcan = {
        'act':a1[0],
        'actstr':jm.base64encode(s1),
        'acttyle':'act',
        'sysmodenum':'contacts',
        'custid':custid, // 传递客户ID
        'projectid':projectid, // 传递项目ID
        'rel_type':2 // 项目联系人关联类型
    };
    var url = js.getajaxurl('getselectdata','mode_contacts|input','flow',gcan);
    js.chajian('rockselect', {
        viewobj:o1,num:o1.name,limit:10,url:url,zb:0,strsss:s1,
        onitemclick:function(sna,val, d){
            var fid= this.nameobj.name;
            // 将纯姓名填充到输入框
            if(this.nameobj) this.nameobj.value = d.given_name_original || val.split('(')[0]; 

            var a1 = this.strsss.split(',');
            // 下面这行原来的逻辑是 if(a1[1])if(form(a1[1]))form(a1[1]).value = val;
            // 由于我们现在用 d.given_name_original 填充主输入框，如果 a1[1] 存在并且是其他字段，
            // 它也应该被正确处理。如果 a1[1] 就是主姓名输入框的name, 则上面的赋值已经处理了。
            // 通常 a1[1] 是用来填充ID之类的隐藏字段，所以用 d.id 更合适。
            // 为了安全起见，如果 a1[1] 存在，我们假设它是ID字段，用 d.id 填充。
            // 如果原始逻辑是填充name，那么需要根据具体情况调整。
            // 暂时保留原始逻辑，但优先使用 d.given_name_original
            if(a1[1] && form(a1[1])){
                // 如果a1[1]是主姓名输入框，则上面已经通过this.nameobj.value赋值
                // 否则，如果它是其他字段，比如ID字段，则用d.id填充
                if(form(a1[1]).name === this.nameobj.name){
                    // 已经被 d.given_name_original 填充
                } else {
                    // 假设是ID字段，用d.id填充，如果不是，需要根据实际情况调整
                     if(d.id) form(a1[1]).value = d.id; 
                }
            } else if (this.nameobj) {
                 //确保主输入框被正确填充
                 this.nameobj.value = d.given_name_original || val.split('(')[0]; 
            }

            c.onselectdataall(fid,d);
            $("input[name='mobile']").val(d.mobile);
            // 填充称呼，假设返回数据d中包含honorific字段，表单字段name为honorific
            if(d.hasOwnProperty('honorific')){
                $("input[name='honorific']").val(d.honorific);
            }
            // 填充职务，假设返回数据d中包含position字段，表单字段name为position
            if(d.hasOwnProperty('position')){
                $("input[name='position']").val(d.position);
            }
            console.log('[DEBUG] onitemclick - selected data:', d);
        },
        nameobj:o1
    });
}

	// 页面加载时，如果 projectid 有值，则触发一次 change 事件以加载 custid 和联系人
	const initialProjectid = input.value;
	if(initialProjectid && initialProjectid !== ''){
		input.dispatchEvent(new Event('change'));
	}
}