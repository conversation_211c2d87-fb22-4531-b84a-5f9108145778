//初始函数
function initbodys(){
    //设置验收到期日期
    var setenddt = function () {
        var date = form('ysdt').value;
        var month = form('expdata').value;
        js.ajax(geturlact('getenddt'),{date:date,month:month},function(ret){
            if(ret){
                form('enddt').value = ret.enddt;
            }
        },'get,json');
    };
    
    //设置验收日期默认值为立项日期+7天 - 已取消自动赋值
    /*
    var setYsdtDefault = function () {
        var startDate = form('startdt').value;
        
        // 如果开始日期为空，清空验收日期
        if (!startDate) {
            form('ysdt').value = '';
            return;
        }
        
        // 只有在验收日期为空时才自动设置
        if (!form('ysdt').value) {
            var startDateObj = new Date(startDate);
            var ysDateObj = new Date(startDateObj);
            ysDateObj.setDate(startDateObj.getDate() + 7); // 加7天
            
            var year = ysDateObj.getFullYear();
            var month = String(ysDateObj.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1
            var day = String(ysDateObj.getDate()).padStart(2, '0');
            
            var newDate = year + '-' + month + '-' + day;
            form('ysdt').value = newDate;
            
            //验收日期变化后，重新计算保修到期日期
            setenddt();
        }
    };
    */

    //验收日期 + 保修月份总天数 = 保修到期的日期
    $(form('expdata')).change(function(){
        setenddt();
    });
    $(form('expdata')).keyup(function(){
        setenddt();
    });
    js.onchangedate = function(){
        setenddt();
    }; //选择时间回调
    
    //立项日期变化时，自动设置验收日期为立项日期+7天 - 已取消自动赋值
    /*
    $(form('startdt')).change(function(){
        setYsdtDefault();
    });
    $(form('startdt')).blur(function(){
        setYsdtDefault();
    });
    */
    
    // 不在页面初始化时自动设置验收日期，只在立项日期修改后才设置

    //表单管理联动
// c.selectmap()
    // 获取目标input元素
    const inputElement = document.querySelector('input[name="linkname"]');

// 绑定click事件
    inputElement.addEventListener('click', function(event) {
        autocomplete(this, 'contactsNameData');
    });

    //获取客户名称
    const input = document.querySelector('input[name="custid"]');

// 获取原生 value 属性的描述符
    const descriptor = Object.getOwnPropertyDescriptor(HTMLInputElement.prototype, 'value');
// 重写 value 的 setter
    Object.defineProperty(input, 'value', {
        set: function(newValue) {
            const oldValue = this.value;
            descriptor.set.call(this, newValue); // 调用原生 setter
            if (oldValue !== newValue) {
                this.dispatchEvent(new Event('change')); // 手动触发事件
            }
        },
        get: function() {
            return descriptor.get.call(this); // 保持原生 getter
        }
    });

// 监听 change 事件
    input.addEventListener('change', function(e) {
        if(rockselectdata !== undefined){
            rockselectdata['linkname'] = []; // 清空rockselect的缓存
        }
        // 清空联系人输入框
        form('linkname').value = '';
        form('linktel').value = '';
    });

    autocomplete = function(o1,s1){
        var a1 = s1.split(',');
        const custid = $("input[name='custid']").val();
        // 修改：使用contactsNameData方法获取客户联系人，custid作为URL参数传递
        var gcan = {'act':'contactsNameData','actstr':jm.base64encode('contactsNameData'),'acttyle':'act','sysmodenum':'contacts','sysmid':mid};
        var url = js.getajaxurl('getselectdata','mode_contacts|input','flow',gcan);
        // 将custid作为URL参数添加到请求中
        if(custid && custid != ''){
            url += '&custid=' + encodeURIComponent(custid);
        }
        js.chajian('rockselect', {
            viewobj:o1,num:o1.name,limit:10,url:url,zb:0,strsss:s1,
            onitemclick:function(sna,val, d){
                var fid= this.nameobj.name;
                var a1 = this.strsss.split(',');
                // 使用d.value（纯姓名）设置输入框的值，而不是sna（带电话号码的显示名）
                this.nameobj.value = d.value || d.given_name_original || sna;
                if(a1[1])if(form(a1[1]))form(a1[1]).value = val
                c.onselectdataall(fid,d);
                // 修改：使用返回数据中的mobile字段设置联系电话
                $("input[name='linktel']").val(d.mobile || '')
            },
            nameobj:o1
        });
    }
}