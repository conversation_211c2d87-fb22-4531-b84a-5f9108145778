//流程模块【wyfang.房屋管理】下录入页面自定义js页面
// 全局变量，存储楼栋信息数组
var louarr = [];

/**
 * 初始化函数，绑定表单元素事件
 * 1. 绑定楼栋ID下拉框change事件
 * 2. 绑定小区ID下拉框change事件
 * 3. 初始化楼栋信息数组
 * 4. 如果存在临时业主ID，则设置其值
 */
function initbodys(){
	// 楼栋ID下拉框change事件
	$(form('louid')).change(function(){
		// 获取选中楼栋对应的小区ID
		var xid = c.getselattr('louid','xqid');
		// 如果存在小区ID，则设置小区ID下拉框的值
		if(xid)form('xqid').value = xid;
	});
	
	// 小区ID下拉框change事件，触发楼栋数量变化
	$(form('xqid')).change(function(){
		changeloushu();
	});
	
	// 初始化楼栋信息数组
	var carr = form('louid'),i,b2,b1;
	for(var i=1;i<carr.length;i++){
		b1 = carr.options[i];
		b2 = $(b1);
		// 将楼栋信息存入数组
		louarr.push({
			'xqid' :b2.attr('xqid'), // 小区ID
			'value' : b1.value,      // 楼栋ID
			'name' : b1.text,        // 楼栋名称
		});
	}
	
	// 如果存在临时业主ID且mid>0(编辑模式)，则设置其值
	if(mid>0 && form('temp_yezhuid'))
		form('temp_yezhuid').value = data.temp_yezhuid;
}

/**
 * 根据小区ID改变楼栋下拉框选项
 * 1. 筛选出属于当前小区的楼栋
 * 2. 重置楼栋下拉框
 * 3. 设置新的楼栋选项
 */
function changeloushu(){
	// 筛选出属于当前小区的楼栋
	var garr = [];
	var xid = form('xqid').value;
	for(var i=0;i<louarr.length;i++){
		if(louarr[i].xqid==xid)garr.push(louarr[i]);
	}
	
	// 重置楼栋下拉框
	var carr = form('louid');
	carr.length = 1; // 只保留第一个空选项
	
	// 设置新的楼栋选项
	js.setselectdata(carr,garr,'value');
}