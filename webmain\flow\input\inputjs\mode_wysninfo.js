//流程模块【wysninfo.设备档案】下录入页面自定义js页面,初始函数
var louarr = [];
function initbodys(){
	
	
	$(form('louid')).change(function(){
		var xid = c.getselattr('louid','xqid');
		if(xid)form('xqid').value = xid;
	});
	$(form('xqid')).change(function(){
		changeloushu();
	});
	var carr = form('louid'),i,b2,b1;
	for(var i=1;i<carr.length;i++){
		b1 = carr.options[i];
		b2 = $(b1);
		louarr.push({
			'xqid' :b2.attr('xqid'),
			'value' : b1.value,
			'name' : b1.text,
		});
	}

}

function changeloushu(){
	var garr = [];
	var xid = form('xqid').value;
	for(var i=0;i<louarr.length;i++){
		if(louarr[i].xqid==xid)garr.push(louarr[i]);
	}
	//console.log(garr);
	var carr = form('louid');
	carr.length = 1;
	js.setselectdata(carr,garr,'value');
}