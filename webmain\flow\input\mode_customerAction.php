<?php
/**
*	此文件是流程模块【customer.客户】对应控制器接口文件。
*/ 
class mode_customerClassAction extends inputAction{
	
	protected function savebefore($table, $arr, $id, $addbo){
		if(!$arr['phone'] && !$arr['mobile'])return '电话和手机至少填写一个';
		if($arr['email'] && !c('email')->isemail($arr['email']))return '邮箱格式错误';
	}
	
	protected function saveafter($table, $arr, $id, $addbo){
		$name = $arr['name'];
		m('custfina')->update("`custname`='$name'", "`custid`='$id'");
		m('custract')->update("`custname`='$name'", "`custid`='$id'");
		m('custsale')->update("`custname`='$name'", "`custid`='$id'");
		m('custappy')->update("`custname`='$name'", "`custid`='$id'");
		m('goodm')->update("`custname`='$name'", "`custid`='$id' and `type` in(1,2)");//1采购,2销售
		m('crm')->custtotal($id);
        m('project')->update("`custname`='$name'", "`custid`='$id'");
        
        // 使用新的统一联系人处理逻辑
        $mobile = isset($arr['tel']) ? trim($arr['tel']) : '';
        $linkname = isset($arr['linkname']) ? trim($arr['linkname']) : '';
        
        if (!empty($mobile) && !empty($linkname)) {
            // 准备额外数据
            $extra_data = [];
            if (isset($arr['linkposition'])) $extra_data['position'] = trim($arr['linkposition']);
            if (isset($arr['linkemail'])) $extra_data['email'] = trim($arr['linkemail']);
            if (isset($arr['linkhonorific'])) $extra_data['honorific'] = trim($arr['linkhonorific']);
            
            // 调用统一的联系人处理逻辑（客户场景）
            $result = m('contacts')->handleContactLogic($mobile, $linkname, 'customer', $id, 0, $this, $extra_data);
            
            if (!$result['success']) {
                // 如果联系人处理失败，可以在这里添加日志或其他处理
            }
        }
	}
	
	// 删除前处理已移至 customerModel.php 的 flowdeletebillbefore 方法中统一处理
	
	/**
	 * 获取客户相关数据的Ajax方法
	 * 支持多个选项卡：销售机会、合同、收款单、付款单、售后单、销售单、客户计划、联系人
	 * @return string 返回格式化的HTML表格数据
	 */
	public function getothernrAjax()
	{
		$custid = (int)$this->get('custid','0');//客户id
		$ind  	= (int)$this->get('ind','0');//第几个选择卡
		$bh   	= 'custsale';//销售机会的
		$atype  = 'all'; //流程模块条件编号
		
		// 根据选项卡索引设置对应的模块和条件
		if($ind==2)$bh='custract';
		if($ind==3){
			$bh='custfina';
			$atype = 'allskd'; //所有收款单的
		}
		if($ind==4){
			$bh='custfinb';
			$atype = 'allfkd';//所有付款单！
		}
		if($ind==5){
			$bh='electwork';//售后单
		}
		if($ind==6){
			$bh='custxiao';//销售单
		}
		if($ind==7){
			$bh='custplan';
		}
		if($ind==8){
			$bh='contacts'; // 使用联系人流程模块
			$atype = 'all'; // 使用all条件，在flowbillwhere中处理客户关联
		}
		
		// 为指定的选项卡添加新增按钮
		$addButtonHtml = '';
		if(in_array($ind, [1, 2, 7, 8])) { // 销售机会、客户合同、跟进计划、联系人
			$addButtonHtml = $this->getAddButtonHtml($ind, $custid);
		}
		
		//读取数据
		$flow  = m('flow')->initflow($bh);//初始化模块
		//调用方法getrowstable是在webmain\model\flow\flow.php 里面的，
		//第一个参数，流程模块条件的编号，如果没有这个编号是读取不到数据
		//第二个参数，额外添加的条件，下面那说明的跟这个客户有关
		//第3个参数，默认读取的条数，默认是100
		
		// 联系人模块需要特殊处理，传递custid参数
		if($ind == 8) {
			// 设置custid参数供联系人流程模块使用
			$_GET['custid'] = $custid;
			$cont = $flow->getrowstable($atype, '');//联系人模块通过flowbillwhere处理查询条件
		} else {
			$cont = $flow->getrowstable($atype, 'and `custid`='.$custid.'');//读取表格数据
		}
		
		// 将新增按钮添加到内容前面
		return $addButtonHtml . $cont;
	}
	
	public function shatetoAjax()
	{
		$sna  = $this->post('sna');
		$sid  = c('check')->onlynumber($this->post('sid'));
		$khid = c('check')->onlynumber($this->post('khid'));
		
		m('customer')->update(array(
			'shate' 	=> $sna,
			'shateid' 	=> $sid,
		),"`id` in($khid)");
	}
	
	/**
	 * 生成新增按钮HTML
	 * @param int $ind 选项卡索引
	 * @param int $custid 客户ID
	 * @return string
	 */
	private function getAddButtonHtml($ind, $custid)
	{
		// 根据选项卡索引确定模块信息
		$moduleConfig = [
			1 => ['module' => 'custsale', 'name' => '销售机会'],
			2 => ['module' => 'custract', 'name' => '客户合同'], 
			7 => ['module' => 'custplan', 'name' => '跟进计划'],
			8 => ['module' => 'contacts', 'name' => '联系人']
		];
		
		if (!isset($moduleConfig[$ind])) {
			return '';
		}
		
		$config = $moduleConfig[$ind];
		$moduleNum = $config['module'];
		$moduleName = $config['name'];
		
		// 生成新增按钮HTML和JavaScript
		$html = <<<HTML
<div style="margin-bottom: 5px; padding: 5px; border-bottom: 1px solid #ddd;">
	<button type="button" onclick="addNew{$moduleNum}({$custid})" 
		style="padding: 5px 10px; border: 1px solid #1389d3; background: #1389d3; color: #fff; cursor: pointer; border-radius: 5px;">
		<i class="fa fa-plus"></i> +新增{$moduleName}
	</button>
</div>

<script type="text/javascript">
if (typeof addNew{$moduleNum} === 'undefined') {
	function addNew{$moduleNum}(custid) {
		// 使用系统原生的js.winiframe弹窗方法
		var url = '?a=lu&m=input&d=flow&num={$moduleNum}&mid=0&custid=' + custid;
			js.winiframe('新增{$moduleName}', url);
	}
}
</script>
HTML;

		return $html;
	}
	

	
	/**
	 * 删除联系人的Ajax方法
	 */
	public function deletecontactAjax()
	{
		$contactId = (int)$this->post('contact_id', 0);
		$custid = (int)$this->post('custid', 0);
		
		if ($contactId <= 0) {
			return array('success' => false, 'msg' => '无效的联系人ID');
		}
		
		try {
			// 删除联系人关联关系
			if ($custid > 0) {
				$relResult = m('custcontrel')->delete("contact_id = $contactId AND customer_id = $custid");
			}
			
			// 检查联系人是否还有其他关联关系
			$otherRels = m('custcontrel')->rows("contact_id = $contactId");
			
			// 如果没有其他关联关系，删除联系人记录
			if ($otherRels == 0) {
				$contactResult = m('contacts')->delete("id = $contactId");
			}
			
			return array('success' => true, 'msg' => '删除成功');
			
		} catch (Exception $e) {
			return array('success' => false, 'msg' => '删除失败：' . $e->getMessage());
		}
	}
}