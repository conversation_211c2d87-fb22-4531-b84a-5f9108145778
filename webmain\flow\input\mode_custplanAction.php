<?php
/**
*	此文件是流程模块【custplan.跟进计划】对应控制器接口文件。
*/ 
class mode_custplanClassAction extends inputAction{
	
	public function getmycust()
	{
		$rows = m('crm')->getmycust($this->adminid, $this->rock->arrvalue($this->rs, 'custid'));
		return $rows;
	}
	
	protected function saveafter($table, $arr, $id, $addbo){
		$status = arrvalue($arr,'status');
		$custid = arrvalue($arr,'custid','0');
		$findt = arrvalue($arr,'findt');
		if($status==1 && $custid>0 && $findt){
			m('customer')->update("`lastdt`='{$findt}'", $custid);
		}
	}

	/**
	 * 获取客户信息的Ajax方法
	 */
	public function getcustinfoAjax()
	{
		try {
			$custid = (int)$this->post('custid');
			if (!$custid) {
				$custid = (int)$this->get('custid');
			}
			
			if (!$custid) {
				return ['success' => false, 'msg' => '客户ID参数缺失'];
			}

			// 获取客户信息
			$customer = m('customer')->getone("id=$custid");
			if (!$customer) {
				return ['success' => false, 'msg' => '客户不存在'];
			}

			return [
				'success' => true, 
				'name' => $customer['name'],
				'id' => $customer['id'],
				'custname' => $customer['name']
			];

		} catch (Exception $e) {
			return ['success' => false, 'msg' => '系统错误：' . $e->getMessage()];
		}
	}
}	
			