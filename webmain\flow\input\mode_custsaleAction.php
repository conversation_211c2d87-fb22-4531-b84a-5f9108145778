<?php
/**
*	客户.销售机会
*/
class mode_custsaleClassAction extends inputAction{
	
	public function selectcust()
	{
		$rows = m('crm')->getmycust($this->adminid, $this->rock->arrvalue($this->rs, 'custid'));
		return $rows;
	}

	/**
	 * 获取客户信息的Ajax方法
	 */
	public function getcustinfoAjax()
	{
		try {
			$custid = (int)$this->post('custid');
			if (!$custid) {
				$custid = (int)$this->get('custid');
			}
			
			if (!$custid) {
				return ['success' => false, 'msg' => '客户ID参数缺失'];
			}

			// 获取客户信息
			$customer = m('customer')->getone("id=$custid");
			if (!$customer) {
				return ['success' => false, 'msg' => '客户不存在'];
			}

			return [
				'success' => true, 
				'name' => $customer['name'],
				'id' => $customer['id'],
				'custname' => $customer['name']
			];

		} catch (Exception $e) {
			return ['success' => false, 'msg' => '系统错误：' . $e->getMessage()];
		}
	}
}	
			