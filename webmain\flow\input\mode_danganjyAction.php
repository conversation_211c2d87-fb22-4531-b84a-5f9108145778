<?php
class mode_danganjyClassAction extends inputAction{
	

	protected function savebefore($table, $arr, $id, $addbo){
		$dnid = $arr['dnid'];
		$jydt 	= $arr['jydt'];
		if($id==0 && $jydt<$this->date)return '借阅日期不能是过去';
		$jydt  = $arr['jydt'];
		$where = '`id`<>'.$id.' and `dnid`='.$dnid.' and `jydt`<=\''.$jydt.'\' and `yjdt`>=\''.$jydt.'\' and `isgh`=0 and `status` in(0,1)';
		$oners = m($table)->getone($where);
		if($oners)return '此档案在'.$jydt.'已被“'.$oners['optname'].'”申请借阅了';
	}
	
		
	protected function saveafter($table, $arr, $id, $addbo){
		
	}
	
	public function getdangandata()
	{
		$where  = m('admin')->getcompanywhere(1);
		$rows 	= m('dangan')->getrows('`isjy`=1 '.$where.'','id,title,num');
		$arr 	= array();
		foreach($rows as $k=>$rs){
			$arr[] = array('value'=>$rs['id'],'name'=>$rs['title'],'subname'=>$rs['num']);
		}
		return $arr;
	}
}	
			