<?php
class mode_diaoboClassAction extends inputAction{
	
	protected function savebefore($table, $arr, $id, $addbo){
		$data = $this->getsubtabledata(0);
		if(count($data)==0)return '至少要有一行记录';
		foreach($data as $k=>$rs){
			if(isset($rs['aid']))foreach($data as $k1=>$rs1){
				if($k!=$k1){
					if($rs['aid']==$rs1['aid'])
						return '行'.($k1+1).'的物品已在行'.($k+1).'上填写，不要重复填写';
				}
			}
		}
		$rows = array();
		$rows['type'] = '3';
		return array(
			'rows' => $rows
		);
	}
	
	protected function saveafter($table, $arr, $id, $addbo){
		
	}
	
	/**
	 * 为custid下拉框提供仓库选项数据的Ajax方法
	 * 根据stamp参数返回不同的仓库数据
	 * stamp=0: 返回id=1的仓库（总仓库）
	 * stamp=1: 返回depotnum等于当前用户工号的仓库（个人仓库）
	 * stamp=2: 返回cgid包含当前用户ID的仓库（共享仓库）
	 */
	public function getCustidOptionsAjax()
	{
		$stamp = (int)$this->get('stamp', 0);
		$uid = $this->adminid;
		
		$rows = array();
		
		try {
			if($stamp === 0) {
				// stamp=0: 仅返回id=1的仓库
				$arows = m('godepot')->getall('`id`=1','id,depotname');
			} elseif($stamp === 1) {
				// stamp=1: 返回depotnum等于当前用户工号的仓库
				$userInfo = m('admin')->getone($uid, 'num');
				$userNum = $userInfo ? $userInfo['num'] : '';
				
				if($userNum) {
					$arows = m('godepot')->getall("`depotnum`='$userNum'",'id,depotname');
				} else {
					$arows = array();
				}
			} else {
				// stamp=2: 返回cgid包含当前用户ID的仓库
				$arows = m('godepot')->getall("FIND_IN_SET('$uid', `cgid`) > 0",'id,depotname');
			}
			
			// 转换为前端需要的格式
			foreach($arows as $rs) {
				$rows[] = array(
					'name' => $rs['depotname'],
					'value' => $rs['id']
				);
			}
			
		} catch(Exception $e) {
			// 出错时返回空数组
		}
		
		$this->returnjson($rows);
	}


	
	public function getgoodsdata()
	{
		return m('goods')->getgoodsdata(3);
	}
}
			