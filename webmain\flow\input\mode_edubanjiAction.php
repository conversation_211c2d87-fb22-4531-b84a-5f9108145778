<?php
/**
*	此文件是流程模块【edubanji.班级管理】对应控制器接口文件。
*/ 
class mode_edubanjiClassAction extends inputAction{
	
	
	protected function savebefore($table, $arr, $id, $addbo){
		$nianduan = $arr['nianduan'];
		$banji 	  = $arr['banji'];
		$xueqiid  = $arr['xueqiid'];
		$where = "`id`<>'$id' and `xueqiid`='$xueqiid' and `nianduan`='$nianduan' and `banji`='$banji'";
		if(m($table)->rows($where)>0)return '已经有这个班级了';
	}
	
	
	protected function saveafter($table, $arr, $id, $addbo){
		$xueqiid  = $arr['xueqiid'];
		m('edu')->updatexueqi($xueqiid);
	}
	
	
	//选择年段
	public function selnianduan()
	{
		$rows = $this->option->getdata('edunianduan');
		$arr  = array();
		foreach($rows as $k=>$rs){
			$rows1 = $this->option->getdata($rs['id']);
			foreach($rows1 as $k1=>$rs1){
				$arr[] = array('value'=>$rs1['name'],'name'=>$rs1['name'],'optgroup'=>$rs['name']);
			}
		}
		
		return $arr;
	}
	
	protected function storeafter($table, $rows)
	{
		$xueqiarr = array();
		$dbs = m('edu');
		if($this->loadci==1)$xueqiarr = $dbs->selxueqitree();
		
		return array(
			'xueqiarr' => $xueqiarr,
			'nowxueqiid'=> $dbs->xueqiid,
			'nianduanarr' => $this->selnianduan()
		);
	}
	
	//返回默认值
	public function inputfieldsval($fid, $rs)
	{
		if($fid=='xueqiid'){
			return m('edu')->getxueqiid();
		}
		return '';
	}
	
	
	public function copybanjiAjax()
	{
		$xueqiid = (int)$this->get('xueqiid','0');
		$xqrs 	 = m('eduxueqi')->getone($xueqiid);
		$type	 = $xqrs['type'];
		$nsrs 	 = false;
		$rows 	 = m('eduxueqi')->getall('`status`=1 and `eduid`='.$xqrs['eduid'].'','*','`startdt` desc');
		foreach($rows as $k=>$rs){
			if($rs['id']==$xueqiid){
				$nsrs = arrvalue($rows, $k+1);
				break;
			}
		}
		if(!$nsrs)return returnerror('没有上学期可复制');
		$ntype    = $nsrs['type'];
		$nxueqiid = $nsrs['id'];
		if($type=='1' && $ntype!='2')return returnerror('此学期为上学期无法复制对应学期');//+1
		if($type=='2' && $ntype!='1')return returnerror('此学期为下学期无法复制对应学期');
		
		//复制班级
		$bjdb 	= m('edubanji');
		$kcdb 	= m('edukecheng');
		$bjrows = $bjdb->getall('`xueqiid`='.$nxueqiid.' and `status`=1');
		$ndarr	= $this->selnianduan();
		foreach($bjrows as $k=>$rs){
			$nianduan = $rs['nianduan'];
			$banji 	  = $rs['banji'];
			$objid 	  = $rs['id'];
			
			//复制的下学期
			if($ntype=='2'){
				if($nianduan=='大班' || $nianduan=='六年级' || $nianduan=='初三' || $nianduan=='高三')continue;
				foreach($ndarr as $k1=>$rs1){
					if($rs1['name']==$nianduan){
						$nianduan = $ndarr[$k1+1]['name'];
						break;
					}
				}
			}
			$where = '`xueqiid`='.$xueqiid.' and `nianduan`=\''.$nianduan.'\' and `banji`=\''.$banji.'\'';
			$ors   =  $bjdb->getone($where);
			$bjid  = 0;
			
			if($ors){
				$bjid = $ors['id'];
				$bjdb->update(array(
					'lieshu' 	=> $rs['lieshu'],
					'zuoweistr' => $rs['zuoweistr'],
					'optdt' 	=> $this->rock->now
				), $bjid);
				
			}else{
				$nuarr = $rs;
				unset($nuarr['id']);
				$nuarr['nianduan'] = $nianduan;
				$nuarr['xueqiid']  = $xueqiid;
				$nuarr['optdt']    = $this->rock->now;
				$nuarr['uid']  	   = $this->adminid;
				$bjid = $bjdb->insert($nuarr);
				
				$this->db->insert('[Q]edusjoin','`type`,`mid`,`sid`,`hao`,`guanxi`', 'select 1,`mid`,'.$bjid.',`hao`,`guanxi` from `[Q]edusjoin` where `type`=1 and `sid`='.$objid.'', true);//复制学生
				
				$this->db->insert('[Q]edukemu','`name`,`laoshi`,`laoshiid`,`sort`,`banjiid`,`kemuid`,`oid`', 'select `name`,`laoshi`,`laoshiid`,`sort`,'.$bjid.',`kemuid`,`id` from `[Q]edukemu` where `banjiid`='.$objid.'', true);//复制安排老师
				
				$this->db->insert('[Q]edukecheng','`banjiid`,`gzid`,`week`,`kemuid`,`remark`', 'select '.$bjid.',`gzid`,`week`,`kemuid`,`remark` from `[Q]edukecheng` where `banjiid`='.$objid.'', true);//复制课程
				
				$orows  = m('edukemu')->getall('`banjiid`='.$bjid.'');
				foreach($orows as $k1=>$rs1){
					$kcdb->update('kemuid='.$rs1['id'].'', '`banjiid`='.$bjid.' and `kemuid`='.$rs1['oid'].'');
				}
			}
			
		}
		m('edu')->updatebjren();
		m('edu')->updatexueqi();
		
		return returnsuccess();
	}
}	
			