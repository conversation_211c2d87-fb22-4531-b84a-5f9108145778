<?php
/**
*	此文件是流程模块【edunotice.学校通知】对应控制器接口文件。
*/ 
class mode_edunoticeClassAction extends inputAction{
	
	
	protected function savebefore($table, $arr, $id, $addbo){
		
	}
	
	
	protected function saveafter($table, $arr, $id, $addbo){
		
	}
	
	//可读取内部通知内容
	public function gettongda()
	{
		$rows = m('flow')->initflow('gong')->getflowrows($this->adminid,'my', 10);
		$arr  = array();
		foreach($rows as $k=>$rs){
			$arr[] = array(
				'name' => $rs['title'],
				'id' => $rs['id'],
				'subname' => $rs['typename'],
			);
		}
		return $arr;
	}
	//获取详情
	public function gonginfoAjax()
	{
		$id = (int)$this->get('id');
		$rs = m('infor')->getone($id);
		
		return $rs;
	}
	
	//获取发送给数据源，只显示当前学期
	public function getreceid()
	{
		$arr = array();
		$arr[] = array(
			'name' => '所有班级',
			'value'=> ''
		);
		
		return $arr;
	}
}	
			