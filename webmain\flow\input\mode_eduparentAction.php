<?php
/**
*	此文件是流程模块【eduparent.家长管理】对应控制器接口文件。
*/ 
class mode_eduparentClassAction extends inputAction{
	

	protected function savebefore($table, $arr, $id, $addbo){
		
	}
	

	protected function saveafter($table, $arr, $id, $addbo){
		$mobile = $arr['mobile'];
		$name = $arr['name'];
		m('edustudent')->update("`lxname`='$name'", "`lxmobile`='$mobile'");
	}
	
	public function delguanxiAjax()
	{
		$did = (int)$this->get('did');
		m('edusjoin')->delete($did);
		
		return returnsuccess('ok');
	}
	
	public function saveguanxiAjax()
	{
		$mid = (int)$this->post('mid');
		$sid = (int)$this->post('sid');
		$guanxi = $this->post('guanxi');
		$this->joobj = m('edusjoin');
		$nwhere = '`type`=0 and `mid`='.$mid.' and `sid`='.$sid.'';
		if($this->joobj->rows($nwhere)==0)$nwhere='';
		$sarr = array(
			'type' => 0,
			'mid' => $mid,
			'sid' => $sid,
			'guanxi' => $guanxi,
		);
		$this->joobj->record($sarr, $nwhere);
		return returnsuccess('ok');
	}
	
	public function selstudentAjax()
	{
		return m('edu')->selstudent();
	}
}	
			