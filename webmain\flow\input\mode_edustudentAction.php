<?php
/**
*	此文件是流程模块【edustudent.学生管理】对应控制器接口文件。
*/ 
class mode_edustudentClassAction extends inputAction{
	
	
	protected function savebefore($table, $arr, $id, $addbo){
		$rows = array();
		if(isset($arr['idnum']))$rows['idnum'] = $this->jm->base64encode($arr['idnum']);// 身份证号加密
		return array(
			'rows'=>$rows
		);
	}
	
	
	protected function saveafter($table, $arr, $id, $addbo){
		
		$lxname = arrvalue($arr, 'lxname');
		$lxmobile = arrvalue($arr, 'lxmobile');
		$lxguanxi = arrvalue($arr, 'lxguanxi');
		$this->flow->savejiaz($lxname, $lxmobile, $lxguanxi, $id);
	}
	
	protected function storeafter($table, $rows)
	{
		$xueqiarr = array();
		if($this->loadci==1)$xueqiarr = m('edu')->selxueqitree();
		return array(
			'xueqiarr' => $xueqiarr,
		);
	}
	
	//选择年段
	public function selnianduan()
	{
		$rows = $this->option->getdata('edunianduan');
		$arr  = array();
		foreach($rows as $k=>$rs){
			$rows1 = $this->option->getdata($rs['id']);
			foreach($rows1 as $k1=>$rs1){
				$arr[] = array('value'=>$rs1['name'],'name'=>$rs1['name'],'optgroup'=>$rs['name']);
			}
		}
		
		return $arr;
	}
	
	
	
	//获取班级
	public function getbanjiAjax()
	{
		$pnum	 = $this->get('pnum'); //不是all就显示自己班级
		$where 	 = '';
		if($pnum=='wewewew'){
			//读取科目
			$kmrows	  = $this->db->getall('SELECT b.`name`,a.laoshi,a.laoshiid,a.`banjiid`,a.`id` FROM `[Q]edukemu` a left join `[Q]edukemu` b on a.kemuid=b.id where a.laoshiid='.$this->adminid.'');
			$bjid = '-1';
			foreach($kmrows as $k=>$rs){
				$bjid.=','.$rs['banjiid'].'';
			}
			$where='and `id` in('.$bjid.')';
		}
		$xueqiid = (int)$this->get('xueqiid','0');
		$rows 	 = array();
		$ndarr = $this->selnianduan();
		$bjarr = $this->db->getall("select * from `[Q]edubanji` where `xueqiid`='$xueqiid' $where order by `banji`");
		foreach($ndarr as $k1=>$rs1){
			$children = $this->nianarr($bjarr, $rs1['name']);
			if($children)$rows[] = array(
				'name' => $rs1['optgroup'].$rs1['name'],
				'id' => 0,
				'bjid' => $this->banjidss,
				'children' => $children
			);
		}
		return $rows;
	}
	private function nianarr($bjarr, $nianduan)
	{
		$rows = array();
		$ss1 = '';
		foreach($bjarr as $k=>$rs){
			if($nianduan==$rs['nianduan']){
				$rows[] = array(
					'name' => $rs['banji'],
					'bjid' => $rs['id'],
					'id' => 0
				);
				$ss1.=','.$rs['id'].'';
			}
		}
		if($ss1!='')$ss1 = substr($ss1, 1);
		$this->banjidss = $ss1;
		return $rows;
	}
	
	
	
	
	
	
	//选学生加入到班级里
	public function xuanxueAjax()
	{
		$rows 	 = array();
		$banjiid = (int)$this->get('banjiid','0');
		$bjrs 	 = m('edubanji')->getone($banjiid);
		$nianduan= $bjrs['nianduan'];
		$xueqiid = $bjrs['xueqiid'];
		$sqlw 	 = 'select `mid` from `[Q]edusjoin` where `type`=1 and `sid` in(select `id` from `[Q]edubanji` where `xueqiid`='.$xueqiid.')';
		//$sql 	 = 'select * from `[Q]edustudent` where `id` not in('.$sqlw.') and `state`=\''.$nianduan.'\'';
		$sqlw	 = 'select `mid` from `[Q]edusjoin` where `type`=1 and `sid`='.$banjiid.'';
		$sql 	 = 'select * from `[Q]edustudent` where `id` not in('.$sqlw.')';
		$xsrows  = $this->db->getall($sql);
		foreach($xsrows as $k=>$rs){
			$idnum = $this->rock->jm->base64decode($rs['idnum']);
			$idnum = substr($idnum,0,-4).'****';
			if(!isempt($rs['nianduan']))$idnum.=','.$rs['nianduan'].'';
			$rows[] = array(
				'value' => $rs['id'],
				'name' => $rs['name'],
				'subname' => $rs['sex'].','.$idnum,
			);
		}
		return $rows;
	}
	
	//保存学生分配班级
	public function saveapxueAjax()
	{
		$banjiid = (int)$this->post('banjiid','0');
		$xsid 	 = c('check')->onlynumber($this->post('xsid')); //学生id
		if(!$xsid || $banjiid==0)return returnerror('错误');
		//edusjoin
		$db 	 = m('edusjoin');
		$xsida 	 = explode(',', $xsid);
		foreach($xsida as $mid){
			$db->insert(array(
				'type' => 1,
				'mid' => $mid,
				'sid' => $banjiid,
			));
		}
		$rshu = $db->rows('`type`=1 and `sid`='.$banjiid.'');
		m('edu')->updatebjren($banjiid);
		return returnsuccess('成功');
	}
	//删除学生
	public function delapxueAjax()
	{
		$banjiid = (int)$this->post('banjiid','0');
		$xsid = (int)$this->post('xsid','0');
		m('edusjoin')->delete('`mid`='.$xsid.' and `type` =1 and `sid`='.$banjiid.'');
		m('edu')->updatebjren($banjiid);
	}
	//修改头衔
	public function edittouxianAjax()
	{
		$banjiid = (int)$this->post('banjiid','0');
		$xsid = (int)$this->post('xsid','0');
		$touxian = $this->post('touxian');
		m('edusjoin')->update(array(
			'guanxi' => $touxian
		),'`mid`='.$xsid.' and `type` =1 and `sid`='.$banjiid.'');
	}
	
	
	//刷新年段
	public function nianduanreloAjax()
	{
		$xueqiid = m('edu')->getxueqiid();
		$dbs = m('edustudent');
		$sql = 'SELECT a.`mid`,b.`nianduan`,b.`banji` FROM `[Q]edusjoin` a left join `[Q]edubanji` b on a.`sid`=b.`id` where a.`type`=1 and b.`xueqiid`='.$xueqiid.'';
		$dbs->update('`nianduan`=null','1=1');
		$rows = $this->db->getall($sql);
		foreach($rows as $k=>$rs){
			$str = $rs['nianduan'].'('.$rs['banji'].')';
			$dbs->update('`nianduan`=concat(ifnull(`nianduan`,\'\'),\''.$str.'\')',$rs['mid']);
		}
		
		return returnsuccess();
	}
	
	//保存班号
	public function savebanhaoAjax()
	{
		$banhao = $this->post('banhao');
		$xsid = (int)$this->post('xsid','0');
		m('edusjoin')->update(array(
			'hao' => $banhao
		), $xsid);
		
		return returnsuccess();
	}
}	
			