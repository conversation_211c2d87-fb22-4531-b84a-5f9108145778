<?php
/**
*	此文件是流程模块【electwork.电子服务单】对应控制器接口文件。
*/ 
class mode_electworkClassAction extends inputAction{
	/**
	*	重写函数：保存前处理，主要用于判断是否可以保存
	*	$table String 对应表名
	*	$arr Array 表单参数
	*	$id Int 对应表上记录Id 0添加时，大于0修改时
	*	$addbo Boolean 是否添加时
	*	return array('msg'=>'错误提示内容','rows'=> array()) 可返回空字符串，或者数组 rows 是可同时保存到数据库上数组
	*/
	protected function savebefore($table, $arr, $id, $addbo){
		$data = $this->getsubtabledata(0);
		// 允许子表无数据，无论status的值如何
		// if(count($data)==0)return '至少要有一行记录';
		
		// 获取当前用户对应的仓库ID
		$userInfo = m('admin')->getone($this->adminid, 'num');
		$userNum = $userInfo ? $userInfo['num'] : '';
		
		if(empty($userNum)) {
			return '当前用户没有工号，无法确定仓库权限';
		}
		
		$depotInfo = m('godepot')->getone("`depotnum`='$userNum'", 'id');
		if(!$depotInfo) {
			return '当前用户没有对应的仓库，无法使用物品';
		}
		
		$depotId = (int)$depotInfo['id'];
		
		$this->sssaid = '0';
		foreach($data as $k=>$rs){
			$this->sssaid.=','.$rs['aid'].'';
			
			// 检查重复物品
			if(isset($rs['aid']))foreach($data as $k1=>$rs1){
				if($k!=$k1){
					if($rs['aid']==$rs1['aid'])
						return '行'.($k1+1).'的商品已在行'.($k+1).'上填写，不要重复填写';
				}
			}
			

		}
		
		$rows['type'] = '6';//一定要是6，不能去掉
		$rows['workid'] = $arr['workid'];
		
		// 防止changeusercheck类型字段替换distid值
		// 当选择工单时，distid应该来自work表，而不是被dist字段的changeusercheck类型覆盖
		if(isset($arr['distid']) && !empty($arr['distid'])) {
			$rows['distid'] = $arr['distid'];
		}
		
		// 如果有关联工单，确保从工单获取正确的distid
		if(isset($arr['workid']) && $arr['workid'] > 0) {
			$workInfo = m('work')->getone($arr['workid'], 'distid');
			if($workInfo && !empty($workInfo['distid'])) {
				$rows['distid'] = $workInfo['distid'];
			}
		}
		return array(
			'rows'=>$rows
		);
	}
	//读取物品
	public function getgoodsdata()
	{
		// 获取当前用户信息
		$userInfo = m('admin')->getone($this->adminid, 'num');
		$userNum = $userInfo ? $userInfo['num'] : '';
		
		// 如果用户没有工号，返回空数组
		if(empty($userNum)) {
			return array();
		}
		
		// 根据用户工号查找对应的仓库
		$depotInfo = m('godepot')->getone("`depotnum`='$userNum'", 'id');
		if(!$depotInfo) {
			// 如果没有找到对应仓库，返回空数组
			return array();
		}
		
		$depotId = (int)$depotInfo['id'];
		
		// 获取该仓库中有库存的物品ID列表
		$stockRecords = $this->db->getall("
			SELECT `aid`, SUM(`count`) as total_count 
			FROM `[Q]goodss` 
			WHERE `depotid`='$depotId' AND `status`=1 
			GROUP BY `aid` 
			HAVING total_count > 0
		");
		
		if(empty($stockRecords)) {
			// 如果该仓库没有任何物品，返回空数组
			return array();
		}
		
		// 构建物品ID列表
		$aids = array();
		$stockMap = array();
		foreach($stockRecords as $record) {
			$aids[] = (int)$record['aid'];
			$stockMap[$record['aid']] = floatval($record['total_count']);
		}
		
		$aidList = implode(',', $aids);
		
		// 获取物品基本信息
		$where = m('admin')->getcompanywhere(1);
		$rows = $this->db->getall("
			SELECT a.`id`, a.`name`, a.`xinghao`, a.`num`, a.`guige`, a.`price`, a.`cost`, a.`unit`, b.`name` as `typename`
			FROM `[Q]goods` a 
			LEFT JOIN `[Q]option` b ON a.`typeid`=b.`id` 
			WHERE a.`id` IN ($aidList) $where
			ORDER BY a.`name`
		");
		
		$result = array();
		foreach($rows as $rs) {
			$name = $rs['name'];
			if(!isempt($rs['xinghao'])) {
				$name .= '(' . $rs['xinghao'] . ')';
			}
			
			$stock = isset($stockMap[$rs['id']]) ? $stockMap[$rs['id']] : 0;
			
			$item = array(
				'name' => $name,
				'value' => $rs['id'],
				'num' => $rs['num'],
				'price' => $rs['price'],
				'unit' => $rs['unit'],
				'cost' => $rs['cost'],
				'stock' => $stock,
				'subname' => $rs['num'] . ' ' . $rs['typename'] . ' (库存:' . $stock . ')',
			);
			
			// 如果库存为0或负数，设置为禁用状态
			if($stock <= 0) {
				$item['disabled'] = true;
				$item['subname'] .= ' 无库存';
			}
			
			$result[] = $item;
		}
		
		return $result;
	}
	//读取工单
	public function getmywork()
	{
		$arr[] = array('value'=>'0','name'=>'不关联');
		
		//读取我工单
		$workid = 0;
		$mid  = (int)$this->get('mid','0');
		if($mid>0){
			$workid = (int)$this->flow->getmou('workid', $mid);
		}
		$where 	= '`status` in(1) and `type`=0 and (`electid`=0 or `id`='.$workid.')';
		// 添加时间限制，只显示未过期的工单
		//$where .= " and `enddt`>='{$this->date}'";
		// 添加用户权限限制：非管理员只能看到自己负责(处理人)或自己创建的工单
		$adminInfo = m('admin')->getone($this->adminid, 'type');
		if($adminInfo['type'] != 1 && $adminInfo['type'] != 2) {
			$uid   = $this->adminid;
			// 判断当前登录用户是否为工单创建人(uid) 或 处理人(distid 字段等于或包含当前ID)
			$where .= " and ( `uid`='{$uid}' or `distid`='{$uid}' or FIND_IN_SET('{$uid}', distid) )";
		}
		// 添加工单关联检查：只显示未被关联的工单或当前编辑记录关联的工单
		if($workid > 0){
			$where .= " and (`electid`=0 or `id`=$workid)";
		}else{
			$where .= " and `electid`=0";
		}
		$rows 	= m('work')->getrows($where, 'id,title,`num`','`id` desc');

		foreach($rows as $k=>$rs){
			$arr[] = array(
				'value' => $rs['id'],
				'name' 	=> ''.$rs['title'].'.'.$rs['num'].''
			);
		}
		return $arr;
	}
	
	/**
	 * 根据status值读取工单数据的Ajax方法
	 * status=0时，读取grade=1的工单（保修期外）
	 * status=1时，读取grade=0的工单（保修期内）
	 */
	public function getMyWorkByStatusAjax()
	{
		try {
			$status = (int)$this->get('status', 0);
			$arr = array();
			$arr[] = array('value'=>'0','name'=>'不关联');
			
			// 根据status值确定grade值
			$grade = ($status == 0) ? 1 : 0;
		
		//读取工单，当前编辑记录关联的工单ID
		$workid = 0;
		$mid = (int)$this->get('mid','0');
		if($mid > 0){
			$workid = (int)$this->flow->getmou('workid', $mid);
		}
		
		// 构建查询条件 - 确保当前关联的工单始终包含在结果中
		$where = "`status` in(1) and `type`=0 and (`grade`='$grade'";
		if($workid > 0) {
			$where .= " or `id`=$workid";
		}
		$where .= ") and (`electid`=0 or `id`=$workid)";
		
		// 添加用户权限限制：非管理员只能看到自己负责(处理人)或自己创建的工单
		$adminInfo = m('admin')->getone($this->adminid, 'type');
		if($adminInfo['type'] != 1 && $adminInfo['type'] != 2) {
			$uid   = $this->adminid;
			$where .= " and ( `uid`='{$uid}' or `distid`='{$uid}' or FIND_IN_SET('{$uid}', distid) )";
		}
		
		// 获取工单数据
		$rows = m('work')->getrows($where, 'id,title,`num`,`grade`','`id` desc');

		foreach($rows as $k=>$rs){
			$gradeName = ($rs['grade'] == 0) ? '[保内]' : '[保外]';
			$displayName = $rs['title'].'.'.$rs['num'];
			// 如果当前工单的grade与期望的grade不匹配，添加标识
			if($rs['grade'] != $grade && $rs['id'] == $workid) {
				$displayName = $gradeName . $displayName;
			}
			
			$arr[] = array(
				'value' => $rs['id'],
				'name' => $displayName
			);
		}
		
			$this->returnjson($arr);
		} catch(Exception $e) {
			// 错误处理：返回空数组
			$this->returnjson(array(array('value'=>'0','name'=>'不关联')));
		}
	}
	public function ractchangeAjax()
	{
		try {
			$workid 	= (int)$this->get('ractid');
			$cars 	= m('work')->getone($workid, '`id`,`title`,`grade`,`dist`,`distid`,`explain`,`startdt`,`enddt`,`projectid`,`linkname`,`linktel`,`address`,`prname`,`imglod`,`imgnew`,`workgc`,`num`,`custid`');
		
		// 初始化workqy为空
		$cars['workqy'] = '';
		
		// 如果工单有关联项目，获取项目的workqy字段值
		if($cars && isset($cars['projectid']) && $cars['projectid'] > 0) {
			// 获取项目的workqy字段值
			$projectInfo = m('project')->getone($cars['projectid'], 'workqy');
			
			if($projectInfo && isset($projectInfo['workqy']) && !empty($projectInfo['workqy'])) {
				// workqy存储的是option表的num字段值（如"LG01"），需要查询option表获取对应的value值
				$workqyNum = $projectInfo['workqy'];
				$optionInfo = m('option')->getone("`num`='$workqyNum'", 'value');
				
				if($optionInfo && isset($optionInfo['value'])) {
					$cars['workqy'] = $optionInfo['value'];
				}
			}
		}
		
			$this->returnjson($cars);
		} catch(Exception $e) {
			// 错误处理：返回空对象
			$this->returnjson(array());
		}
	}
	/**
	*	重写函数：保存后处理，主要保存其他表数据
	*	$table String 对应表名
	*	$arr Array 表单参数
	*	$id Int 对应表上记录Id
	*	$addbo Boolean 是否添加时
	*/	
	protected function saveafter($table, $arr, $id, $addbo){
		$workid = $arr['workid'];
		if($workid>0)m('work')->update('`electid`='.$id.'', $workid);//更新工单对应电子单id
	}
}
			