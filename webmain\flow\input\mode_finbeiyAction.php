<?php
/**
*	此文件是流程模块【finbeiy.备用金使用登记】对应控制器接口文件。
*/ 
class mode_finbeiyClassAction extends inputAction{
	
	
	protected function savebefore($table, $arr, $id, $addbo){
		$rows['type'] = '8';//一定要是8，不能去掉
		$deptid = $arr['custid'];
		$beiyogn= $this->beitong($deptid, $id);
		$money	= floatval($arr['money']);
		if($money>floatval($beiyogn))return '申请使用超过可用金额'.$beiyogn.'元';
		
		return array(
			'rows'=>$rows
		);
	}
	
		
	protected function saveafter($table, $arr, $id, $addbo){
		
	}
	
	
	//统计剩余备用金
	private function beitong($deptid, $nid=0)
	{
		$dbs 	= m('fininfom');
		$zong 	= $dbs->getmou('sum(money)','`custid`='.$deptid.' and `type`=7 and `status`=1');
		if(!$zong)$zong = 0;
		$zong 	= floatval($zong);
		
		$zong1 	= $dbs->getmou('sum(money)','`custid`='.$deptid.' and `type`=8 and `status` in(0,1) and `id`<>'.$nid.'');
		if(!$zong1)$zong1 = 0;
		$zong1 	= floatval($zong1);
		
		$jin 	= $zong - $zong1;
		if($jin<0)$jin = 0;
		
		return $this->rock->number($jin);
	}
	
	public function getshengtotal()
	{
		$urs = m('admin')->getone($this->adminid);
		$zont= $this->beitong($urs['deptid']);
		return ''.$urs['deptname'].' 剩余可用备用金：'.$zont.'元';
	}
	
	public function chugeressAjax()
	{
		$deptid = (int)$this->get('deptid','0');
		$deptna = m('dept')->getmou('name', $deptid);
		$zont = $this->beitong($deptid);
		return ''.$deptna.' 剩余可用备用金：'.$zont.'元';
	}
	
	
	
	
	
	
	
	
	/**
	*	统计
	*/
	public function yisuantotalAjax()
	{
		$year = $this->get('year', date('Y'));
		$rows = array();
		$where= m('admin')->getcompanywhere(3);
		$sql  = 'select `custid`,MAX(`name`)as `name`,sum(money)as money from `[Q]fininfom` where `type`=7 and `status`=1 '.$where.' group by `custid`';
		$strw = $this->db->getall($sql);
		

		$sql  = 'select `custid`,sum(money)as money from `[Q]fininfom` where `type`=8 and `status` in(0,1)  group by `custid`';
		$stss = $this->db->getall($sql);
		$star = array();
		foreach($stss as $k1=>$rs1)$star[$rs1['custid']]=$rs1['money'];
		
		$zongz = 0;
		foreach($strw as $k=>$rs){
			$moneyfu	= floatval(arrvalue($star, $rs['custid'],'0'));
			if($moneyfu<0)$moneyfu = 0-$moneyfu;
			$rows[] = array(
				'money' => $rs['money'],
				'name' => $rs['name'],
				'moneyfu' => $this->rock->number($moneyfu),
				'moneysh' => $this->rock->number(floatval($rs['money'])-$moneyfu),
				'bili' => $this->rock->number(($moneyfu/floatval($rs['money']))*100).'%',
			);
			$zongz+=floatval($rs['money']);
		}
		if($zongz>0)$rows[] = array(
			'name' => '合计',
			'money' => $this->rock->number($zongz),
		);
		$bacarr = array(
			'success' => true,
			'totalCount' => count($rows),
			'downCount' => count($rows),
			'rows' => $rows,
			'year' => $year,
		);
		
		if($this->request('execldown')=='true')return $this->exceldown($bacarr);
		return $bacarr;
	}
}	
			