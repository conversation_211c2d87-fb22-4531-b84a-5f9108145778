<?php
/**
*	此文件是流程模块【finkai.开票申请】对应控制器接口文件。
*/ 
class mode_finkaiClassAction extends inputAction{
	
	
	protected function savebefore($table, $arr, $id, $addbo){
		$rows['type'] = '5';//一定要是5，不能去掉
		return array(
			'rows'=>$rows
		);
	}
	
		
	protected function saveafter($table, $arr, $id, $addbo){
		// 如果关联了电子服务单，更新其receipt字段为3（表示已开票）
		if(isset($arr['num']) && !empty($arr['num']) && (int)$arr['num'] > 0){
			$numValue = (int)$arr['num'];
			
					// 检查是否为电子服务单（type=6）
		$electInfo = $this->db->getone('[Q]goodm', '`id`='.$numValue.' AND `type`=6', 'id');
		if($electInfo){
			// 更新电子服务单的receipt字段为3
			m('goodm')->update(array('receipt' => '3'), $numValue);
		}
		}
	}
	
	public function selectcust()
	{
		// 检查当前用户是否是管理员
		$adminInfo = m('admin')->getone($this->adminid, 'type');
		$isAdmin = ($adminInfo['type'] == 1 || $adminInfo['type'] == 2); // 1和2通常是管理员类型
		
		$custid = $this->rock->arrvalue($this->rs, 'custid');
		$where = '';
		
		if($isAdmin) {
			// 管理员：可以看到所有客户，不限制uid
			if($custid > 0) {
				$where = "1=1"; // 显示所有客户，不限制
			} else {
				$where = "1=1"; // 显示所有客户，不限制
			}
		} else {
			// 非管理员：只显示自己的客户
			$where = "`uid`={$this->adminid}";
		}
		
		$rows = m('crm')->custdata($where);
		return $rows;
	}
	
	public function getotherAjax()
	{
		$id = (int)$this->get('id','0');
		$rs = m('customer')->getone($id, 'id,shibieid,openbank,cardid,kpdzdh,routeline');
		return $rs;
	}
	
	/**
	 * 获取可开票的数据源（合同、销售单、电子服务单）
	 * 参考custfina模块的hetongdata方法实现
	 */
	public function hetongdata()
	{
		$htid = 0;
		$mid  = (int)$this->get('mid','0');
		if($mid>0){
			// finkai模块中使用num字段存储关联的合同/销售单/电子服务单ID
			$numValue = $this->flow->getmou('num', $mid);
			if(!empty($numValue) && is_numeric($numValue) && (int)$numValue > 0){
				$htid = (int)$numValue;
			}
		}
		$rows = m('crm')->getmyract($this->adminid, $htid, 0);
		$arr  = array();
		$arr[] = array(
			'value' => '0',
			'name' 	=> '不选择',
		);
		
		// 获取合同数据
		foreach($rows as $k=>$rs){
			$arr[] = array(
				'value' => $rs['id'],
				'optgroup'=>'合同',
				'name' 	=> '['.$rs['num'].']'.$rs['custname'].' (￥'.number_format($rs['money'], 2).')',
			);
		}
		
		// 获取销售单数据
		$adminInfo = m('admin')->getone($this->adminid, 'type');
		$userWhere = '';
		if($adminInfo['type'] != 1 && $adminInfo['type'] != 2) { // 如果不是管理员，只显示自己的
			$userWhere = ' and g.`uid`='.$this->adminid;
		}
		
		$salesRows = $this->db->getall('SELECT g.`id`,g.`num`,g.`custid`,c.`name` as custname,g.`prname`,g.`money` FROM `[Q]goodm` g LEFT JOIN `[Q]customer` c ON g.`custid`=c.`id` WHERE g.`type`=2 and g.`status`=1 and g.`money`>0'.$userWhere.' ORDER BY g.`optdt` DESC');
		foreach($salesRows as $k=>$rs){
			$displayName = '['.$rs['num'].']'.$rs['custname'];
			if(!empty($rs['prname'])) {
				$displayName .= ' - '.$rs['prname'];
			}
			$displayName .= ' (￥'.number_format($rs['money'], 2).')';
			
			$arr[] = array(
				'value' => '-'.$rs['id'],
				'optgroup'=>'销售单',
				'name' 	=> $displayName,
			);
		}
		
		// 获取电子服务单数据 - value直接使用id，且只显示待开票(receipt=1)的
		$electrows = $this->db->getall('SELECT g.`id`,g.`num`,g.`prname`,g.`custid`,c.`name` as custname,g.`money` FROM `[Q]goodm` g LEFT JOIN `[Q]customer` c ON g.`custid`=c.`id` WHERE g.`type`=6 and g.`status`=1 and g.`money`>0 and g.`receipt`=1'.$userWhere.' ORDER BY g.`optdt` DESC');
		foreach($electrows as $k=>$rs){
			$displayName = '['.$rs['num'].']';
			if(!empty($rs['prname'])) {
				$displayName .= $rs['prname'];
			} else {
				$displayName .= $rs['custname'];
			}
			$displayName .= ' (￥'.number_format($rs['money'], 2).')';
			
			$arr[] = array(
				'value' => $rs['id'], // 直接使用id，不再使用-electwork_前缀
				'optgroup'=>'电子服务单',
				'name' 	=> $displayName,
			);
		}
		
		return $arr;
	}
	
	/**
	 * 根据选择的合同/销售单/电子服务单获取客户信息
	 * 主要用于传递给c.onselectdata['fullname']，然后由changegetother填充详细信息
	 */
	public function ractchangeAjax()
	{
		$ractid = $this->get('ractid');
		$cars = array();
		
		// 判断数据类型并获取客户信息
		if((int)$ractid < 0) {
			// 销售单（负数ID）
			$salesid = 0 - (int)$ractid;
			$xrsArr = $this->db->getall('SELECT g.`custid`,c.`name` as custname,c.`unitname` FROM `[Q]goodm` g LEFT JOIN `[Q]customer` c ON g.`custid`=c.`id` WHERE g.`id`='.$salesid.' and g.`type`=2');
			if($xrsArr && count($xrsArr) > 0){
				$xrs = $xrsArr[0];
				$cars['id'] = $xrs['custid']; // 客户ID
				$cars['name'] = $xrs['custname']; // 客户名称
				$cars['subname'] = $xrs['unitname'] ?: $xrs['custname']; // 优先使用unitname，为空则使用custname
			}
		} else if((int)$ractid > 0) {
			// 需要判断是合同还是电子服务单
			// 先尝试作为合同查询
			$htrs = m('custract')->getone((int)$ractid, 'id,custid,custname');
			if($htrs) {
				// 是合同，需要获取客户的unitname
				$custInfo = m('customer')->getone($htrs['custid'], 'id,name,unitname');
				if($custInfo) {
					$cars['id'] = $htrs['custid']; // 客户ID
					$cars['name'] = $custInfo['name']; // 客户名称
					$cars['subname'] = $custInfo['unitname'] ?: $custInfo['name']; // 优先使用unitname
				}
			} else {
				// 尝试作为电子服务单查询
				$xrsArr = $this->db->getall('SELECT g.`custid`,c.`name` as custname,c.`unitname` FROM `[Q]goodm` g LEFT JOIN `[Q]customer` c ON g.`custid`=c.`id` WHERE g.`id`='.(int)$ractid.' and g.`type`=6');
				if($xrsArr && count($xrsArr) > 0){
					$xrs = $xrsArr[0];
					$cars['id'] = $xrs['custid']; // 客户ID
					$cars['name'] = $xrs['custname']; // 客户名称
					$cars['subname'] = $xrs['unitname'] ?: $xrs['custname']; // 优先使用unitname，为空则使用custname
				}
			}
		}
		
		$this->returnjson($cars);
	}

	/**
	 * 获取公司单位下拉选择数据
	 * 用于销售方公司选择，支持层级显示
	 * @return array 公司选择数据数组，包含value和name字段
	 */
	public function companydata()
	{
		// 返回公司单位下拉数据，仅显示当前单位及子单位
		// 参数1表示不包含"最顶级"选项
		return m('company')->getselectdata(1);
	}

	/**
	 * 根据公司ID获取销售方详细信息
	 * 用于自动填充销售方的纳税人识别号、开户行等信息
	 * @return array 包含id,name,nameen,shibieid,openbank,cardid等字段
	 */
	public function companyinfoAjax()
	{
		$id = (int)$this->get('id', '0');
		
		if($id<=0){
			$this->returnjson(array());
			return;
		}
		
		// 获取公司详细信息，同时兼容字段大小写
		$rs = m('company')->getone($id,'id,name,nameen,shibieid,openbank,Openbank,cardid');
		
		if(!$rs) $rs = array();
		
		// 兼容openbank字段大小写问题
		if(!isset($rs['openbank']) && isset($rs['Openbank'])){
			$rs['openbank'] = $rs['Openbank'];
		}
		
		// 返回完整的公司信息，前端根据需要使用
		$this->returnjson($rs);
	}
}
			