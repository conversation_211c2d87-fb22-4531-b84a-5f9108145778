<?php
/**
*	此文件是流程模块【finkemu.会计科目】对应控制器接口文件。
*/ 
class mode_finkemuClassAction extends inputAction{
	
	
	protected function savebefore($table, $arr, $id, $addbo){
		$pid = $this->getshangji(m($table), $arr['num']);
		$rows['pid'] = $pid;
		return array(
			'rows' => $rows
		);
	}
	
	public function daorudevAjax()
	{
$str = '1001	库存现金	资产类
1002	银行存款	资产类
1012	其他货币资金	资产类
1101	短期投资	资产类
1101001	股票	资产类
1101002	债券	资产类
1101003	基金	资产类
1121	应收票据	资产类
1122	应收账款	资产类
1123	预付账款	资产类
1131	应收股利	资产类
1132	应收利息	资产类
1221	其他应收款	资产类
1401	材料采购	资产类
1402	在途物资	资产类
1403	原材料	资产类
1404	材料成本差异	资产类
1405	库存商品	资产类
1407	商品进销差价	资产类	贷
1408	委托加工物资	资产类
1411	周转材料	资产类
1421	消耗性生物资产	资产类
1501	长期债券投资	资产类
1511	长期股权投资	资产类
1601	固定资产	资产类
1602	累计折旧	资产类	贷
1604	在建工程	资产类
1605	工程物资	资产类
1606	固定资产清理	资产类
1621	生产性生物资产	资产类
1622	生产性生物资产累计折旧	资产类	贷
1701	无形资产	资产类
1702	累计摊销	资产类	贷
1801	长期待摊费用	资产类
1901	待处理财产损溢	资产类
2001	短期借款	负债类	贷
2201	应付票据	负债类	贷
2202	应付账款	负债类	贷
2203	预收账款	负债类	贷
2211	应付职工薪酬	负债类	贷
2221	应交税费	负债类	贷
2221001	应交增值税	负债类	贷
2221001001	进项税额	负债类
2221001002	已交税金	负债类
2221001003	转出未交增值税	负债类
2221001004	减免税款	负债类
2221001005	销项税额	负债类	贷
2221001006	出口退税	负债类	贷
2221001007	进项税额转出	负债类
2221001008	出口抵减内销产品应纳税额	负债类
2221001009	转出多交增值税	负债类	贷
2221001010	销项税额抵减	负债类
2221002	未交增值税	负债类	贷
2221003	应交消费税	负债类	贷
2221004	应交营业税	负债类	贷
2221005	应交资源税	负债类	贷
2221006	应交所得税	负债类	贷
2221007	应交土地增值税	负债类	贷
2221008	应交城市维护建设税	负债类	贷
2221009	应交房产税	负债类	贷
2221010	应交城镇土地使用税	负债类	贷
2221011	应交车船使用税	负债类	贷
2221012	应交个人所得税	负债类	贷
2221013	教育费附加	负债类	贷
2221014	地方教育费附加	负债类	贷
2221015	矿产资源补偿费	负债类	贷
2221016	排污费	负债类	贷
2221017	印花税	负债类	贷
2221018	预交增值税	负债类
2221019	待抵扣进项税额	负债类
2221020	待认证进项税额	负债类
2221021	待转销项税额	负债类	贷
2221022	增值税留抵税额	负债类
2221023	简易计税	负债类	贷
2221024	转让金融商品应交增值税	负债类	贷
2221025	代扣代交增值税	负债类	贷
2221026	应交增值税（小规模纳税人专用）	负债类	贷
2231	应付利息	负债类	贷
2232	应付利润	负债类	贷
2241	其他应付款	负债类	贷
2401	递延收益	负债类	贷
2501	长期借款	负债类	贷
2701	长期应付款	负债类	贷
3001	实收资本	权益类	贷
3002	资本公积	权益类	贷
3101	盈余公积	权益类	贷
3101001	法定盈余公积	权益类	贷
3101002	任意盈余公积	权益类	贷
3101003	法定公益金	权益类	贷
3103	本年利润	权益类	贷
3104	利润分配	权益类	贷
3104001	其他转入	权益类	贷
3104002	提取法定盈余公积	权益类	贷
3104003	提取法定公益金	权益类	贷
3104004	提取任意盈余公积	权益类	贷
3104005	应付利润	权益类	贷
3104006	未分配利润	权益类	贷
4001	生产成本	成本类
4101	制造费用	成本类
4301	研发支出	成本类
4401	工程施工	成本类
4403	机械作业	成本类
5001	主营业务收入	损益类	贷
5051	其他业务收入	损益类	贷
5111	投资收益	损益类	贷
5301	营业外收入	损益类	贷
5301001	非流动资产处置净收益	损益类	贷
5301002	政府补助	损益类	贷
5301003	捐赠收益	损益类	贷
5301004	盘盈收益	损益类	贷
5401	主营业务成本	损益类
5402	其他业务成本	损益类
5403	税金及附加	损益类
5601	销售费用	损益类
5601001	销售人员职工薪酬	损益类
5601002	业务招待费	损益类
5601003	修理费	损益类
5601004	办公费	损益类
5601005	水电费	损益类
5601006	差旅费	损益类
5601007	折旧费	损益类
5601008	摊销费	损益类
5601009	展览费	损益类
5601010	商品维修费	损益类
5601011	运输费	损益类
5601012	装卸费	损益类
5601013	包装费	损益类
5601014	保险费	损益类
5601015	广告费	损益类
5601016	业务宣传费	损益类
5602	管理费用	损益类
5602001	管理人员职工薪酬	损益类
5602002	业务招待费	损益类
5602003	修理费	损益类
5602004	办公费	损益类
5602005	水电费	损益类
5602006	差旅费	损益类
5602007	折旧费	损益类
5602008	摊销费	损益类
5602009	开办费	损益类
5602010	研究费用	损益类
5602011	咨询费	损益类
5602012	长期待摊费用摊销	损益类
5603	财务费用	损益类
5603001	利息费用	损益类
5603002	手续费	损益类
5603003	汇兑损益	损益类
5603004	现金折扣	损益类
5711	营业外支出	损益类
5711001	非流动资产处置净损失	损益类
5711002	赞助支出	损益类
5711003	捐赠支出	损益类
5711004	盘亏损失	损益类
5711005	坏账损失	损益类
5711006	存货毁损报废损失	损益类
5711007	无法收回的长期债券投资损失	损益类
5711008	无法收回的长期股权投资损失	损益类
5711009	自然灾害等不可抗力因素造成的损失	损益类
5711010	税收滞纳金	损益类
5711011	罚款支出	损益类
5801	所得税费用	损益类';
		$arr = explode("\n", $str);
		$db = m('finkemu');
		$sort = -1;
		$rows = $db->getall('1=1');
		$cbarr = array();
		foreach($rows as $k=>$rs)$cbarr[$rs['num']] = $rs['name'];
		foreach($arr as $str1){
			$sort++;
			$straa = explode('	', $str1);
			$num   = $straa[0];
			$name  = $straa[1];
			$flx   = 0;
			$type  = $this->gettypess($straa[2]);
			if(isset($straa[3]))$flx=1;
			$where  = "`num`='$num'";
			if(!isset($cbarr[$num]))$where='';
			$pid	= $this->getshangji($db, $num);
			$db->record(array(
				'num' => $num,
				'name' => $name,
				'type' => $type,
				'pid'  => $pid,
				'flx'  => $flx,
				'sort' => $sort+$type*99,
			), $where);
		}
	}
	private function gettypess($na)
	{
		$type = 0;
		if(contain($na, '负债'))$type=1;
		if(contain($na, '权益'))$type=2;
		if(contain($na, '成本'))$type=3;
		if(contain($na, '损益'))$type=4;
		return $type;
	}
	
	public function createnumsAjax()
	{
		$bh = $this->get('bh');
		return $this->db->sericnum(''.$bh.'','[Q]finkemu','num', 3).'';
	}
	
	//获取上级科目id
	public $kemuarrr = array();
	public function getshangji($db, $num)
	{
		$pid = 0;
		$len = strlen($num);
		$snum= '';
		if($len==7)$snum = substr($num,0,4);
		if($len==10)$snum = substr($num,0,7);
		if($snum){
			if(!isset($this->kemuarrr[$snum])){
				$rea = $db->getone("`num`='$snum'");
				if($rea)$pid = $rea['id'];
				$this->kemuarrr[$snum] = $pid;
			}else{
				$pid = $this->kemuarrr[$snum];
			}
		}
		return $pid;
	}
}	
			