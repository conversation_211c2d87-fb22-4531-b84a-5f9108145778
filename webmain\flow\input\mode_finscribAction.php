<?php
/**
*	此文件是流程模块【finscrib.凭证模板】对应控制器接口文件。
*/ 
class mode_finscribClassAction extends inputAction{
	
	/**
	*	重写函数：保存前处理，主要用于判断是否可以保存
	*	$table String 对应表名
	*	$arr Array 表单参数
	*	$id Int 对应表上记录Id 0添加时，大于0修改时
	*	$addbo Boolean 是否添加时
	*	return array('msg'=>'错误提示内容','rows'=> array()) 可返回空字符串，或者数组 rows 是可同时保存到数据库上数组
	*/
	protected function savebefore($table, $arr, $id, $addbo){
		
	}
	
	/**
	*	重写函数：保存后处理，主要保存其他表数据
	*	$table String 对应表名
	*	$arr Array 表单参数
	*	$id Int 对应表上记录Id
	*	$addbo Boolean 是否添加时
	*/	
	protected function saveafter($table, $arr, $id, $addbo){
		
	}
	
	public function mobanleixingdata()
	{
		$data = array();
		$rows = $this->option->getmnum('finmobantype');
		foreach($rows as $k=>$rs){
			$data[] = array(
				'name' => $rs['name'],
				'value' => $rs['name'],
			);
		}
		
		/*
		$rows = $this->option->getmnum('finjishoutype');
		foreach($rows as $k=>$rs){
			$data[] = array(
				'name' => '收-'.$rs['name'],
				'value' => $rs['name'],
			);
		}
		$rows = $this->option->getmnum('finjizhitype');
		foreach($rows as $k=>$rs){
			$data[] = array(
				'name' => '支-'.$rs['name'],
				'value' => $rs['name'],
			);
		}*/
		
		
		return $data;
	}
	
	public function addmorenAjax()
	{
		$db = m('finscrib');
		if($db->rows('1=1')>0)return returnerror('已经有记录，不能在导入');
		$str = '1	0	0	提现	日常支出	0
2	1	0	提现	1001 库存现金	1
3	1	1	提现	1002 银行存款	2
4	0	0	利息收入	日常支出	0
5	4	0	利息收入	1002 银行存款	2
6	4	1	利息收入	5603001 财务费用-利息费用	145
7	0	0	支付银行手续费	日常支出	0
8	7	0	支付银行手续费	5603002 财务费用-手续费	146
9	7	1	支付银行手续费	1002 银行存款	2
10	0	0	报销业务招待费	日常支出	0
11	10	0	报销业务招待费	5601002 销售费用-业务招待费	116
12	10	1	报销业务招待费	1002 银行存款	2
13	0	0	购入固定资产	采购	0
14	13	0	购入固定资产	1601 固定资产	25
15	13	1	购入固定资产	2202 应付账款	38
16	0	0	支付货款	采购	0
17	16	0	支付货款	2202 应付账款	38
18	16	1	支付货款	1002 银行存款	2
19	0	0	销售	销售	0
20	19	0	销售	1122 应收账款	9
21	19	1	销售	5001 主营业务收入	103
22	19	2	销售	2221001005 应交税费-应交增值税-销项税额	47
23	0	0	收货款	销售	0
24	23	0	收货款	1002 银行存款	2
25	23	1	收货款	1122 应收账款	9
26	0	0	工资	工资	0
27	26	0	支付工资	2211 应付职工薪酬	40
28	26	1	支付工资	1002 银行存款	2
29	0	0	缴纳未交增值税	税金	0
30	29	0	缴纳未交增值税	2221002 应交税费-未交增值税	53
31	29	1	缴纳未交增值税	1002 银行存款	2
32	0	0	计提税金	税金	0
33	32	0	计提税金	5403 税金及附加	113
34	32	1	计提税金	2221007 应交税费-应交土地增值税	58
35	32	2	计提税金	2221014 应交税费-地方教育费附加	65
36	32	3	计提税金	2221013 应交税费-教育费附加	64
37	0	0	计提所得税	税金	0
38	37	0	计提所得税	5801 所得税费用	161
39	37	1	计提所得税	2221005 应交税费-应交资源税	56
40	0	0	报销差旅费	日常支出	0
41	40	0	报销差旅费	5601006 销售费用-差旅费	120
42	40	1	报销差旅费	1002 银行存款	2';
		$arrr 	= explode("\n", $str);
		$comid 	= m('admin')->getcompanyid();
		foreach($arrr as $str1){
			$arr = explode('	', $str1);
			$uarr['id'] = $arr[0];
			$uarr['mid'] = $arr[1];
			$uarr['sort'] = $arr[2];
			$uarr['mingc'] = $arr[3];
			$uarr['leixing'] = $arr[4];
			$uarr['kmid'] = $arr[5];
			$uarr['comid'] = $comid;
			$uarr['uid'] = $this->adminid;
			$uarr['optid'] = $this->adminid;
			$uarr['optname'] = $this->adminname;
			$db->insert($uarr);
		}
		
		return returnsuccess();
	}
}	
			