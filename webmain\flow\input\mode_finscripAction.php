<?php
/**
*	此文件是流程模块【finscrip.凭证管理】对应控制器接口文件。
*/ 
class mode_finscripClassAction extends inputAction{
	
	/**
	*	重写函数：保存前处理，主要用于判断是否可以保存
	*	$table String 对应表名
	*	$arr Array 表单参数
	*	$id Int 对应表上记录Id 0添加时，大于0修改时
	*	$addbo Boolean 是否添加时
	*	return array('msg'=>'错误提示内容','rows'=> array()) 可返回空字符串，或者数组 rows 是可同时保存到数据库上数组
	*/
	protected function savebefore($table, $arr, $id, $addbo){
		$zhangid	= $arr['zhangid'];
		$type		= $arr['type'];
		$bh			= (int)$arr['bh'];
		$month		= substr($arr['dt'],0,7);
		$onrs 		= m($table)->getone('`id`<>'.$id.' and `zhangid`='.$zhangid.' and `bh`='.$bh.' and `type`='.$type.' and `dt` like \''.$month.'%\'');
		if($onrs)return '编号'.$bh.'已经存在';
		
		$jzid		= $arr['jzid'];
		if($jzid>0){
			$onrs 		= m($table)->getone('`id`<>'.$id.' and `jzid`='.$jzid.'');
			if($onrs)return '记账信息已生成过凭证了';
			$jzrs 		= m('finjibook')->getone($jzid);
			$money	    = floatval($jzrs['money']);
			if($money<0)$money = 0-$money;
			if($money != floatval($arr['moneyjie']))return '关联的记账金额'.$money.'跟当前不一样';
		}
		
	}
	
	/**
	*	重写函数：保存后处理，主要保存其他表数据
	*	$table String 对应表名
	*	$arr Array 表单参数
	*	$id Int 对应表上记录Id
	*	$addbo Boolean 是否添加时
	*/	
	protected function saveafter($table, $arr, $id, $addbo){
		$jzid		= $arr['jzid'];
		if($jzid>0)m('finjibook')->update('pzid='.$id.'', $jzid);
	}
	
	
	
	public function createbhAjax()
	{
		$dt 		= $this->get('dt');
		$type 		= (int)$this->get('type');
		$zhangid 	= (int)$this->get('zhangid');
		$zhrs 		= m('finzhang')->getone($zhangid);
		if(!$dt){
			$onrs 		= m('finscrip')->getone('`zhangid`='.$zhangid.' and `type`='.$type.' ','*','`dt` desc');
			if($onrs)$dt = $onrs['dt'];
		}
		
		if(!$dt)$dt = $zhrs['startdt'];
		$month 		= substr($dt,0,7);
		$onrs 		= m('finscrip')->getone('`zhangid`='.$zhangid.' and `type`='.$type.' and `dt` like \''.$month.'%\'','*','`bh` desc');
		$bh 		= 1;
		
		if($onrs){
			$bh = (int)$onrs['bh'] + 1;
		}
		
		return returnsuccess(array(
			'dt' => $dt,
			'bh' => $bh
		));
	}
	
	protected function storeafter($table, $rows)
	{
		$money = 0;
		$data  = array();
		if($rows){
			$data = $rows;
			$mon1 = $mon2 = 0;
			foreach($rows as $k=>$rs){
				$mon1+=floatval($rs['moneyjie']);
				$mon2+=floatval($rs['moneydai']);
			}
			$carr = array(
				'id'=> 0,
				'zhangid'	=> '合计',
				'checkdisabled' => true,
				'moneyjie' => $this->rock->number($mon1),
				'moneydai' => $this->rock->number($mon2),
			);
			$data[] = $carr;
		}
		if($this->loadci>1)return array(
			'rows' => $data
		);
		
		$zhangarr = m('fina')->getzhangtao(1);
		foreach($zhangarr as $k=>&$rs){
			$rs['name'].='('.$rs['subname'].')';
		}
		return array(
			'zhangarr' => $zhangarr,
			'rows' => $data
		);
	}
	
	public function printallAction()
	{
		$this->displayfile = 'webmain/flow/page/view_finscrip_3.html';
		$sid	= $this->get('sid');
		$sida   = explode(',', $sid);
		$this->assign('sida', $sida);
		$this->title = '打印凭证';
	}
	
	//摘要数据
	public function zhaiyaodata()
	{
		$data = array();
		$rows = $this->option->getmnum('finzhaiyao');
		foreach($rows as $k=>$rs){
			$data[] = array(
				'name' => $rs['name'],
				'value' => $rs['name'],
			);
		}
		return $data;
	}
	
	//读取凭证模板
	public function mobandata()
	{
		$data = array();
		$db   = m('finscrib');
		$rows = $db->getall('`mid`=0 and `status`=1','*','`id`');
		foreach($rows as $k=>$rs){
			$arows = $db->getall('`mid`='.$rs['id'].'','*','`sort`');
			$kemulist = '';
			$lista  = array();
			foreach($arows as $k1=>$rs1){
				$kemulist.='<br>['.$rs1['mingc'].']'.$rs1['leixing'].'';
				$lista[] = array(
					'zhaiyao' => $rs1['mingc'],
					'kemuid' => $rs1['kmid'],
					'kenuname' => $rs1['leixing'],
				);
			}
			
			$data[] = array(
				'value' => $rs['id'],
				'name'  => $rs['mingc'].'('.$rs['leixing'].')',
				'subname'  => $kemulist,
				'lista'  => $lista,
			);
		}
		return $data;
	}
	
	//读取记账记录信息
	public function jizhanginfoAjax()
	{
		$id 	= (int)$this->get('id','0');
		$data 	= array();
		$lxa 	= array('finjishoutype','finjizhitype');
		$rs 	= m('finjibook')->getone($id);
		if(!$rs)return returnerror('记账信息不存在');
		$pzid	= $rs['pzid'];
		$pzrs   = m('finscrip')->getone('`id`='.$pzid.' and `status`<>5');
		if($pzrs)return returnerror('此记账信息已生成过了凭证');
		
		$money 	 = floatval($rs['money']);
		if($money<0)$money = 0-$money;
		$money	 = $this->rock->number($money);
		
		$data['type'] 	 = $rs['type'];
		$data['money'] 	 = $money;
		$data['id'] 	 = $rs['id'];
		$data['jtype'] 	 = $rs['jtype'];
		$data['zhangid'] = $rs['zhangid'];
		$data['applydt'] = $rs['applydt'];
		$data['explain'] = $rs['explain'];
		$pid  	 = $this->option->getpids($lxa[$rs['type']]);
		
		$lista 	 = array();
		$frs 	 = m('finount')->getone($rs['accountid']);
		if(!$frs)return returnerror('此记账对应帐号信息不存在');
		if($frs['kemuid']=='0')return returnerror(''.$frs['name'].'未设置对应科目');
		$lxrs	 = $this->option->getone("`pid`='$pid' and `name`='".$rs['jtype']."'");
		if(!$lxrs || isempt($lxrs['values']))return returnerror('记账类型“'.$rs['jtype'].'”未设置对应科目');
		
		$zhaiyao = $rs['explain'];
		if(isempt($zhaiyao))$zhaiyao = $rs['jtype'];
		if(!isempt($rs['custname']))$zhaiyao.='-'.$rs['custname'].'';
		
		if($rs['type']=='0'){
			$lista[] = array(
				'zhaiyao' 	=> $zhaiyao,
				'kemuid'  	=> $frs['kemuid'],
				'kenuname'  => $frs['kemuname'],
				'moneysjie' => $money
			);
			$lista[] = array(
				'zhaiyao' 	=> $zhaiyao,
				'kemuid'  	=> $lxrs['values'],
				'kenuname'  => $lxrs['explain'],
				'moneysdai' => $money
			);
		}
		if($rs['type']=='1'){
			$lista[] = array(
				'zhaiyao' 	=> $zhaiyao,
				'kemuid'  	=> $lxrs['values'],
				'kenuname'  => $lxrs['explain'],
				'moneysjie' => $money
			);
			$lista[] = array(
				'zhaiyao' 	=> $zhaiyao,
				'kemuid'  	=> $frs['kemuid'],
				'kenuname'  => $frs['kemuname'],
				'moneysdai' => $money
			);
		}
		
		$data['lista']	 = $lista;
		return returnsuccess($data);
	}
}	
			