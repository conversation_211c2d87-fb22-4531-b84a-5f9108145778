<?php
/**
*	此文件是流程模块【finyisu.财务预算】对应控制器接口文件。
*/ 
class mode_finyisuClassAction extends inputAction{
	
	
	protected function savebefore($table, $arr, $id, $addbo){
		$rows['type'] = '6';//一定要是6，不能去掉
		return array(
			'rows'=>$rows
		);
	}
	
		
	protected function saveafter($table, $arr, $id, $addbo){
		
	}
	
	public function getyears()
	{
		$s = 2017;
		$m = date('Y')+3;
		$arr = array();
		for($i=$m;$i>=$s;$i--){
			$arr[] = array(
				'value' => $i,
				'name' => $i.'年',
			);
		}
		return $arr;
	}
	
	/**
	*	统计
	*/
	public function yisuantotalAjax()
	{
		$year = $this->get('year', date('Y'));
		$rows = array();
		$where= m('admin')->getcompanywhere(3);
		$sql  = 'select `custid`,MAX(`name`)as `name`,sum(money)as money from `[Q]fininfom` where `type`=6 and `status`=1 and `bills`='.$year.' '.$where.' group by `custid`';
		$strw = $this->db->getall($sql);
		
		$sql  = 'select sum(money) as money,`xgdeptid` from `[Q]finjibook` where `type`=1 and `status`=1 and `xgdeptid`>0 group by `xgdeptid`';
		$stss = $this->db->getall($sql);
		$star = array();
		foreach($stss as $k1=>$rs1)$star[$rs1['xgdeptid']]=$rs1['money'];
		
		$zongz = 0;
		foreach($strw as $k=>$rs){
			$moneyfu	= floatval(arrvalue($star, $rs['custid'],'0'));
			if($moneyfu<0)$moneyfu = 0-$moneyfu;
			$rows[] = array(
				'money' => $rs['money'],
				'name' => $rs['name'],
				'moneyfu' => $this->rock->number($moneyfu),
				'bili' => $this->rock->number(($moneyfu/floatval($rs['money']))*100).'%',
			);
			$zongz+=floatval($rs['money']);
		}
		if($zongz>0)$rows[] = array(
			'name' => '合计',
			'money' => $this->rock->number($zongz),
		);
		$bacarr = array(
			'success' => true,
			'totalCount' => count($rows),
			'downCount' => count($rows),
			'rows' => $rows,
			'year' => $year,
		);
		if($this->request('execldown')=='true')return $this->exceldown($bacarr);
		return $bacarr;
	}
}	
			