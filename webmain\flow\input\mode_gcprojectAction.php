<?php
/**
*	此文件是流程模块【gcproject.项目管理】对应控制器接口文件。
*/ 
class mode_gcprojectClassAction extends inputAction{
	
	/**
	*	重写函数：保存前处理，主要用于判断是否可以保存
	*	$table String 对应表名
	*	$arr Array 表单参数
	*	$id Int 对应表上记录Id 0添加时，大于0修改时
	*	$addbo Boolean 是否添加时
	*	return array('msg'=>'错误提示内容','rows'=> array()) 可返回空字符串，或者数组 rows 是可同时保存到数据库上数组
	*/
	protected function savebefore($table, $arr, $id, $addbo){
		$num = $arr['num'];
		$tbxmid = (int)$this->post('sstbxmid');
		if($tbxmid>0){
			$rs  	= m('gctbxm')->getone($tbxmid);
			if($rs && $rs['poid']>0)return '此招标项目已经立项过了';
		}
	}
	
	/**
	*	重写函数：保存后处理，主要保存其他表数据
	*	$table String 对应表名
	*	$arr Array 表单参数
	*	$id Int 对应表上记录Id
	*	$addbo Boolean 是否添加时
	*/	
	protected function saveafter($table, $arr, $id, $addbo){
		$name = $arr['gcname'];
		m('gcinfo')->update("`pgcname`='$name'", "`pgcid`='$id'");
		$num = $arr['num'];
		$rs  = m('gctbxm')->getone("`num`='$num'");
		if($rs){
			m('gctbxm')->update('`poid`='.$id.'', $rs['id']);
			m($table)->update('`tbid`='.$rs['id'].'', $id);
		}
	}
	
	public function proinfoAjax()
	{
		$sid = (int)$this->get('sid');
		$rs  = m('gcproject')->getone($sid);
		if(!$rs)return array();
		$num = $this->db->sericnum(''.$rs['num'].'-','[Q]gcproject','num',3);
		$data= array(
			'num' => $num,
			'address' => $rs['address'],
			'dengji' => $rs['dengji'],
			'jsname' => $rs['jsname'],
			'sgname' => $rs['sgname'],
			'jsid' => $rs['jsid'],
			'fzid' => $rs['fzid'],
			'fzname' => $rs['fzname'],
			'sgid' => $rs['sgid'],
			'leixing' => $rs['leixing'],
			'jhstartdt' => $rs['jhstartdt'],
			'jhenddt' => $rs['jhenddt'],
		);
		return $data;
	}
	
	public function tbxminfoAjax()
	{
		$tbid = (int)$this->get('tbid');
		$rs  = m('gctbxm')->getone($tbid);
		if(!$rs)return array();
		
		$data= array(
			'gcname' => $rs['gcname'],
			'num' => $rs['num'],
			'address' => $rs['address'],
			'fzid' => $rs['fzid'],
			'fzname' => $rs['fzname'],
			'leixing' => $rs['leixing'],
			'zaojia' => $rs['htfee'],
			'jsname' => $rs['jsname'],
			'jsid' => $rs['jsid'],
			'explain' => $rs['explain'],
			'state' => $rs['state'],
		);
		return $data;
	}
}	
			