<?php
/**
*	此文件是流程模块【gctbbzj.投标保证金】对应控制器接口文件。
*/ 
class mode_gctbbzjClassAction extends inputAction{
	
	protected function savebefore($table, $arr, $id, $addbo){
		
		$rows['type'] = '2';//固定是2不能去掉修改
		return array(
			'rows' => $rows
		);
	}
	
	/**
	*	重写函数：保存后处理，主要保存其他表数据
	*	$table String 对应表名
	*	$arr Array 表单参数
	*	$id Int 对应表上记录Id
	*	$addbo Boolean 是否添加时
	*/	
	protected function saveafter($table, $arr, $id, $addbo){
		
	}
}	
			