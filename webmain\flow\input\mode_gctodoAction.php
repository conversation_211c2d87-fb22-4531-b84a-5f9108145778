<?php
/**
*	此文件是流程模块【gcaqzg.项目动态】对应控制器接口文件。
*/ 
class mode_gctodoClassAction extends inputAction{
	
	
	protected function savebefore($table, $arr, $id, $addbo){
		
		$rows['djtype'] = '4';//固定是4
		return array(
			'rows' => $rows
		);
	}
	
	
	protected function saveafter($table, $arr, $id, $addbo){
		
		//替换word里的变量
		$fid 	= (int)arrvalue($arr,'qingkuan','0');
		m('word')->replaceWord($fid, $arr);
	}
}	
			