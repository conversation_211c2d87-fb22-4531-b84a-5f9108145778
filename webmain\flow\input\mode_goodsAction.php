<?php

class mode_goodsClassAction extends inputAction{
	public function getfilenumAjax()
	{
		$typeid	= (int)$this->post('type');
		$onrs	= $this->option->getone($typeid);
		$val 	= arrvalue($onrs, 'value');
		
		if(isempt($val)){
			$val= strtoupper(c('pingyin')->get(arrvalue($onrs, 'name'),2));//没有设置值用拼音
		}
		
		$num 	= ''.$val.'';
		return $this->db->sericnum($num,'[Q]goods','xinghao', 2);
	}
	
	public function getgoodstype()
	{
		$rows = m('goods')->getgoodstype();
		return $rows;
	}
	
	protected function savebefore($table, $arr, $id, $addbo){
		$bo = m('goods')->existsgoods($arr, $id);
		if($bo)return '物品已存在了';
	}
	
	
	public function reloadstockAjax()
	{
		m('goods')->setstock();
		return 'ok';
	}
}	
			