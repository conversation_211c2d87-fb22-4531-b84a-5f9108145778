<?php

class mode_jxcbaseClassAction extends inputAction{
	

	protected function savebefore($table, $arr, $id, $addbo){
		$bo = m('jxcbase')->existsgoods($arr, $id);
		if($bo)return '改商品已存在了';
	}
	
	public function reloadstockAjax()
	{
		m('jxcbase')->setstock();
	}
	
	protected function storeafter($table, $rows, $barr=array())
	{

		if($this->loadci>1)return array(
			'rows' => $rows
		);
		
		//多加字段。
		$fieldsarr 	= false;
		$depotarr	= m('jxcbase')->godepotarr();
		if(isset($barr['listinfo'])){
			$fieldsarr = $barr['listinfo']['fieldsarr'];
			foreach($depotarr as $k1=>$rs1){
				$fieldsarr[] = array(
					'fields'=>'stock_'.$rs1['value'].'',
					'name'=> $rs1['depotname'],
					'islb'=> 1,
				);
			}
		}
		
		$sarr = array();
		if($fieldsarr){
			$barr['listinfo']['fieldsarr'] = $fieldsarr;
			$sarr['listinfo'] = $barr['listinfo'];
		}
		
		return $sarr;
	}	
	
	public function optstockdataAjax()
	{
		$rows = array();
		$mid  = (int)$this->post('mid');
		$rows = $this->db->getall('select a.unit,a.count,b.name,b.guige,b.xinghao,b.num,a.aid,a.id,b.stock,(a.`count`-a.`couns`)maxcount from [Q]jxcgoodn a left join [Q]jxcbase b on a.aid=b.id where mid='.$mid.' and a.`count`<>a.`couns`');
		
		$barr['depotarr']	= m('jxcbase')->godepotarr();
		$barr['success'] = true;
		$barr['rows'] = $rows;
		
		return $barr;
	}
	
	//入库操作
	public function chukuoptAjax()
	{
		$dt 	= $this->post('dt');
		$type 	= (int)$this->post('type');
		$depotid= (int)$this->post('depotid');
		$kind 	= (int)$this->post('kind');
		$mid 	= (int)$this->post('mid','0');
		$sm 	= $this->post('sm');
		$cont 	= $this->post('cont');
		$sharr	= c('array')->strtoarray($cont);
		$arr['applydt'] = $dt;
		$arr['type'] 	= $type;
		$arr['kind'] 	= $kind;
		$arr['depotid'] = $depotid;
		$arr['explain'] = $sm;
		$arr['uid'] 	= $this->adminid;
		$arr['optid'] 	= $this->adminid;
		$arr['optdt'] 	= $this->now;
		$arr['comid'] 	= m('admin')->getcompanyid();
		$arr['optname'] = $this->adminname;
		$arr['status'] 	= 1;
		$arr['mid'] 	= $mid;
		$aid 			= '0';
		
		$ndbs			= m('jxcgoodn');
		$mtype 			= -1;
		
		//根据主表出入库操作
		$mrs 	= m('jxcgoodm')->getone("`id`='$mid' and `status`=1");
		if(!$mrs)return '该单据还未审核完成，不能出入库操作';
		
		
		$mtype = (int)$mrs['type']; //3就是调拨
		$arr['comid'] = $mrs['comid'];
		$aids = '';
		foreach($sharr as $k=>$rs){
			$ssid= $rs[0];
			$nrs = $ndbs->getone($ssid);
			if(!$nrs)continue;
			
			
			$sheng		= floatval($nrs['count']) - floatval($nrs['couns']);
			$aids .=','.$nrs['aid'].'';
			$arr['aid'] = $nrs['aid'];
			$count = (int)$rs[1];
			
			$arr['nid'] 	= $ssid;
			$arr['depotid'] = $depotid;
			$arr['type'] 	= $type;
			$arr['explain'] = $sm;
			$arr['price']   = $nrs['price'];
			$arr['unit']    = $nrs['unit'];
			
			if($count<0)$count = 0-$count;
			if($count==0 || $sheng==0 || $count>$sheng)continue;
			$arr['count'] = $count;
			if($type==1)$arr['count'] = 0- $arr['count'];//出库为负数
			
			$ndbs->update('`couns`=`couns`+'.$count.'', $ssid);
			
			$this->db->record('[Q]jxcgoods', $arr);
		}
		
		m('jxcbase')->upstatem($mid);
		if($aids)m('jxcbase')->setstock(substr($aids, 1));
		
		
		return 'success';
	}
}	
				