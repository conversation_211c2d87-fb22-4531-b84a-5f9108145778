<?php
/**
*	此文件是流程模块【jxccaigou.采购管理】对应控制器接口文件。
*/ 
class mode_jxccaigouClassAction extends inputAction{
	
	
	protected function savebefore($table, $arr, $id, $addbo){
		$htid = arrvalue($arr,'htid');
		$otherid = arrvalue($arr,'otherid');
		if($htid){
			$to = m('jxcgoodm')->rows('`id`<>'.$id.' and `htid`='.$htid.'');
			if($to>0)return '此采购合同已经创建过了采购单了';
		}
		if($otherid){
			$to = m('jxcgoodm')->rows('`id`<>'.$id.' and `otherid`='.$otherid.'');
			if($to>0)return '此采购计划已经创建过了采购单了';
		}
		
		$rows['dtype'] = 3; //必须为3
		$rows['type'] = 0; 
		$rows['kind'] = 1;
		
		return array(
			'rows' => $rows
		);
	}
	
		
	protected function saveafter($table, $arr, $id, $addbo){
		$htid = arrvalue($arr,'htid');
		if($htid){
			m('jxcgoodm')->update('`htid`='.$id.'', $htid);
		}
		$otherid = arrvalue($arr,'otherid');
		if($otherid){
			m('jxcgoodm')->update('`otherid`='.$id.',`state`=1', $otherid);
		}
	}
	
	public function jxcbasedata()
	{
		return m('jxcbase')->getjxcgoodsdata();
	}
	
	//读取采购合同
	public function caigouht()
	{
		$arr = array();
		$mid = (int)$this->get('mid');
		$whee = '`htid`=0';
		if($mid>0){
			$htid = arrvalue($this->rs, 'htid');
			if($htid)$whee='('.$whee.' or `id`='.$htid.')';
		}
		$rows = m('jxcgoodm')->getall('`uid`='.$this->adminid.' and `status`=1 and `dtype`=2 and '.$whee.'');
		foreach($rows as $k=>$rs){
			$arr[] = array(
				'value' => $rs['id'],
				'name' => $rs['custname'].'('.$rs['num'].')',
			);
		}
		return $arr;
	}
	
	//读取合同信息
	public function gethtinfoAjax()
	{
		$id = (int)$this->get('id','0');
		
		return m('jxcgoodm')->getone('`id`='.$id.' and `dtype`=2','`custid`,`custname`,`money`');
	}
	
	//读取采购计划
	public function getjhinfoAjax()
	{
		$id = (int)$this->get('id','0');
		$mrs= m('jxcgoodm')->getone('`id`='.$id.' and `dtype`=1','`custid`,`custname`,`money`,`id`,`explain`,`discount`,`num`');
		$zbrow = array();
		if($mrs){
			$zbrow = m('jxcgoodn')->getall('`mid`='.$id.'','*','`sort` asc');
			$zbrow = m('jxcbase')->flowsubdata($zbrow, 0);
		}
		return array(
			'mrs' => $mrs,
			'zbrow' => $zbrow,
		);
	}
}	
			