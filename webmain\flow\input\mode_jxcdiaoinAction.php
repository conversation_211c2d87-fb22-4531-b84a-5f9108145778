<?php
/**
*	此文件是流程模块【jxcdiaoin.商品调拨入库】对应控制器接口文件。
*/ 
class mode_jxcdiaoinClassAction extends inputAction{
	
	
	protected function savebefore($table, $arr, $id, $addbo){
		
		$rows['type'] = 0;
		$rows['kind'] = 2;
		$rows['dtype'] = 7; //必须为7
		
		if($addbo)return '不能直接新增';
		if($this->rs['htid']==$arr['depotid'])return '入库仓库不能选一样';
		
		return array(
			'rows' => $rows
		);
	}
	
		
	protected function saveafter($table, $arr, $id, $addbo){
		
	}
	
	
	
	public function jxcbasedata()
	{
		return array();
	}
	
	public function getdepotdata()
	{
		$htid = arrvalue($this->rs,'htid');
		return m('jxcbase')->godepotarr(" and `id`<>'$htid'");
	}
	
}	
			