<?php
/**
*	此文件是流程模块【jxcdiaoout.商品调拨出库】对应控制器接口文件。
*/ 
class mode_jxcdiaooutClassAction extends inputAction{
	
	
	protected function savebefore($table, $arr, $id, $addbo){
		
		$rows['type'] = 1;
		$rows['kind'] = 23;
		$rows['dtype'] = 6; //必须为6
		return array(
			'rows' => $rows
		);
	}
	
		
	protected function saveafter($table, $arr, $id, $addbo){
		
	}
	
	
	
	public function jxcbasedata()
	{
		return m('jxcbase')->getjxcgoodsdata(3);
	}
	
	public function getdepotdata()
	{
		
		return m('jxcbase')->godepotarr();
	}
	
}	
			