<?php

class mode_jxcpandianClassAction extends inputAction{
	

	public function kinddata()
	{
		$arr = $this->option->getdata('jxcstockin');
		$data= array();
		if($arr)foreach($arr as $k=>$rs)$data[] = array('name'=>$rs['name'],'value'=>$rs['value']);
		//if($data)$data[0]['optgroup'] = '入库';

		$arr = $this->option->getdata('jxcstockout');
		$data1= array();
		if($arr)foreach($arr as $k=>$rs)$data1[] = array('name'=>$rs['name'],'value'=>$rs['value']);
		//if($data1)$data1[0]['optgroup'] = '出库';
		
		return array_merge($data, $data1);
	}
	
	
	protected function storeafter($table, $rows)
	{
		$money = 0;
		if($rows){
			
		}
		if($this->loadci>1)return array(
			'rows' => $rows
		);
		return array(
			'depotarr' => m('jxcbase')->godepotarr(),
			'rows' => $rows
		);
	}
}	
			