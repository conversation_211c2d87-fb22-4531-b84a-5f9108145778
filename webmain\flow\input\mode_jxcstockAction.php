<?php
/**
*	此文件是流程模块【jxcstock.商品出入库】对应控制器接口文件。
*/ 
class mode_jxcstockClassAction extends inputAction{
	
	
	protected function savebefore($table, $arr, $id, $addbo){
		$kind = (int)$arr['kind'];
		$type = 0;
		if($kind>=20)$type = 1;
		
		$rows['type'] = $type;
		$rows['dtype'] = 0; //必须为0
		return array(
			'rows' => $rows
		);
	}
	
		
	protected function saveafter($table, $arr, $id, $addbo){
		
	}
	
	public function kinddata()
	{
		$arr = $this->option->getdata('jxcstockin');
		$data= array();
		if($arr)foreach($arr as $k=>$rs)$data[] = array('name'=>$rs['name'],'value'=>$rs['value']);
		if($data)$data[0]['optgroup'] = '入库';

		$arr = $this->option->getdata('jxcstockout');
		$data1= array();
		if($arr)foreach($arr as $k=>$rs)$data1[] = array('name'=>$rs['name'],'value'=>$rs['value']);
		if($data1)$data1[0]['optgroup'] = '出库';
		
		return array_merge($data, $data1);
	}
	
	public function jxcbasedata()
	{
		return m('jxcbase')->getjxcgoodsdata();
	}
	
	public function getdepotdata()
	{
		
		return m('jxcbase')->godepotarr();
	}
	
	public function depotdatadataAjax()
	{
		return $this->getdepotdata();
	}
	
	//操作
	public function optsotckAjax()
	{
		$id 	= (int)$this->post('id');
		$ckid 	= (int)$this->post('ckid');
		$sm 	= $this->post('sm');
		$mknum  = $this->post('mknum');
		return m('jxcbase')->optstock($mknum, $id,$ckid,$sm);
	}
}	
			