<?php
/**
*	此文件是流程模块【jxcxiaoshou.销售关联】对应控制器接口文件。
*/ 
class mode_jxcxiaoshouClassAction extends inputAction{
	
	
	protected function savebefore($table, $arr, $id, $addbo){
		$htid = arrvalue($arr,'htid');
		$otherid = arrvalue($arr,'otherid');
		if($htid){
			$to = m('jxcgoodm')->rows('`id`<>'.$id.' and `htid`='.$htid.'');
			if($to>0)return '此销售合同已经创建过了销售单了';
		}
		
		$rows['dtype'] = 5; //必须为3
		$rows['type'] = 1; 
		$rows['kind'] = 20;
		
		return array(
			'rows' => $rows
		);
	}
	
		
	protected function saveafter($table, $arr, $id, $addbo){
		$htid = arrvalue($arr,'htid');
		if($htid){
			m('jxcgoodm')->update('`htid`='.$id.'', $htid);
		}
		
	}
	
	//读取我的客户
	public function getmycust()
	{
		$rows = m('crm')->getmycust($this->adminid, $this->rock->arrvalue($this->rs, 'custid'));
		return $rows;
	}
	
	public function jxcbasedata()
	{
		return m('jxcbase')->getjxcgoodsdata(1);
	}
	
	//读取销售合同
	public function xiaoshouht()
	{
		$arr = array();
		$mid = (int)$this->get('mid');
		$whee = '';
		if($mid>0){
			$htid = arrvalue($this->rs, 'htid');
			if($htid)$whee='and `id`='.$htid.'';
		}else{
			$whee = 'and `htid`=0';
		}
		$rows = m('jxcgoodm')->getall('`uid`='.$this->adminid.' and `status`=1 and `dtype`=4 '.$whee.'');
		foreach($rows as $k=>$rs){
			$arr[] = array(
				'value' => $rs['id'],
				'name' => $rs['custname'].'('.$rs['num'].')',
			);
		}
		return $arr;
	}
	

	
	//读取采购计划
	public function gethtinfoAjax()
	{
		$id = (int)$this->get('id','0');
		$mrs= m('jxcgoodm')->getone('`id`='.$id.' and `dtype`=4','`custid`,`custname`,`money`,`id`,`explain`,`discount`,`num`');
		$zbrow = array();
		if($mrs){
			$zbrow = m('jxcgoodn')->getall('`mid`='.$id.'','*','`sort` asc');
			$zbrow = m('jxcbase')->flowsubdata($zbrow, 0);
		}
		return array(
			'mrs' => $mrs,
			'zbrow' => $zbrow,
		);
	}
	
	//销售统计
	public function totaldataAjax()
	{
		$rows = array();
		$fields = '`applydt`';
		$tields = 'sum(a.`money`)';
		$str12	= '';
		$where  = '';
		$tongtype 	= (int)$this->post('tongtype');
		$loadci 	= (int)$this->post('loadci');
		$depotid 	= (int)$this->post('depotid');
		$dt1 	= $this->post('dt1');
		$atype 	= $this->post('atype');
		$dt2 	= $this->post('dt2');
		if(!$dt1)$dt1 = date('Y-m-01');
		if(!$dt2)$dt2 = date('Y-m-d');
		if($tongtype==1){
			$fields = 'left(a.`applydt`,7)';
		}
		if($tongtype==3)$fields = 'a.`uid`';
		if($tongtype==4)$fields = 'left(a.`applydt`,4)';
		if($tongtype==2){
			$fields = 'b.`deptid`';
			$str12  = 'left join `[Q]admin` b on a.`uid`=b.`id`';
		}
		
		if($tongtype==5){
			$fields = 'b.`aid`';
			$tields= 'sum(b.`price` * b.`count`)';
			$str12  = 'right join `[Q]jxcgoodn` b on a.`id`=b.`mid`';
		}
		if($tongtype==6){
			$fields = 'b.`aid`';
			$tields= 'count(1)';
			$str12  = 'right join `[Q]jxcgoodn` b on a.`id`=b.`mid`';
		}

		if($depotid>0)$where='and a.`depotid`='.$depotid.'';
		if($dt1)$where.=" and a.`applydt`>='$dt1'";
		if($dt2)$where.=" and a.`applydt`<='$dt2'";
		$dtype = 5;
		if($atype=='caigou')$dtype = 3;
		
		$rows = $this->db->getall('select '.$fields.' as name,'.$tields.' as `value` from `[Q]jxcgoodm` a '.$str12.' where a.`dtype`='.$dtype.' '.$where.' group by '.$fields.'');
		if($tongtype==3 || $tongtype==2 || $tongtype==5 || $tongtype==6){ 
			if($tongtype==3)$ddb = m('admin');
			if($tongtype==2)$ddb = m('dept');
			if($tongtype==5 || $tongtype==6)$ddb = m('jxcbase');
			if($rows)foreach($rows as $k=>$rs){
				$ors = $ddb->getXinxi($rs['name']);
				$unit = '元';
				if($ors){
					$str = $ors['name'];
					if($tongtype==5 || $tongtype==6){
						$str1 = '';
						if(!isempt($ors['xinghao']))$str1.=''.$ors['xinghao'].'';
						if(!isempt($ors['guige']))$str1.=' '.$ors['guige'].'';
						if($str1)$str.='('.$str1.')';
						if($tongtype==6)$unit = $ors['unit'];
					}
					$rows[$k]['name'] = $str;
				}
				$rows[$k]['unit'] = $unit;
			}
		}
		
		$depotarr  = false;
		if($loadci==1)$depotarr = m('jxcbase')->godepotarr();
		return array(
			'rows' => $rows,
			'dt1' => $dt1,
			'dt2' => $dt2,
			'depotarr' => $depotarr,
		);
	}
	
	
	//统计数量
	public function reshusAjax()
	{
		$db1 = m('godepot');
		$rows = $db1->getall('1=1');
		foreach($rows as $k=>$rs){
			$spshu = 0;
			$sql = 'SELECT `aid`,sum(`count`) as stttso FROM `[Q]jxcgoods` where `depotid`='.$rs['id'].' GROUP BY aid';
			$ros1 = $this->db->getall($sql);
			foreach($ros1 as $k1=>$rs1)if($rs1['stttso']>0)$spshu++;
			$db1->update('`spshu`='.$spshu.'', $rs['id']);
		}
	}
}	
			