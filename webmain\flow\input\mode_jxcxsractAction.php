<?php
/**
*	此文件是流程模块【jxcxsract.销售合同】对应控制器接口文件。
*/ 
class mode_jxcxsractClassAction extends inputAction{
	
	
	protected function savebefore($table, $arr, $id, $addbo){

		$rows['dtype'] = 4; //必须为4
		return array(
			'rows' => $rows
		);
	}
	
		
	protected function saveafter($table, $arr, $id, $addbo){
		
	}
	
	//读取我的客户
	public function getmycust()
	{
		$rows = m('crm')->getmycust($this->adminid, $this->rock->arrvalue($this->rs, 'custid'));
		return $rows;
	}
	
	public function jxcbasedata()
	{
		return m('jxcbase')->getjxcgoodsdata(1);
	}
}	
			