<?php
/**
*	此文件是流程模块【officia.发文单】对应接口文件。
*	可在页面上创建更多方法如：public funciton testactAjax()，用js.getajaxurl('testact','mode_officia|input','flow')调用到对应方法
*/ 
class mode_officiaClassAction extends inputAction{
	
	
	protected function savebefore($table, $arr, $id, $addbo){
		
	}
	
		
	protected function saveafter($table, $arr, $id, $addbo){
		
	}
	
	public function getfilenumAjax()
	{
		$type 	= $this->post('type');
		$num 	= ''.$type.'〔Year〕';
		return $this->db->sericnum($num,'[Q]official','num', 1).'号';
	}
	
	//读取主送单位
	public function getofficiaunit()
	{
		$selvalue = (int)$this->get('selvalue','0');
		$selectdata[] = array(
			'name' => '选设置单位',
			'value' => '0',
		);
		$selectdata[] = array(
			'name' => '选内部部门',
			'value' => '1',
		);
		$rows = array();
		if($selvalue==0)$rows = $this->option->getdata('officiaunit');
		if($selvalue==1){
			$cdata= m('dept')->getdata(false);
			foreach($cdata as $k=>$rs){
				$rows[] = array(
					'name' => $rs['name'],
					'value' => $rs['id'],
					'padding' => 24*($rs['level']-1)
				);
			}
		}
		
		
		
		$barr['rows'] = $rows;
		$barr['selectdata'] = $selectdata;
		
		return $barr;
	}
	
	
	//创建文档
	public function createwordAjax()
	{
		$tit = $this->post('tit');
		$filepath = ''.UPDIR.'/'.date('Y-m').'/'.date('d').'_officia'.rand(1000,9999).'.docx';
		$this->rock->createtxt($filepath, base64_decode($this->creatework('docx')));
		$uarr = array(
			'filename' => ''.$tit.'.docx',
			'fileext' => 'docx',
			'filetype' => 'application/msword',
			'filepath' => $filepath,
			'filesize' => filesize($filepath),
			'filesizecn' => $this->rock->formatsize(filesize($filepath)),
			'optid' 	=> $this->adminid,
			'optname' 	=> $this->adminname,
			'adddt' 	=> $this->rock->now,
			'ip' 		=> $this->rock->ip,
			'web' 		=> $this->rock->web,
		);
		$uarr['id'] = m('file')->insert($uarr);
		return $uarr;
	}
	
	public function getcompanydataAjax()
	{
		$companid = m('admin')->getcompanyid();
		$rows = m('company')->getall('`id`<>'.$companid.'','id,name');
		return returnsuccess($rows);
	}
	
	public function sendcompanydataAjax()
	{
		$gwid = (int)$this->post('gwid','0');
		$xuanzhe 	= $this->post('xuanzhe');
		$sm 		= $this->post('sm');
		//saas模式
		
		
		
		return returnsuccess('发送成功');
	}
	
	private function creatework($lx){
		$workarr = array(
			'docx' => '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',
		);
		return $workarr[$lx];
	}
}	
			