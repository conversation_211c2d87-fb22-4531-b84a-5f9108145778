<?php
/**
*	此文件是流程模块【officidu.督查督办】对应控制器接口文件。
*/ 
class mode_officiduClassAction extends inputAction{
	
	/**
	*	重写函数：保存前处理，主要用于判断是否可以保存
	*	$table String 对应表名
	*	$arr Array 表单参数
	*	$id Int 对应表上记录Id 0添加时，大于0修改时
	*	$addbo Boolean 是否添加时
	*	return array('msg'=>'错误提示内容','rows'=> array()) 可返回空字符串，或者数组 rows 是可同时保存到数据库上数组
	*/
	protected function savebefore($table, $arr, $id, $addbo){
		
	}
	
	/**
	*	重写函数：保存后处理，主要保存其他表数据
	*	$table String 对应表名
	*	$arr Array 表单参数
	*	$id Int 对应表上记录Id
	*	$addbo Boolean 是否添加时
	*/	
	protected function saveafter($table, $arr, $id, $addbo){
		
	}
	
	//读取主送单位
	public function getofficiaunit()
	{
		$selvalue = (int)$this->get('selvalue','0');
		$selectdata[] = array(
			'name' => '选设置单位',
			'value' => '0',
		);
		$selectdata[] = array(
			'name' => '选内部部门',
			'value' => '1',
		);
		$rows = array();
		if($selvalue==0)$rows = $this->option->getdata('officiaunit');
		if($selvalue==1){
			$cdata= m('dept')->getdata(false);
			foreach($cdata as $k=>$rs){
				$rows[] = array(
					'name' => $rs['name'],
					'value' => $rs['id'],
					'padding' => 24*($rs['level']-1)
				);
			}
		}
		
		
		
		$barr['rows'] = $rows;
		$barr['selectdata'] = $selectdata;
		
		return $barr;
	}
	
	public function tongjiDataAjax()
	{
		$columns = $rows = array();
		$dtobj 		= c('date');
		$tjtype		= $this->post('tjtype','dt');
		$startdt	= $this->post('startdt');
		$enddt		= $this->post('enddt');
		$where 		= '';
		if(!isempt($startdt))$where.=" and `startdt`>='".$startdt." 00:00:00'";
		if(!isempt($enddt))$where.=" and `startdt`<='".$enddt." 23:59:59'";
		$rows = $sdar = array();
		$arrs = m('officidu')->getall('`isturn`=1 and `status` in(0,1,3) '.$where.'');
		
		foreach($arrs as $k=>$rs){
			$rs['dt'] 	  = substr($rs['startdt'],0,10);
			$rs['yuefen'] = substr($rs['startdt'],0,7);
			$val  = $rs[$tjtype];
			if(!isset($sdar[$val]))$sdar[$val] = array(0,0);
			if($rs['status']==1){
				$sdar[$val][0]++;
			}else{
				$sdar[$val][1]++;
			}
		}
		
		$sarr= m('flow:officidu')->jibielist();
		$saea= array(0,0,0);
		$ks1 = 0;
		foreach($sdar as $k=>$v){
			$ks1++;
			if($tjtype=='jibie'){
				foreach($sarr as $k1=>$vs1){
					if($vs1['value']==$k)$k='<font color="'.$vs1['color'].'">'.$vs1['name'].'</font>';
				}
			}
			$saea[0]+=$v[0];
			$saea[1]+=$v[1];
			$saea[2]+=$v[0]+$v[1];
			$rows[] = array(
				'name' => $k,
				'wctotal' => $v[0],
				'watotal' => $v[1],
				'zztotal' => $v[0]+$v[1],
			);
		}
		if($ks1>1)$rows[] = array(
			'name' => '合计',
			'wctotal' => $saea[0],
			'watotal' => $saea[1],
			'zztotal' => $saea[2],
		);
		
		return array(
			'rows' => $rows,
			'startdt' => $startdt,
			'enddt' => $enddt,
			'totalCount' => count($rows),
			'downCount' => count($rows),
		);
	}
}	
			