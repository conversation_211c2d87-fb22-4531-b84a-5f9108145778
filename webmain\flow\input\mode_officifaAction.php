<?php
/**
*	此文件是流程模块【officifa.公文分发传阅】对应控制器接口文件。
*/ 
class mode_officifaClassAction extends inputAction{
	
	
	protected function savebefore($table, $arr, $id, $addbo){
		
	}
	
	
	protected function saveafter($table, $arr, $id, $addbo){
		
		$gwid = $arr['gwid'];
		$receid 	= $arr['receid'];
		$recename 	= $arr['recename'];
		$gwrs 		= m('official')->getone($gwid);
		if($gwrs){
			if(!isempt($gwrs['receid'])){
				$receid.=','.$gwrs['receid'].'';
				$recename.=','.$gwrs['recename'].'';
			}
		}
		
		m('official')->update(array(
			'ffid' 		=> $id,
			'ffdt' 		=> $arr['applydt'],
			'receid' 	=> $receid,
			'recename' 	=> $recename,
			'startdt' 	=> $arr['startdt'],
			'enddt' 	=> $arr['enddt'],
		), $gwid);
	}
	
	//获取要分发的公文，发文需要套红和盖章才可以分发,可以多次分发，1个月内公文
	public function getgongwen()
	{
		$sysmid = (int)$this->get('sysmid','0');
		$gwid 	= 0;
		if($sysmid>0){
			$gwid 	= (int)m('officialfa')->getmou('gwid', $sysmid);//and ()
		}
		$where  = m('admin')->getcompanywhere(1);
		$dt	  	= c('date')->adddate($this->rock->date,'d', -30);
		$ushe	= '`uid`='.$this->adminid.'';
		if(m('zheng')->isguan($this->adminid)){
			$ushe = '1=1';
		}
		$rows = m('official')->getall("$ushe $where and `status`=1 and ((`thid`<>0 and (`yzid`>0 or `yzid`=-1) and `type`=0) or (`type`=1)) and (`applydt`>='$dt' or `ffid`=0 or `id`='$gwid')");
		$barr = array();
		foreach($rows as $k=>$rs){
			$barr[] = array(
				'name' => $rs['title'],
				'subname' => $rs['num'],
				'num' => $rs['num'],
				'unitsame' => $rs['unitsame'],
				'value' => $rs['id'],
			);
		}
		return $barr;
	}
	
	public function getgwinfoAjax()
	{
		$gwid = (int)$this->get('gwid','0');
		$rs = m('official')->getone("`id`='$gwid'",'id,num,title,unitsame');
		
		return $rs;
	}
	
}	
			