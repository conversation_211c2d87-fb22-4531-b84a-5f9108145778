<?php
class mode_projectClassAction extends inputAction{
    
    // 临时存储联系人ID，用于在saveafter中建立关联关系
    private $temp_contact_id;

    protected function savebefore($table, $arr, $id, $addbo){
        // 不在这里处理联系人逻辑，改到saveafter中处理
        return '';
    }
    
    protected function saveafter($table, $arr, $id, $addbo){
        // 使用新的统一联系人处理逻辑
        $mobile = isset($arr['linktel']) ? trim($arr['linktel']) : '';
        $linkname = isset($arr['linkname']) ? trim($arr['linkname']) : '';
        $custid = isset($arr['custid']) ? (int)$arr['custid'] : 0;
        
        if (!empty($mobile) && !empty($linkname)) {
            // 准备额外数据
            $extra_data = [];
            if (isset($arr['linkposition'])) $extra_data['position'] = trim($arr['linkposition']);
            if (isset($arr['linkemail'])) $extra_data['email'] = trim($arr['linkemail']);
            if (isset($arr['linkhonorific'])) $extra_data['honorific'] = trim($arr['linkhonorific']);
            
            // 调用统一的联系人处理逻辑（项目场景）
            $result = m('contacts')->handleContactLogic($mobile, $linkname, 'project', $custid, $id, $this, $extra_data);
            
            if (!$result['success']) {
                // 如果联系人处理失败，可以在这里添加日志或其他处理
            }
        }
    }
    
    // 删除前处理已移至 projectModel.php 的 flowdeletebillbefore 方法中统一处理
	
	public function progressdata()
	{
		$arr = array();
		for($i=0;$i<=100;$i++)$arr[]=array('value'=>$i,'name'=>$i.'%');
		return $arr;
	}
    //计算保修到期日
    public function getenddtAjax(){
        $date = $this->get('date');//日期
        $month = $this->get('month');//月份
        if(!$date){
            $newDate = "";
        }else{
            $newDate = date("Y-m-d",strtotime("+ {$month} month",strtotime($date)));
        }
        return ["enddt"=>$newDate];
    }
    //获取所有客户数据，参考mode_workAction.php中的projectdata()方法
    public function customerdata()
    {
        // 获取所有状态正常的客户数据，按更新时间倒序排列，unitname单独显示为12px字体
        $rows = m('customer')->getall('`status`=1','`id`,`name`,`unitname`','`optdt` desc');
        $arr = array();
        if($rows){
            foreach($rows as $k=>$rs){
                $arr[] = array(
                    'name' => $rs['name'],
                    'value' => $rs['id'],
                    'subname' => $rs['unitname'] // unitname作为subname单独返回，前端可控制12px字体显示
                );
            }
        } else {
            // 如果没有客户数据，返回提示信息
            $arr[] = array('value'=>0,'name'=>'没有可选择的客户');
        }
        return $arr;
    }
    
    /**
     * 获取项目对应客户下的联系人数据
     */
    public function getselectdata()
    {
        $custid = (int)$this->get('custid', 0); // 从GET参数获取客户ID
        $act = $this->get('act', ''); // 获取操作类型
        
        // 如果操作类型是获取联系人姓名数据
        if($act == 'contactsNameData'){
            // 获取该客户下的联系人
            $contacts = m('contacts')->getCustomerContacts($custid);
            $arr = array();
            
            if(is_array($contacts)){
                foreach($contacts as $contact){
                    $arr[] = array(
                        'id' => $contact['id'],
                        'value' => $contact['id'],
                        'name' => $contact['given_name'] . ($contact['mobile'] ? ' (' . $contact['mobile'] . ')' : ''),
                        'given_name' => $contact['given_name'],
                        'mobile' => isset($contact['mobile']) ? $contact['mobile'] : '',
                        'honorific' => isset($contact['honorific']) ? $contact['honorific'] : '',
                        'position' => isset($contact['position']) ? $contact['position'] : '',
                        'is_main' => isset($contact['is_main']) ? $contact['is_main'] : 0,
                        'subname' => (isset($contact['mobile']) && !empty($contact['mobile'])) ? '手机:'.$contact['mobile'] : ''
                    );
                }
            }
            
            return array('rows' => $arr);
        }
        
        return array('rows' => array());
    }
    
    /**
     * 获取联系人姓名数据（兼容旧版本调用）
     */
    public function contactsNameData($where = ''){
        // 优先从GET参数获取custid
        $custid = (int)$this->get('custid', 0);
        
        // 如果GET参数中没有custid，尝试从where参数解析
        if($custid == 0 && !empty($where) && is_numeric($where)){
            $custid = (int)$where;
        }
        
        // 获取该客户下的联系人
        $contacts = m('contacts')->getCustomerContacts($custid);
        $result = array();
        
        if($contacts && is_array($contacts)){
            foreach($contacts as $contact){
                $result[] = array(
                    'id' => $contact['id'],
                    'value' => $contact['id'],
                    'name' => $contact['given_name'] . ($contact['mobile'] ? ' (' . $contact['mobile'] . ')' : ''),
                    'given_name' => $contact['given_name'],
                    'mobile' => isset($contact['mobile']) ? $contact['mobile'] : '',
                    'honorific' => isset($contact['honorific']) ? $contact['honorific'] : '',
                    'position' => isset($contact['position']) ? $contact['position'] : '',
                    'is_main' => isset($contact['is_main']) ? $contact['is_main'] : 0,
                    'subname' => (isset($contact['mobile']) && !empty($contact['mobile'])) ? '手机:'.$contact['mobile'] : ''
                );
            }
        }
        
        return $result;
    }
    
    //获取关联项目信息，可参考客户模块或人员档案模块
   /* public function gethetongAjax()
    {
        $guid = (int)$this->get('id','0');
        $ind  = (int)$this->get('ind','0');
        $bh   = 'userract';
        $zd   = 'uid';
        if($ind==4){
            $bh   = 'reward';
            $zd   = 'objectid';
        }
        if($ind==5){
            $bh   = 'hrpositive';
        }
        if($ind==6){
            $bh   = 'hrredund';
        }
        if($ind==7){
            $bh   = 'hrtrsalary';
        }
        if($ind==8){
            $bh   = 'hrtransfer';
            $zd   = 'tranuid';
        }
        $flow = m('flow')->initflow($bh);
        $cont = $flow->getrowstable('all','and`'.$zd.'`='.$id.'');
        return $cont;
    }*/
}
			