<?php
 
class mode_workClassAction extends inputAction{
	
	
	protected function savebefore($table, $arr, $id, $addbo){
		$distid = arrvalue($arr,'distid');
		$dist   = arrvalue($arr,'dist');
		$this->oharr = array();
		if(!isempt($distid)){
			if(contain($distid,'u') || contain($distid,'d') || contain($distid,'g'))return '分配给人员必须使用多选或者单选';
			$jscj 	= (int)$this->post('temp_cjall','0');
			if($jscj==1 && contain($distid,',')){
				$distida = explode(',', $distid);
				$dista   = explode(',', $dist);
				if(count($distida)>1){
					foreach($distida as $k1=>$v1){
						if($k1>0){
							$this->oharr[] = array(
								'distid' => $distida[$k1],
								'dist' 	 => $dista[$k1],
							);
						}
					}
					
					$rows = array();
					$rows['distid'] = $distida[0];
					$rows['dist'] 	= $dista[0];
					return array(
						'rows' => $rows
					);
				}
			}
		}
	}
	
	
	protected function saveafter($table, $arr, $id, $addbo){
		if($this->oharr){
			$sysisturn		= (int)$this->post('istrun','1');
			$subna = '';
			if($sysisturn==0)$subna='保存';
			foreach($this->oharr as $k1=>$rs1){
				$arr['distid'] 	= $rs1['distid'];
				$arr['dist'] 	= $rs1['dist'];
				$arr['zhuid'] 	= $id;
				unset($arr['id']);
				$nid = m($table)->insert($arr);
				
				$this->flow->loaddata($nid, false);
				$this->flow->submit($subna);
			}
		}
        
        // 使用新的统一联系人处理逻辑（工单任务场景）
        $mobile = isset($arr['linktel']) ? trim($arr['linktel']) : '';
        $linkname = isset($arr['linkname']) ? trim($arr['linkname']) : '';
        $custid = isset($arr['custid']) ? (int)$arr['custid'] : 0;
        $projectid = isset($arr['projectid']) ? (int)$arr['projectid'] : 0;
        
        if (!empty($mobile) && !empty($linkname)) {
            // 准备额外数据
            $extra_data = [];
            if (isset($arr['linkposition'])) $extra_data['position'] = trim($arr['linkposition']);
            if (isset($arr['linkemail'])) $extra_data['email'] = trim($arr['linkemail']);
            if (isset($arr['linkhonorific'])) $extra_data['honorific'] = trim($arr['linkhonorific']);
            
            // 调用统一的联系人处理逻辑（工单任务场景）
            $result = m('contacts')->handleContactLogic($mobile, $linkname, 'work', $custid, $projectid, $this, $extra_data);
            
            if (!$result['success']) {
                // 如果联系人处理失败，可以在这里添加日志或其他处理
            }
        }
	}
	
	public function projectdata()
	{
		$rows 	= m('project')->getall('id>0 and status in(0,1,2,3)','`id`,`title`','optdt desc');
		$arr	= array();
		foreach($rows as $k=>$rs){
			$arr[] = array(
				'name' => $rs['title'],
				'value' => $rs['id']
			);
		}
		return $arr;
	}
    //获取项目信息
    public function getprojectoneAjax(){
        $pid = (int)$this->get('pid');//项目Id
        if(empty($pid)){
            return [];
        }
        $rs  	= m('project')->getone('`id`='.$pid.'', '`id`,`title`,`custid`,`custname`,`linkname`,`linktel`,`address`,`addresslatlng`,`startdt`,`enddt`,`status`,`content`');

        //获取是否保修标记
        $rs['grade_flag'] = 1;
        if($rs['enddt']){
            $enddt = strtotime($rs['enddt']);
            if($enddt > time()){
                $rs['grade_flag'] = 0;
            }else{
                $rs['grade_flag'] = 1;
            }
        }
    
        return $rs;
    }
    /**
     * 获取项目联系人信息（通过关联表）
     * 修复：使用关联表查询而不是直接使用mid字段
     */
    public function getprolinkAjax(){
        $pid = (int)$this->get('pid');  //项目ID
        if(empty($pid)){
            return [];
        }
        
        // 通过关联表获取项目联系人
        $contacts = m('contacts')->getProjectContacts($pid);
        $arr = array();
        
        if(is_array($contacts)){
            foreach($contacts as $contact){
                $arr[] = array(
                    'id' => $contact['id'],
                    'name' => $contact['given_name'],
                    'Phone' => $contact['mobile'] ? $contact['mobile'] : '',
                    'subname' => $contact['honorific'] ? $contact['honorific'] : '',
                    'position' => isset($contact['position']) ? $contact['position'] : '',
                    'is_main' => isset($contact['is_main']) ? $contact['is_main'] : 0
                );
            }
        }
        
        return $arr;
    }
    
    /**
     * 获取客户联系人信息（通过关联表）
     */
    public function getCustomerContactsFromRelAjax(){
        $custid = (int)$this->get('custid');  //客户ID
        if(empty($custid)){
            return [];
        }
        
        // 通过关联表获取客户联系人
        $contacts = m('contacts')->getCustomerContacts($custid);
        $arr = array();
        
        if(is_array($contacts)){
            foreach($contacts as $contact){
                $arr[] = array(
                    'id' => $contact['id'],
                    'name' => $contact['given_name'],
                    'Phone' => $contact['mobile'] ? $contact['mobile'] : '',
                    'subname' => $contact['honorific'] ? $contact['honorific'] : '',
                    'position' => isset($contact['position']) ? $contact['position'] : '',
                    'is_main' => isset($contact['is_main']) ? $contact['is_main'] : 0
                );
            }
        }
        
        return $arr;
    }
     
    /**
     * 获取联系人选择数据（通过关联表）
     * 统一的联系人数据获取接口
     */
    public function getselectdata(){
        $custid = (int)$this->get('custid', 0);
        $projectid = (int)$this->get('projectid', 0);
        $act = $this->get('act', '');
        
        // 如果操作类型是获取联系人姓名数据
        if($act == 'contactsNameData'){
            // 工单任务：只获取项目下的联系人
            $contacts = [];
            
            if($projectid > 0) {
                $contacts = m('contacts')->getProjectContacts($projectid);
            }
            
            $arr = array();
            if(is_array($contacts)){
                foreach($contacts as $contact){
                    $arr[] = array(
                        'id' => $contact['id'],
                        'value' => $contact['id'],
                        'name' => $contact['given_name'] . ($contact['mobile'] ? ' (' . $contact['mobile'] . ')' : ''),
                        'given_name' => $contact['given_name'],
                        'mobile' => isset($contact['mobile']) ? $contact['mobile'] : '',
                        'honorific' => isset($contact['honorific']) ? $contact['honorific'] : '',
                        'position' => isset($contact['position']) ? $contact['position'] : '',
                        'is_main' => isset($contact['is_main']) ? $contact['is_main'] : 0,
                        'subname' => (isset($contact['mobile']) && !empty($contact['mobile'])) ? '手机:'.$contact['mobile'] : ''
                    );
                }
            }
            
            return array('rows' => $arr);
        }
        
        return array('rows' => array());
    }
 
    /**
     * 获取任务类型数据
     */
    public function getworktypeAjax(){
        $type = (int)$this->get('type');
        $op = m('option')->getone("`num`='worktype'");
        if(!$op){
            return array();
        }
        $rs = m('option')->getone("`pid`='".$op['id']."' and `value`=".$type, "name,value");
        return $rs ? $rs : array();
    }
    

}
			