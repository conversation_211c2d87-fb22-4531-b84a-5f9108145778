<?php
/**
*	此文件是流程模块【wyfang.房屋管理】对应控制器接口文件。
*/ 
class mode_wyfangClassAction extends inputAction{
	
	protected function storeafter($table, $rows)
	{
		
		$barr  = array();
		if($this->loadci==1){
			$barr = m('wuye')->getxqfangtree();
		}
		return array(
			'xqarr' => $barr
		);
	}
	
	
	protected function savebefore($table, $arr, $id, $addbo){
		$where = "`xqid`='".$arr['xqid']."'";
		$where .= " and `louid`='".$arr['louid']."'";
		$where .= " and `ceng`='".$arr['ceng']."'";
		$where .= " and `name`='".$arr['name']."'";
		$where .= " and `id`<>$id";
		if(m($table)->rows($where)>0)return '此门牌号已经存在';
	}
}	
			