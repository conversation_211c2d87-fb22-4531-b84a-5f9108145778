<?php
/**
*	此文件是流程模块【wyfee.收费账单】对应控制器接口文件。
*/ 
class mode_wyfeeClassAction extends inputAction{
	
	/**
	*	重写函数：保存前处理，主要用于判断是否可以保存
	*	$table String 对应表名
	*	$arr Array 表单参数
	*	$id Int 对应表上记录Id 0添加时，大于0修改时
	*	$addbo Boolean 是否添加时
	*	return array('msg'=>'错误提示内容','rows'=> array()) 可返回空字符串，或者数组 rows 是可同时保存到数据库上数组
	*/
	protected function savebefore($table, $arr, $id, $addbo){
		$fangid = $arr['fangid'];
		$fangrs = m('wyfang')->getone($fangid);
		if($fangrs){
			$rows['xqid'] = $fangrs['xqid'];
			$rows['louid'] = $fangrs['louid'];
		}
		return array(
			'rows' => $rows
		);
	}
	
	/**
	*	重写函数：保存后处理，主要保存其他表数据
	*	$table String 对应表名
	*	$arr Array 表单参数
	*	$id Int 对应表上记录Id
	*	$addbo Boolean 是否添加时
	*/	
	protected function saveafter($table, $arr, $id, $addbo){
		
	}
	
	
	//这个就是数据
	protected function storeafter($table, $rows)
	{
		
		$barr  = array();
		if($this->loadci==1){
			$barr = m('wuye')->getxqfangtree();
		}
		//返回这个
		return array(
			'xqarr' => $barr
		);
	}
	
	public function wuyefeeitemdata()
	{
		$fangid = (int)$this->rock->get('fangid','0');
		$where  = '';
		if($fangid>0){
			$frs = m('wyfang')->getone($fangid);
			$where = 'and `xqid`='.$frs['xqid'].' and `louid` in(0,'.$frs['louid'].') and `fangid` in(0,'.$fangid.')';
		}
		
		$rows   = m('wyitem')->getall('`status`=1 '.$where.'','id as value,`name`,price as subname');
		
		return $rows;
	}
}	
			