<?php
/**
*	此文件是流程模块【wuyefuwu.业主服务】对应控制器接口文件。
*/ 
class mode_wyfuwuClassAction extends inputAction{
	
	
	protected function savebefore($table, $arr, $id, $addbo){
		$rows 	= array();
		$yezhuid = m('wuye')->getyezhuid();
		if($yezhuid>0){
			$yzinfo = m('wyyezhu')->getone($yezhuid);
			$rows['yezhuid'] 	= $yezhuid; //保存业主id
			$rows['yezhuname'] = arrvalue($yzinfo, 'name');
		}
		return array(
			'rows' => $rows
		);
	}
	
	protected function storeafter($table, $rows)
	{
		
		$barr  = array();
		if($this->loadci==1){
			$barr = m('wuye')->getxqfangtree();
		}
		return array(
			'xqarr' => $barr
		);
	}
	
	public function auto_address()
	{
		$str 	 = '';
		$wyobj	 = m('wuye');
		$yezhuid = $wyobj->getyezhuid();
		if($yezhuid>0){
			$allfeng = $wyobj->getyezhutofang($yezhuid);
			foreach($allfeng as $k=>$rs){
				$str1 = $wyobj->getfanginfo(array(
					'xqname' => $rs['xqname'],
					'louname' => $rs['louname'],
					'danyuan' => $rs['danyuan'],
					'fangname' => '',
				));
				$str.='<a href="javascript:;" onclick="c.changeweizhi('.$rs['xqid'].','.$rs['louid'].','.$rs['danyuan'].')">'.$str1.'</a>&nbsp;&nbsp;';
			}
		}
		return '<input class="inputs" name="address" placeholder="可输入你的位置">'.$str.'';
	}
}	
			