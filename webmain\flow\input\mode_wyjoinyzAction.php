<?php
/**
*	此文件是流程模块【wyjoinyz.业主入住】对应控制器接口文件。
*/ 
class mode_wyjoinyzClassAction extends inputAction{
	
	
	protected function savebefore($table, $arr, $id, $addbo){
		$where = "`fangid`='".$arr['fangid']."'";
		$where .= " and `yezhuid`='".$arr['yezhuid']."'";
		$where .= " and `id`<>$id";
		if(m($table)->rows($where)>0)return '此房屋和业主已登记入住了';
	}
	
	/**
	*	重写函数：保存后处理，主要保存其他表数据
	*	$table String 对应表名
	*	$arr Array 表单参数
	*	$id Int 对应表上记录Id
	*	$addbo Boolean 是否添加时
	*/	
	protected function saveafter($table, $arr, $id, $addbo){
		if($arr['guanxi']=='业主'){
			m('wyfang')->update(array(
				'yezhuid' => $arr['yezhuid'],
				'rzdt'	 => $arr['dt']
			), $arr['fangid']);
		}
	}
}	
			