<?php
/**
*	此文件是流程模块【wylou.楼栋管理】对应控制器接口文件。
*/ 
class mode_wylouClassAction extends inputAction{
	
	protected function storeafter($table, $rows)
	{
		return array(
			'xqarr' => m('wuye')->getxiaoqu()
		);
	}
	
	public function uploadtongAjax()
	{
		m('wuye')->updatexiaoqu();
	}
	
	protected function savebefore($table, $arr, $id, $addbo){
		$to = m($table)->rows("`xqid`='".$arr['xqid']."' and `name`='".$arr['name']."' and `id`<>'$id'");
		if($to>0)return '楼的名称“'.$arr['name'].'”已经存在了';
	}
	
	public function createfangAjax()
	{
		$mid = (int)$this->post('mid');
		$ks  = (int)$this->post('ksceng','1');
		$fangshu = (int)$this->post('fangshu','1');
		$lours 	 = m('wylou')->getone($mid);
		$cengshu = (int)$lours['cengshu'];
		
		$zs 	 = 0;
		$db 	 = m('wyfang');
		$rows 	 = $db->getall('louid='.$mid.'');
		$sars 	 = array();
		foreach($rows as $k=>$rs)$sars[$rs['name']] = $rs['id'];
		
		$mianjiarr = array();
		$huxingarr = array();
		for($j=1;$j<=$fangshu;$j++){
			$mianjiarr[$j]  = $this->post('mianji'.$j.'');
			$huxingarr[$j]  = $this->post('huxing'.$j.'');
		}
		
		for($i=$ks;$i<=$cengshu;$i++){
			
			for($j=1;$j<=$fangshu;$j++){
				$ls = $j;
				if($j<10)$ls = '0'.$j.'';
				$name    = ''.$i.''.$ls.'';
				$mianji  = $mianjiarr[$j];
				$huxing  = $huxingarr[$j];
				if($mianji && $huxing){
					$fid = (int)arrvalue($sars, $name,'0');
					$uarr= array();
					$uarr['name']   = $name;
					$uarr['mianji'] = $mianji;
					$uarr['huxing'] = $huxing;
					$uarr['xqid']   = $lours['xqid'];
					$uarr['louid']  = $mid;
					$uarr['comid']  = $lours['comid'];
					$uarr['ceng']  = $i;
					//$uarr['optid']  = $this->adminid;
					$where = '`id`='.$fid.'';
					if($fid==0){
						$where = ''; 
					}
					$db->record($uarr, $where);
					$zs++;
				}
			}
		}
		$fangshu = m('wyfang')->rows('`louid`='.$mid.'');
		m('wylou')->update('`fangshu`='.$fangshu.'', $mid);
		
		
		return returnsuccess('总共生成房屋'.$zs.'数');
	}
}	
			