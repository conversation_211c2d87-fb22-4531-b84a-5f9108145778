<?php
/**
*	此文件是流程模块【wysninfo.设备档案】对应控制器接口文件。
*/ 
class mode_wysninfoClassAction extends inputAction{
	
	
	protected function savebefore($table, $arr, $id, $addbo){
		
	}
	
		
	protected function saveafter($table, $arr, $id, $addbo){
		
	}
	
	protected function storeafter($table, $rows)
	{
		
		$barr  = array();
		if($this->loadci==1){
			$barr = m('wuye')->getxqfangtree();
		}
		return array(
			'xqarr' => $barr
		);
	}
}	
			