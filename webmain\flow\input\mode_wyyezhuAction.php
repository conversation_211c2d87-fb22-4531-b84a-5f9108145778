<?php
/**
*	此文件是流程模块【wyyezhu.业主管理】对应控制器接口文件。
*/ 
class mode_wyyezhuClassAction extends inputAction{
	

	protected function savebefore($table, $arr, $id, $addbo){
		$rows 	= array();
		$rows['mobile'] = $this->rock->jm->strlook(arrvalue($arr,'mobile'));
		$rows['address']= $this->rock->jm->strlook(arrvalue($arr,'address'));
		$rows['idnum'] 	= $this->rock->jm->strlook(arrvalue($arr,'idnum'));
		$notsave	= '';
		if(isempt($rows['mobile']))$notsave	.= ',mobile';
		if(isempt($rows['address']))$notsave.= ',address';
		if(isempt($rows['idnum']))$notsave	.= ',idnum';
		
		if($notsave!='')$notsave = substr($notsave,1);
		return array(
			'rows' => $rows,
			'notsave' => $notsave,
		);
	}
	
	
	protected function saveafter($table, $arr, $id, $addbo){
		
	}
	
	
	protected function storeafter($table, $rows)
	{
		
		$barr  = array();
		if($this->loadci==1){
			$barr = m('wuye')->getxqfangtree();
		}
		return array(
			'xqarr' => $barr
		);
	}
}	
			