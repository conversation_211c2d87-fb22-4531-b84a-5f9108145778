<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<title><?=$da['title']?></title>
<link rel="stylesheet" href="<?=$da['p']?>/css/rock.css" />
<link rel="stylesheet" type="text/css" href="mode/plugin/css/jquery-rockdatepicker.css"/>
<link rel="shortcut icon" href="favicon.ico" />
<script type="text/javascript" src="js/jquery.js"></script>
<script type="text/javascript" src="js/js.js?<?=$nowtime?>"></script>
<script type="text/javascript" src="js/base64-min.js"></script>
<script type="text/javascript" src="mode/plugin/jquery-rockdatepicker.js"></script>
<script type="text/javascript" src="<?=$da['p']?>/flow/input/inputjs/input.js?<?=$nowtime?>"></script>
<script type="text/javascript" src="web/res/js/jquery-changeuser.js?<?=$nowtime?>"></script>
<script type="text/javascript" src="mode/plugin/jquery-rockmodels.js?<?=$nowtime?>"></script>
<script type="text/javascript">
var editor,arr=<?=$da['fieldsjson']?>,moders=<?=json_encode($da['moders'])?>,gongsiarr=<?=json_encode($da['gongsiarr'])?>,subfielsa=<?=json_encode($da['subfielsa'])?>,zbnamearr=<?=json_encode($da['zbnamearr'])?>,isedit=0,mid='<?=$da['mid']?>',isinput=1,data={},tempdata='<?=$tempdata?>';
</script>
<?php
echo c('color')->getApptheme();
?>
<style>
<?php
$colarr = c('color')->getColor();
$maincolors= $colarr['colors'];
?>
.ys1{padding:5px 5px;color:#666666}
.ys2{padding:5px 5px;}

.inputs,.textarea{flex:1;width:95%;margin:3px 0px;width:-webkit-fill-available}
.cionsss{padding:4px; background-color:#dddddd}
.datesss{background:url(mode/icons/date.png) no-repeat right;cursor:pointer}

.status{position: absolute;right:5px;top:10px;display:none;width:80px;height:80px;overflow:hidden; border:3px red solid;border-radius:50%;font-size:20px;text-align:center;line-height:80px;color:red;transform:rotate(-45deg);-o-transform:rotate(-45deg);-ms-transform:rotate(-45deg);-webkit-transform:rotate(-45deg);filter:progid:DXImagetransform.Microsoft.Matrix(M11=0.707,M12=-0.707,M21=0.707,M22=0.707,SizingMethod='auto expand');}

.tablesub{width:-webkit-fill-available}
.tablesub td{height:30px;text-align:center;border:var(--border)}
.tablesub .inputs,.tablesub .textarea{border:none;background:none;border-radius:0;margin:0;width:95%;}
.tablesub .inputs[readonly]{background-color:rgba(0,0,0,0.05);}

.course{padding:8px; background-color:rgba(<?=$maincolors?>,0.2);border:1px rgba(<?=$maincolors?>,0.3) solid;margin-right:10px;text-align:center;border-radius:5px}
.coursejt{height:8px;overflow:hidden;width:30px;background-color:rgba(<?=$maincolors?>,0.2)}
.coursejts{width:0px;  height:0px; overflow:hidden;border-width:8px;border-style:solid;border-color:transparent transparent transparent rgba(<?=$maincolors?>,0.2);}
.tishi{color:#888888;font-size:12px;padding:3px}
.ke-icon-crop{background-image: url(mode/icons/crop.png);width: 16px;height: 16px;}
.tablesub .xuhao{text-align:center;width:100%;padding:0;min-width:35px}
.tablesub .xuhao[readonly]{background:none}
.tablesub tr:first-child{background:var(--main-vgcolor)}
.zbtitle{}
.addys{}
</style>
</head>
<body>
<div align="center">
	<div class="blank10"></div>
	<div style="min: width 700px;; position:relative;max-width:<?=$da['inputwidth']?>;margin:0px 10px;">
		<div onclick="$(this).remove()" class="status"></div>
		<div style="padding-bottom:8px;"><span id="inputtitle" onclick="location.reload()" style="font-size:22px"><?=$da['title']?></span></div>
		<div class="tdcont border" style="background:var(--main-bgcolor);padding:10px;border-radius:5px" align="left">
			<form name="myform" autocomplete="off">
			<input name="id" type="hidden" value="<?=$da['mid']?>">
			<input name="sxuanfileid" type="hidden" value="">
			<?php
			$firstrs 	= array();
			$coursestr 	= '';
			for($i=0;$i<$da['zbshu'];$i++)echo '<input value="0" type="hidden" name="sub_totals'.$i.'">';
			echo $da['content'];
			if($da['course']){

				$coursestr = '<div align="center" style="padding:20px 0px">';
				$coursestr .='<table><tr>';
				foreach($da['course'] as $k=>$rs){
					$coursestr .='<td><div class="course">'.$rs['name'].'';
					$coursestr .='</div></td>';
					if($rs['id']>-1){
						if($rs['id'] > 0){
							if(!$firstrs)$firstrs = $rs;
							if($rs['isnow'])$firstrs = $rs;
						}
						$coursestr .='<td><div class="coursejt"></div></td>';
						$coursestr .='<td><div class="coursejts"></div></td>';
					}
				}
				$coursestr .= '</tr></table>';
				$coursestr .= '</div>';

				if($da['moders']['isbxs']==1)$coursestr='';//不显示流程图

				//判断流程步骤是否上步指定
				if($firstrs && $firstrs['checktype']=='change'){
					$firstrs['isbt'] = 0;
					$placeholder = '可不选,如自己选择不要随意选择';
					$stsp = '<div style="padding-top:20px"><table width="100%"><tr><td width="15%" nowrap height="30" align="right">';
					if($firstrs['checktype']=='change'){
						$stsp.='<font color=red>*</font>';
						$placeholder = '必须指定人员'.arrvalue($firstrs,'explain').'';
						$firstrs['isbt'] = 1;
					}
					$stsp.= ''.$firstrs['name'].'：</td>';
					$stsp.= '<td width="100%"><div class="btn-group"><input  class="inputs" style="width:99%" id="sysnextchange" value="'.$firstrs['sysnextopt'].'" placeholder="'.$placeholder.'" readonly type="text" name="sysnextopt"><input name="sysnextoptid" value="'.$firstrs['sysnextoptid'].'" id="sysnextchange_id" type="hidden"><input name="sysnextcustidid" value="'.$firstrs['id'].'" type="hidden">';
					$stsp.= '<button type="button" onclick="js.changeclear(\'sysnextchange\')" class="webbtn">×</button><button type="button" id="btnchange_recename" onclick="js.changeuser(\'sysnextchange\',\'changeusercheck\',\'选择'.$firstrs['name'].'\',{changerange:\''.$firstrs['checktypeid'].'\'})" class="webbtn">选择</button></div></td>';
					$stsp.= '</tr></table></div>';
					echo $stsp;
				}
			}

			//--start--
			if($da['moders']['isflow']==3 || ($da['moders']['isflow']==4 && isset($da['courserows']))){
				$str1 = '<div align="center"><div style="text-align:center;background:#EDF7FC;line-height:30px;border-radius:5px;margin-top:20px;"><b>下步处理</b></div>';

				$str1.='<div style="padding-top:5px">';
				$str2 = $inputobj->inputchangeuser(array(
					'name'		=>'sys_nextcoursename',
					'id'		=>'sys_nextcoursenameid',
					'placeholder'=>'选择下步处理人',
					'type'		=> 'changeusercheck',
					'title'		=> '选择下步处理人',
				));
				$str3 = '';
				$dsa1 = ($da['moders']['isflow']==4) ? 'none' : '';
				foreach($da['courserows'] as $k3=>$rs3)$str3.='<option changerange="'.$rs3['checktypeid'].'" value="'.$rs3['id'].'" checktype="'.$rs3['checktype'].'">'.$rs3['id'].'.'.$rs3['name'].'</option>';
				$str1.='<table width="100%"><tr><td width="15%" nowrap height="30" align="right"><font color=red>*</font>下步处理&nbsp;</td><td width="100%"><select name="sys_nextcourseid" class="inputs" onchange="c.changenextcourse(this, '.$da['moders']['isflow'].')"><option value="">-请选择-</option>'.$str3.'</select></td></tr><tr><td>&nbsp;</td></tr><tr style="display:'.$dsa1.'" id="sys_nextcoursediv1"><td nowrap height="30" align="right"><font color=red>*</font>下步处理人&nbsp;</td><td>'.$str2.'</td></tr></table>';
				$str1.='</div>';
				$str1.='</div>';
				echo $str1;
			}
			if($da['moders']['isflow']==5){
				$csst1= '';
				if($da['mid']=='0')$csst1= '，<a class="zhu" href="javascript:;" onclick="c.flow5import()">引入上次</a>';
				$str1 = '<div align="center"><div style="text-align:center;background:var(--main-border);line-height:30px;border-radius:5px;margin-top:20px;"><b>自定义审核流程</b>(顺序依次审核)'.$csst1.'</div>';
				$str1.= '<div style="display:inline-block;margin-top:10px" id="flow5div"><div style="border:dashed 1px #cccccc;border-radius:5px;height:50px" class="upload_items"><img class="imgs" src="images/jia.png" style="height:40px;width:40px"></div></div>';
				echo $str1;
			}
			//--end--

			//是否抄送
			$iscs = (int)$da['moders']['iscs'];
			if($iscs>0){
				$csstr= $inputobj->inputchangeuser(array(
					'name'		=> 'syschaosong',
					'id'		=> 'syschaosongid',
					'placeholder'=>'选择要抄送的人员',
					'type'		=> 'changeusercheck',
					'title'		=> '选择抄送人员',
					'value' 	=> $da['chao']['csname'],
					'valueid'	=> $da['chao']['csnameid']
				));
				echo '<div style="padding-top:20px"><table width="100%"><tr><td width="15%" nowrap height="30" align="right"><font color="red">'.(($iscs==1) ? '':'*').'</font>抄送给&nbsp;</td><td width="100%">'.$csstr.'</td></tr></table></div>';
			}
			?>
			</form>
		</div>
		<?php echo $coursestr;?>
	</div>
	<div style="height:70px; overflow:hidden"></div>
	<div align="right" style="background:var(--main-bgcolor);border-top:var(--border);padding:10px 0px; position:fixed;width:100%;bottom:0px;left:0px;z-index:10">
		<span id="msgview"></span>&nbsp;
		<span id="AltSspan" style="display:none" >
		<input id="Altzhan" type="button" style="border-radius:5px;background:#999" onclick="return c.savezhan()" value="暂时保存" class="webbtn" href="javascript:;">&nbsp;<?php
		if($da['moders']['isflow']>0)echo '<input id="AltCaogao" type="button" style="border-radius:5px;background:#666" onclick="return c.savecaogao()" value="保存草稿" class="webbtn" href="javascript:;">&nbsp;';
		?><input id="AltS" type="button" onclick="return c.save()" value="直接提交" class="webbtn"></span>&nbsp; &nbsp;
	</div>
</div>
<script>
firstrs=<?=json_encode($firstrs)?>;
</script>
<script type="text/javascript" src="<?=$da['p']?>/flow/input/inputjs/mode_<?=$da['moders']['num']?>.js?<?=time()?>"></script>
<script type="text/javascript" src="web/res/js/jquery-rockupload.js"></script>
<script type="text/javascript" src="web/res/js/jquery-imgview.js"></script>
<?php 
if($otherfile)include_once($otherfile);
?>
</body>
</html>