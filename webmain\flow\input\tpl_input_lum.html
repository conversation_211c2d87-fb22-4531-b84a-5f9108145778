<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="viewport" content="maximum-scale=1.0,minimum-scale=1.0,user-scalable=0,width=device-width,initial-scale=1.0"/>
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-status-bar-style" content="yes" />
<title><?=$da['title']?></title>
<link rel="shortcut icon" href="favicon.ico" />
<link rel="stylesheet" type="text/css" href="<?=$da['p']?>/css/cssm.css?<?=$nowtime?>">
<link rel="stylesheet" type="text/css" href="mode/plugin/css/jquery-rockdatepicker.css"/>
<script type="text/javascript" src="js/jquery.js"></script>
<script type="text/javascript" src="js/js.js?<?=$nowtime?>"></script>
<script type="text/javascript" src="js/base64-min.js"></script>
<script type="text/javascript" src="mode/plugin/jquery-rockdatepicker.js"></script>
<script type="text/javascript" src="mode/plugin/jquery-rockdatepicker-mobile.js"></script>
<script type="text/javascript" src="web/res/js/jquery-rockupload.js"></script>
<script type="text/javascript" src="<?=$da['p']?>/flow/input/inputjs/input.js?<?=$nowtime?>"></script>
<script type="text/javascript" src="web/res/js/jquery-changeuser.js?<?=$nowtime?>"></script>
<script type="text/javascript" src="web/res/js/jquery-imgview.js"></script>
<script type="text/javascript" src="mode/plugin/jquery-rockmodels.js"></script>
<script type="text/javascript">
<?php
$maincolor = getconfig('apptheme','#1389D3');
?>
var editor,arr=<?=$da['fieldsjson']?>,moders=<?=json_encode($da['moders'])?>,gongsiarr=<?=json_encode($da['gongsiarr'])?>,subfielsa=<?=json_encode($da['subfielsa'])?>,zbnamearr=<?=json_encode($da['zbnamearr'])?>,isedit=0,mid='<?=$da['mid']?>',isinput=1,data={},maincolor='<?=$maincolor?>',tempdata='<?=$tempdata?>';
ismobile=1;
js.changeuser=function(na,lx,tit,cans){
	var can = {
		'changetype':lx,
		'titlebool':get('header_title'),
		'idobj':get(na+'_id'),
		'nameobj':get(na),
		'onselect':function(sna,sid){
			js.changeuser_after(this.formname,this,sna,sid);
		}
	};
	var formname = get(na).name;
	var bcar = js.changeuser_before(formname),i;
	for(i in cans)can[i]=cans[i];
	if(typeof(bcar)=='string' && bcar){js.msg('msg', bcar);return;}
	if(typeof(bcar)=='object')for(i in bcar)can[i]=bcar[i];
	can.formname = formname;
	$('body').chnageuser(can);
}
function clearuser(na){
	get(na).value='';
	get(na+'_id').value='';
	get(na).focus();
}
js.datechange=function(o1,lx){
	$.rockdatepicker_mobile({
		'inputobj':o1,
		'view':lx
	});
}
function initApp(){
	js.setapptitle(moders.name);
}


// 字段分组折叠展开控制函数 - 实现互斥展开
function toggleFieldSection(header) {
	var content = header.nextElementSibling;
	var icon = header.querySelector('.field-section-icon');
	var isCurrentlyOpen = content.style.display === 'block';
	
	// 先关闭所有其他分组
	var allSections = document.querySelectorAll('.field-section-content');
	var allIcons = document.querySelectorAll('.field-section-icon');
	
	for(var i = 0; i < allSections.length; i++) {
		allSections[i].style.display = 'none';
		allIcons[i].textContent = '▼';
		allIcons[i].style.transform = 'rotate(180deg)';
	}
	
	// 如果当前分组原来是关闭的，则打开它
	if (!isCurrentlyOpen) {
		content.style.display = 'block';
		icon.textContent = '▲';
		icon.style.transform = 'rotate(0deg)';
	}
}

// 移动端消息提示管理 - 模仿原生js.msg行为
var mobileMsg = {
	timer: null,
	
	// 重写js.setmsg函数，在移动端使用自定义样式和行为
	setmsg: function(txt, col, ids) {
		if (!ids) ids = 'msgview';
		
		// 如果是移动端的msgview，使用自定义处理
		if (ids === 'msgview' && typeof(ismobile) !== 'undefined' && ismobile == 1) {
			this.showMsg(txt, col);
		} else {
			// 其他情况使用原生方法
			$('#' + ids).html(js.getmsg(txt, col));
		}
	},
	
	// 显示消息
	showMsg: function(txt, col) {
		var msgEl = document.getElementById('msgview');
		if (!msgEl) return;
		
		// 清除之前的定时器
		if (this.timer) {
			clearTimeout(this.timer);
			this.timer = null;
		}
		
		// 如果消息为空，隐藏容器
		if (!txt) {
			msgEl.innerHTML = '';
			msgEl.style.display = 'none';
			return;
		}
		
		// 设置颜色
		if (!col) col = 'red';
		if (col === 'green') col = '#4CAF50';
		if (col === 'red') col = '#f44336';
		
		// 创建消息内容
		var msgContent = '<div onclick="mobileMsg.hideMsg()" style="color:' + col + '">' + txt + '</div>';
		msgEl.innerHTML = msgContent;
		msgEl.style.display = 'block';
		
		// 5秒后自动隐藏
		this.timer = setTimeout(function() {
			mobileMsg.hideMsg();
		}, 5000);
	},
	
	// 隐藏消息
	hideMsg: function() {
		var msgEl = document.getElementById('msgview');
		if (msgEl) {
			msgEl.innerHTML = '';
			msgEl.style.display = 'none';
		}
		if (this.timer) {
			clearTimeout(this.timer);
			this.timer = null;
		}
	}
};

// 在移动端重写js.setmsg和js.msg函数
if (typeof(ismobile) !== 'undefined' && ismobile == 1) {
	js.setmsg = function(txt, col, ids) {
		mobileMsg.setmsg(txt, col, ids);
	};
	
	// 重写js.msg函数，在移动端使用统一的消息提示
	var originalJsMsg = js.msg;
	js.msg = function(lx, txt, sj) {
		// 如果是none或空，清除消息
		if (lx == 'none' || !lx) {
			mobileMsg.hideMsg();
			return;
		}
		
		// 处理不同类型的消息
		var color = 'red';
		if (lx == 'success') color = 'green';
		if (lx == 'wait') {
			txt = '⏳ ' + txt;
			color = '#ff6600';
		}
		if (lx == 'msg') color = 'red';
		
		// 使用自定义消息显示
		mobileMsg.showMsg(txt, color);
	};
}
</script>
<style>
<?php
$maincolora= c('image')->colorTorgb($maincolor);
$maincolors= ''.$maincolora[0].','.$maincolora[1].','.$maincolora[2].'';
echo 'body{--main-color:'.$maincolor.';--font-size:16px;}';
?>
.datesss{background:url(mode/icons/date.png) no-repeat right;cursor:pointer}
input,textarea,select,*,td, button{font-size:16px}
.lurim{text-align:right;padding-left:5px}
.tablesub td{height:25px;text-align:left;border:0px #888888 solid;}
.tablesub .inputs{width:-webkit-fill-available}

.status{position: absolute;right:15px;top:2px;display:none;width:70px;height:70px;overflow:hidden; border:2px red solid;border-radius:50%;font-size:16px;text-align:center;line-height:70px;color:red;transform:rotate(-45deg);-o-transform:rotate(-45deg);-webkit-transform:rotate(-45deg);-ms-transform:rotate(-45deg)}


.btn-default{background-color:#1389D3;}
.btn-danger{background-color:#d9534f;}
.btn:hover{opacity:1;color:#ffffff}
.lumtr{background-color:white}
.tablelum{}
.inputs1{border-bottom:0.5px #cccccc solid;border-top:0px;border-left:0px;border-right:0px}
.inputs1:focus{border:none;box-shadow:none;border-bottom:0.5px <?=$maincolor?> solid;}


.divzb0{display:inline-block;width:100%;margin:5px 0px}
.divzb1{float:left;width:25%;overflow:auto;text-align:right;line-height:20px;margin-top:5px}
.divzb2{float:left;width:73%}

/* dual column layout for subtable */
.divzb-row {
    display: flex;
    width: 100%;
}
.divzb-field {
    width: 50%;
    display: flex;
    align-items: center;
    padding: 0px;
	box-sizing: border-box;
}
.divzb-field:first-child{
	border-right: 0px solid #fcfcfc;
}
.divzb-label {
    width: 40%;
    text-align: right;
    padding-right: 5px;
    color: #888;
    font-size: 14px;
	word-break: break-all;
}
.divzb-input {
    width: 60%;
}
.divzb-input .inputs{
    width:98%;
}

.divzb-field-full {
    width: 100%;
    display: flex;
    align-items: center;
    padding: 5px;
    box-sizing: border-box;
}
.divzb-field-full > .divzb-label {
    width: 25%;
	font-size: 14px;
	word-break: break-all;
}
.divzb-field-full > .divzb-input {
    width: 75%;
}

.xuhao{border:none;font-size:14px;text-align:left;color:#888888;padding:0;margin:0;margin-left:2px;padding-top:2px}
.xuhao:focus{border:none;}
.xuantitle{text-align:left;font-size:14px;padding-left:8px;line-height:25px;margin-top:10px;color:#888888;}
.divinput .btn-group{width:98%}

/* 移动端录入页面固定底部提交栏样式 */
.mobile-submit-bar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #fff;
	border-top: 1px solid #e0e0e0;
	padding: 4px 8px;
	text-align: center;
	z-index: 50;
	box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
	display: flex;
	align-items: center;
}

.mobile-submit-left {
	width: 50%;
	display: flex;
	justify-content: space-evenly;
	align-items: center;
}

.mobile-side-btn {
	background: none;
	border: none;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	width: 60px;
	font-size: 12px;
	color: #666;
}
.mobile-side-btn:active{opacity:0.8;}

.side-btn-icon {
	width: 20px;
	height: 20px;
	margin-bottom: 2px;
}

.side-btn-text{
    font-size:10px;
}

.mobile-submit-right {
	width: 50%;
	display: flex;
	justify-content: center;
}

.mobile-submit-btn {
	width: 100%;
	height: 42px;
	border: none;
	border-radius: 21px;
	background: #1389D3;
	color: #fff;
	font-size: 16px;
}
.mobile-submit-btn:active{opacity:0.9;}

/* 为固定bottom栏预留空间，避免内容被遮挡 */
.mobile-submit-spacer {
	height: 66px;
}

/* 字段分组折叠样式 */
.field-section {
	margin: 5px 0;
	border: 1px solid #e0e0e0;
	border-radius: 5px;
	background: #fff;
}

.field-section-header {
	padding: 12px 15px;
	background: #f8f9fa;
	border-bottom: 1px solid #e0e0e0;
	cursor: pointer;
	display: flex;
	justify-content: space-between;
	align-items: center;
	user-select: none;
	-webkit-user-select: none;
}

.field-section-header:active {
	background: #e9ecef;
}

.field-section-header.required-section {
	background: #e8f5e8;
	border-bottom: 1px solid #c3e6c3;
}

.field-section-header.optional-section {
	background: #ece9dd;
	border-bottom: 1px solid #e6e3db;
}

.field-section-title {
	font-weight: bold;
	font-size: 14px;
	color: #333;
	margin-right: 10px;
}

.field-section-icon {
	font-size: 14px;
	color: #666;
	transition: transform 0.3s ease;
}

.field-section-content {
	overflow: hidden;
	transition: all 0.3s ease;
}

.field-section-content table {
	margin: 0;
}

/* 调整字段布局宽度 */
.field-section-content .lumtr .lurim {
	width: 25%;
	text-align: left;
	padding: 8px;
	vertical-align: middle;
	word-wrap: break-word;
	word-break: break-all;
	line-height: 1.4;
	display: table-cell;
}

.field-section-content .lumtr td:last-child {
	width: 75%;
	vertical-align: middle;
}

/* 移动端子表删除按钮样式 */
a[onclick^="c.delrow"] {
    position:static !important;
    color:#1389D3 !important;
    padding:4px 8px;
    border-radius:4px;
    font-size:16px;
    margin-right:6px;
}

/* === 移动端子表样式优化 Start === */
.divzb-row {
    /* 增加行间距及背景 */
    margin-bottom: 2px;
    background-color: #ffffff;
}
.divzb-row:nth-child(odd) {
    background-color: #fafafa;
}

.divzb-field {
    /* 调整内边距，保证触控友好 */
    padding: 8px 6px;
    box-sizing: border-box;
}

.divzb-label {
    /* 提升可读性 */
    font-size: 15px;
    line-height: 1.4;
    padding-right: 6px;
}

.divzb-input {
    /* 让输入区域占满剩余空间 */
    width: 60%;
}

.divzb-input .inputs,
.divzb-input input,
.divzb-input select,
.divzb-input textarea {
    width: 100% !important;
    border: 1px solid #e6e6e6;
    border-radius: 4px;
    padding: 6px 8px;
    box-sizing: border-box;
    font-size: 15px;
}

.divzb-field-full {
    padding: 8px 6px;
    box-sizing: border-box;
}

/* 细化子表边框与投影，增加分隔感 */
.tablelum .tablesub {
    border: 1px solid #eaeaea;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0,0,0,0.03);
}
.tablesub td {
    border-color: #eaeaea;
}

/* 小屏幕进一步缩小字体，避免换行过多 */
@media (max-width: 420px) {
    .divzb-label,
    .divzb-input .inputs,
    .divzb-input input,
    .divzb-input select,
    .divzb-input textarea {
        font-size: 13px;
    }
}
/* === 移动端子表样式优化 End === */

/* 新增箭头按钮及隐藏复制按钮 */
.subrow-toggle-btn{
    font-size:16px;
    color:#999;
    transition:transform 0.3s ease;
    margin-left:4px;
}

/* 调整子表整体及头部样式 */
.tablelum .tablesub{
    background:#ffffff;
    margin-bottom:12px;
}
.mobile-subrow-header{
    background:#f9f9f9;
}

/* 序号圆形背景 */
.subrow-xuhao{
	display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #3498db;
  color: white;
  font-size: 14px;
  font-weight: bold;
  text-align: center;
  margin-right: 8px;
}

/* === 统一配色与风格 === */
:root{
    --border-color:#e0e0e0;
    --header-bg:#f9f9f9;
    --required-bg:#e8f5e8; /* 绿色浅色 */
    --optional-bg:#f2f2f2; /* 灰色浅色 */
    --text-color:#333;
}

.mobile-subrow-header{
    background:var(--header-bg);
    border:1px solid var(--border-color);
    border-bottom:0;
}

.tablelum .tablesub{
    border:1px solid var(--border-color);
    border-radius:4px;
    box-shadow:none;
}

.divzb-row{
    border-bottom:1px solid var(--border-color);
}

.field-section-header.required-section{background:var(--required-bg);} 
.field-section-header.optional-section{background:var(--optional-bg);} 

.subrow-toggle-btn{color:#666;} 

/* 字段 label 星号颜色统一 */
.divzb-label font[color="red"]{color:#e74c3c !important;}

/* === end 统一配色 === */

/* === 字体统一：子表与主表一致 === */
.divzb-label,
.divzb-field-full > .divzb-label,
.divzb-input .inputs,
.divzb-input input,
.divzb-input select,
.divzb-input textarea{
    font-size:var(--font-size) !important;
}

/* === Begin vertical label-input layout for dual-column (minhang) fields === */
.divzb-field{
    /* keep two fields per row but stack label and input vertically */
    display:block;
    width:50%;
    padding:4px 6px;
    box-sizing:border-box;
    background-color:#fff;
}
.divzb-label{
    width:100%;
    text-align:left;
    padding:0 0 2px 0; /* compact space between label and input */
}
.divzb-input{
    width:100%;
}
.divzb-input .inputs,
.divzb-input input,
.divzb-input select,
.divzb-input textarea{
    width:100% !important;
}
/* === End vertical layout rules === */

/* 统一垂直布局输入框高度，避免部分字段高度过小 */
.divzb-input .inputs{
    height:40px !important;
    line-height:38px !important;
}

/* 子表头高度调小 */
.mobile-subrow-header,
.mobile-subrow-header td{
    padding:4.5px 6px !important;
    line-height:20px !important;
}

/* 修复子表序号圆形背景中 input 填充问题 */
.subrow-xuhao input{
    width:100% !important;
    height:100% !important;
    border:none !important;
    background:transparent !important;
    color:#ffffff !important;
    text-align:center;
    padding:0 !important;
    margin:0 !important;
    font-weight:bold;
}

/* 统一移动端录入页面字段文字颜色为 #555 */
.mbody,
.mbody input,
.mbody select,
.mbody textarea,
.lurim,
.divzb-label,
.field-section-title{
    color:#555 !important;
}

/* 独立的提示信息容器，固定在提交栏上方，模仿原生js.msg样式 */
.mobile-msg-view{
    position:fixed;
    left:0;
    right:0;
    bottom:72px; /* 提交栏高度(66px)+间隙 */
    text-align:center;
    z-index:200; /* 提高层级，与原生js.msg一致 */
    pointer-events:auto; /* 允许点击 */
    font-size:16px;
    display:none; /* 默认隐藏 */
}

.mobile-msg-view > div{
    display:inline-block;
    padding:8px 20px;
    background:rgba(0,0,0,0.7);
    color:white;
    border-radius:5px;
    cursor:pointer; /* 提示可点击 */
    max-width:90%;
    word-wrap:break-word;
}
</style>
</head>

<body class="mbody">
<?php 
if($showheader==1)echo '<div id="header_title" style="padding-top:'.$cenghei.'px" class="header"><span onclick="js.back()" class="header-back"></span>'.$da['title'].'</div><div style="height:'.($cenghei+50).'px;overflow:hidden"></div>';
?>

<div  style="position:relative">
	<div class="status"></div>
	<form name="myform" autocomplete="off">
		<input name="id" type="hidden" value="<?=$da['mid']?>">
		<input name="sxuanfileid" type="hidden" value="">
		<?php 
		for($i=0;$i<$da['zbshu'];$i++)echo '<input value="0" type="hidden" name="sub_totals'.$i.'">';
		?>
		<div style="padding-top:10px">
		<table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablelum">
		<?php if($da['mid']==0){?>
		<?php
		}
		echo $da['content'];
		if($da['isupfile']==1){
		?>
		<tr class="lumtr">
		<td class="lurim" nowrap><?=$da['isupfiles']?></td>
		<td><div class="divinput">
			<input name="fileid" id="fileidview-inputEl" type="hidden">
			<div id="view_fileidview" style="height:auto;min-height:60px" class="inputs"></div>
			<div><input style="width:150px" onchange="f.change(this)" type="file"></div>
		</div></td>
		</tr>
		<?php
		}
		$firstrs = array();
		if($da['course']){
			$coursestr = '<div align="center" style="padding:20px 0px">';
			$coursestr .='<table><tr>';
			foreach($da['course'] as $k=>$rs){
				$coursestr .='<td><div class="course">'.$rs['name'].'';
				$coursestr .='</div></td>';
				if($rs['id']>-1){
					if($rs['id'] > 0){
						if(!$firstrs)$firstrs = $rs;
						if($rs['isnow'])$firstrs = $rs;
					}
					$coursestr .='<td><div class="coursejt"></div></td>';
					$coursestr .='<td><div class="coursejts"></div></td>';
				}
			}
			$coursestr .= '</tr></table>';
			$coursestr .= '</div>';
			//判断流程步骤是否上步指定
			if($firstrs && $firstrs['checktype']=='change'){
				$placeholder = '可不选';
				$firstrs['isbt'] = 0;
				$stsp = '<tr><td colspan="2"><div class="xuantitle">选择审批人</div></td></tr>';
				$stsp.= '<tr class="lumtr">';
				$stsp.= '<td class="lurim" nowrap>';
				if($firstrs['checktype']=='change'){
					$stsp.='<font color=red>*</font>';
					$placeholder = '必须指定人员'.arrvalue($firstrs,'explain').'';
					$firstrs['isbt'] = 1;
				}
				$stsp.= ''.$firstrs['name'].':</td>';
				$stsp.= '<td><div class="divinput"><table width="98%" cellpadding="0" border="0"><tr><td width="100%"><div class="btn-group"><input class="inputs" style="flex:1" id="sysnextchange" value="'.$firstrs['sysnextopt'].'" placeholder="'.$placeholder.'" readonly type="text" name="sysnextopt"><input name="sysnextoptid" value="'.$firstrs['sysnextoptid'].'" id="sysnextchange_id" type="hidden"><input name="sysnextcustidid" value="'.$firstrs['id'].'" type="hidden">';
				$stsp.= '<button type="button" onclick="js.changeclear(\'sysnextchange\')" class="webbtn">×</button><button type="button" id="btnchange_recename" onclick="js.changeuser(\'sysnextchange\',\'changeusercheck\',\'\',{changerange:\''.$firstrs['checktypeid'].'\'})" class="webbtn">选择</button></div></td></tr></table></div></td>';
				$stsp.= '</tr>';
				echo $stsp;
			}
		}
		//--start--
		if($da['moders']['isflow']==3 || ($da['moders']['isflow']==4 && isset($da['courserows']))){
			$str1 = '<tr><td colspan="2"><div class="xuantitle">下步处理</div></td></tr>';
			$str2 = $inputobj->inputchangeuser(array(
				'name'		=>'sys_nextcoursename',
				'id'		=>'sys_nextcoursenameid',
				'placeholder'=>'选择下步处理人',
				'type'		=> 'changeusercheck',
				'title'		=> '选择下步处理人',
			));
			$str3 = '';
			$dsa1 = ($da['moders']['isflow']==4) ? 'none' : '';
			foreach($da['courserows'] as $k3=>$rs3)$str3.='<option changerange="'.$rs3['checktypeid'].'" value="'.$rs3['id'].'" checktype="'.$rs3['checktype'].'">'.$rs3['id'].'.'.$rs3['name'].'</option>';
			$str1.= '<tr class="lumtr"><td class="lurim" nowrap><font color="red">*</font>下步处理</td><td><div class="divinput"><select name="sys_nextcourseid" class="inputs" onchange="c.changenextcourse(this, '.$da['moders']['isflow'].')"><option value="">-请选择-</option>'.$str3.'</select></div></td></tr>';
			$str1.= '<tr class="lumtr" style="display:'.$dsa1.'" id="sys_nextcoursediv1"><td class="lurim" nowrap><font color="red">*</font>下步处理人</td><td><div class="divinput">'.$str2.'</div></td></tr>';
			echo $str1;
		}
		if($da['moders']['isflow']==5){
			$csstr= '<div style="display:inline-block" class="divinput" id="flow5div"><div style="border:dashed 1px #cccccc;border-radius:5px;height:50px" class="upload_items"><img class="imgs" src="images/jia.png" style="height:40px;width:40px"></div></div>';
			$csst1= '';
			if($da['mid']=='0')$csst1= '，<a class="zhu" href="javascript:;" onclick="c.flow5import()" style="font-size:12px">引入上次</a>';
			$str1 = '<tr><td colspan="2"><div class="xuantitle">自定义审核流程(依次审核)'.$csst1.'</div></td></tr>';
			$str1.= '<tr class="lumtr"><td class="lurim" nowrap><font color="red">*</font>审核流程</td><td>'.$csstr.'</td></tr>';
			echo $str1;
		}
		//--end--
		
		//是否抄送
		$iscs = (int)$da['moders']['iscs'];
		if($iscs>0){
			$csstr= $inputobj->inputchangeuser(array(
				'name'		=> 'syschaosong',
				'id'		=> 'syschaosongid',
				'placeholder'=>'选择要抄送的人员',
				'type'		=> 'changeusercheck',
				'title'		=> '选择抄送人员',
				'value' 	=> $da['chao']['csname'],
				'valueid'	=> $da['chao']['csnameid']
			));
			echo '<tr><td colspan="2"><div class="xuantitle">抄送</div></td></tr>';
			echo '<tr class="lumtr"><td class="lurim" nowrap><font color="red">'.(($iscs==1) ? '':'*').'</font>抄送给</td><td><div class="divinput">'.$csstr.'</div></td></tr>';
		}
		?>
		</table>
		</div>
	</form>
	<!-- 移动端录入页面底部操作栏，固定在屏幕底部 -->
	<div class="mobile-submit-bar">
		<!-- 左侧：暂存/草稿等按钮（图标在上，文字在下） -->
		<div class="mobile-submit-left" id="AltSspan" style="display:none">
			<button id="Altzhan" type="button" class="mobile-side-btn webbtn" onclick="return c.savezhan()">
				<img src="images/files.png" alt="保存" class="side-btn-icon">
				<span class="side-btn-text">暂存</span>
			</button>
			<?php if($da['moders']['isflow']>0){?>
			<button id="AltCaogao" type="button" class="mobile-side-btn webbtn" onclick="return c.savecaogao()">
				<img src="images/file.png" alt="草稿" class="side-btn-icon">
				<span class="side-btn-text">存草稿</span>
			</button>
			<?php }?>
		</div>
		<!-- 右侧：直接提交，占用 50% 宽度 -->
		<div class="mobile-submit-right">
			<button id="AltS" type="button" class="mobile-submit-btn webbtn" onclick="return c.save()">提交</button>
		</div>
		<!-- 消息提示 -->
		<!-- #msgview 被移出提交栏，避免提示信息挤占按钮 -->
	</div>
	<!-- 独立的消息提示容器，固定在提交栏之上 -->
	<div id="msgview" class="mobile-msg-view"></div>
	<!-- 为固定底部栏预留空间 -->
	<div class="mobile-submit-spacer"></div>
</div>
<script>
firstrs=<?=json_encode($firstrs)?>;
</script>
<script type="text/javascript" src="<?=$da['p']?>/flow/input/inputjs/mode_<?=$da['moders']['num']?>.js?<?=time()?>"></script>
<?php 
if($otherfile)include_once($otherfile);
if($da['showtype']=='view')echo '<script src="webmain/main/flowview/flowview_input.js?'.time().'"></script>';
?>
</body>
</html>