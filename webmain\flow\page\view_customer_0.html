<!-- 引入移动端修复脚本 -->
<script src="webmain/js/customer_mobile_fix.js"></script>

<div class="r-tabs" tabid="a">
	<div index="0" class="r-tabs-item active">
	客户资料
	</div>
	<div index="7" custid="{id}" class="r-tabs-item">
	跟进计划
	</div>
	<div index="1" custid="{id}" class="r-tabs-item">
	销售机会
	</div>
	<div index="2" custid="{id}" class="r-tabs-item">
	客户合同
	</div>
	<div index="5" custid="{id}" class="r-tabs-item">
	售后单
	</div>
	<div index="3" custid="{id}" class="r-tabs-item">
	收款单
	</div>
	<div index="4" custid="{id}" class="r-tabs-item">
	付款单
	</div>
	<div index="6" custid="{id}" class="r-tabs-item">
	销售单
	</div>
	<div index="8" custid="{id}" class="r-tabs-item">
	联系人
	</div>
</div>

<!--第一个内容，结构化客户详情-->
<div tabitem="0" tabid="a">
<table width="100%" bordercolor="#000000" border="0" class="ke-zeroborder">
<tbody>
<tr>
	<td height="34" width="15%" align="right" class="ys1">客户编号</td>
	<td class="ys2" width="35%">{custid}</td>
	<td align="right" class="ys1" width="15%">客户类型</td>
	<td class="ys2" width="35%">{type}</td>
</tr>
<tr>
	<td height="34" align="right" class="ys1">客户名称</td>
	<td class="ys2">{name}</td>
	<td class="ys1" align="right">^supername^</td>
	<td class="ys2">{supername}</td>
</tr>
<tr>
	<td height="34" align="right" class="ys1">^unitname^</td>
	<td class="ys2" colspan="3">{unitname}</td>
</tr>
<tr>
	<td height="34" align="right" class="ys1">客户状态</td>
	<td class="ys2">{status}</td>
	<td class="ys1" align="right">统计状态</td>
	<td class="ys2">{isstat}</td>
</tr>
</tbody>
</table>
<b><br /></b>

<div><b>联系信息</b></div>
<table width="100%" bordercolor="#000000" border="0" class="ke-zeroborder">
<tbody>
<tr>
	<td height="34" width="15%" align="right" class="ys1">联系人</td>
	<td class="ys2" width="35%">{linkname}</td>
	<td align="right" class="ys1" width="15%">联系电话</td>
	<td class="ys2" width="35%">{tel}</td>
</tr>
<tr>
	<td height="34" align="right" class="ys1">邮箱地址</td>
	<td class="ys2">{email}</td>
	<td class="ys1" align="right">交通路线</td>
	<td class="ys2">{routeline}</td>
</tr>
<tr>
	<td height="34" align="right" class="ys1">详细地址</td>
	<td class="ys2" colspan="3">{address}</td>
</tr>
</tbody>
</table>
<b><br /></b>

<div><b>财务信息</b></div>
<table width="100%" bordercolor="#000000" border="0" class="ke-zeroborder">
<tbody>
<tr>
	<td height="34" width="15%" align="right" class="ys1">开票地址电话</td>
	<td class="ys2" colspan="3">{kpdzdh}</td>
</tr>
<tr>
	<td height="34" align="right" class="ys1">开户银行</td>
	<td class="ys2" colspan="3">{openbank}</td>
</tr>
<tr>
	<td height="34" align="right" class="ys1">银行卡号</td>
	<td class="ys2" width="35%">{cardid}</td>
	<td class="ys1" align="right">识别ID</td>
	<td class="ys2" width="35%">{shibieid}</td>
</tr>
</tbody>
</table>
<b><br /></b>

<div><b>其他信息</b></div>
<table width="100%" bordercolor="#000000" border="0" class="ke-zeroborder">
<tbody>
<tr>
	<td height="34" width="15%" align="right" class="ys1">客户说明</td>
	<td class="ys2" colspan="3">{explain}</td>
</tr>
<tr>
	<td height="34" align="right" class="ys1">附件文件</td>
	<td class="ys2" colspan="3">{file_content}</td>
</tr>
</tbody>
</table>
</div>

<div class="ys0" tabitem="1" tabid="a" style="display:none">销售机会</div>
<div class="ys0" tabitem="2" tabid="a" style="display:none">客户合同</div>
<div class="ys0" tabitem="3" tabid="a" style="display:none">收款单</div>
<div class="ys0" tabitem="4" tabid="a" style="display:none">付款单</div>
<div class="ys0" tabitem="5" tabid="a" style="display:none">售后单</div>
<div class="ys0" tabitem="6" tabid="a" style="display:none">销售单</div>
<div class="ys0" tabitem="7" tabid="a" style="display:none">跟进计划</div>
<div class="ys0" tabitem="8" tabid="a" style="display:none">联系人</div>
</div>

<!-- 移动端安全处理脚本 -->
<script>
// 安全的客户ID获取函数
function getSafeCustomerId() {
    try {
        // 方法1：从模板变量获取
        var templateId = '{id}';
        if (templateId && templateId !== '{id}') {
            return parseInt(templateId, 10) || 0;
        }

        // 方法2：从URL参数获取
        var urlParams = new URLSearchParams(window.location.search);
        var urlId = urlParams.get('mid') || urlParams.get('id');
        if (urlId) {
            return parseInt(urlId, 10) || 0;
        }

        // 方法3：从全局变量获取
        if (typeof window.custid !== 'undefined') {
            return parseInt(window.custid, 10) || 0;
        }

        return 0;
    } catch (error) {
        console.error('获取客户ID失败：', error);
        return 0;
    }
}

// 确保所有客户ID相关的JavaScript都是安全的
$(document).ready(function() {
    try {
        // 设置全局安全的客户ID
        window.safeCustomerId = getSafeCustomerId();

        // 检查是否为移动端
        var isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || window.innerWidth <= 768;

        if (isMobile) {
            // 如果是移动端，尝试加载移动端标签页功能
            if (typeof initMobileCustomerTabs === 'function' && window.safeCustomerId > 0) {
                initMobileCustomerTabs(window.safeCustomerId);
            }
        }
    } catch (error) {
        console.error('客户页面初始化失败：', error);
    }
});
</script>