<!-- 移动端优化的标签页容器 -->
<div id="mobileCustomerTabs" class="mobile-tabs-container">
	<!-- 标签页将通过JavaScript动态生成 -->
</div>

<!-- 原有的PC版标签页（隐藏在移动端） -->
<div class="r-tabs pc-only" tabid="a" style="display: none;">
	<div index="0" class="r-tabs-item active">
	客户资料
	</div>
	<div index="7" custid="{id}" class="r-tabs-item">
	跟进计划
	</div>
	<div index="1" custid="{id}" class="r-tabs-item">
	销售机会
	</div>
	<div index="2" custid="{id}" class="r-tabs-item">
	客户合同
	</div>
	<div index="5" custid="{id}" class="r-tabs-item">
	售后单
	</div>
	<div index="3" custid="{id}" class="r-tabs-item">
	收款单
	</div>
	<div index="4" custid="{id}" class="r-tabs-item">
	付款单
	</div>
	<div index="6" custid="{id}" class="r-tabs-item">
	销售单
	</div>
</div>

<!--移动端结构化客户详情-->
<div tabitem="0" tabid="a" class="customer-detail-content">

<div class="section-title">基本信息</div>
<table>
<tr><td>客户编号</td><td>：</td><td>{custid}</td></tr>
<tr><td>客户类型</td><td>：</td><td>{type}</td></tr>
<tr><td>客户名称</td><td>：</td><td><strong>{name}</strong></td></tr>
<tr><td>单位名称</td><td>：</td><td>{unitname}</td></tr>
<tr><td>上级单位</td><td>：</td><td>{supername}</td></tr>
<tr><td>客户状态</td><td>：</td><td>{status}</td></tr>
</table>

<div class="section-title">联系信息</div>
<table>
<tr><td>联系人</td><td>：</td><td>{linkname}</td></tr>
<tr><td>联系电话</td><td>：</td><td>{tel}</td></tr>
<tr><td>邮箱地址</td><td>：</td><td>{email}</td></tr>
<tr><td>详细地址</td><td>：</td><td>{address}</td></tr>
<tr><td>交通路线</td><td>：</td><td>{routeline}</td></tr>
</table>

<div class="section-title">财务信息</div>
<table>
<tr><td>开票地址电话</td><td>：</td><td>{kpdzdh}</td></tr>
<tr><td>开户银行</td><td>：</td><td>{openbank}</td></tr>
<tr><td>银行卡号</td><td>：</td><td>{cardid}</td></tr>
<tr><td>识别ID</td><td>：</td><td>{shibieid}</td></tr>
</table>

<div class="section-title">其他信息</div>
<table>
<tr><td>客户说明</td><td>：</td><td>{explain}</td></tr>
<tr><td>附件文件</td><td>：</td><td>{file_content}</td></tr>
</table>

</div>

<!-- 原有的PC版内容区域（隐藏在移动端） -->
<div class="ys0 pc-only" tabitem="1" tabid="a" style="display:none">销售机会</div>
<div class="ys0 pc-only" tabitem="2" tabid="a" style="display:none">客户合同</div>
<div class="ys0 pc-only" tabitem="3" tabid="a" style="display:none">收款单</div>
<div class="ys0 pc-only" tabitem="4" tabid="a" style="display:none">付款单</div>
<div class="ys0 pc-only" tabitem="5" tabid="a" style="display:none">售后单</div>
<div class="ys0 pc-only" tabitem="6" tabid="a" style="display:none">销售单</div>
<div class="ys0 pc-only" tabitem="7" tabid="a" style="display:none">跟进计划</div>

<!-- 移动端标签页初始化脚本 -->
<script>
$(document).ready(function() {
    // 检测是否为移动端
    var isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || window.innerWidth <= 768;

    if (isMobile) {
        // 隐藏PC版标签页
        $('.pc-only').hide();

        // 初始化移动端标签页
        var customerId = '{id}'; // 从模板获取客户ID
        initMobileCustomerTabs(customerId);
    } else {
        // PC端隐藏移动端容器
        $('#mobileCustomerTabs').hide();
        $('.pc-only').show();
    }
});

function initMobileCustomerTabs(customerId) {
    // 获取标签页配置
    $.ajax({
        url: 'index.php?d=we&m=component&a=getMobileTabs&category_code=customer',
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.success && response.data.length > 0) {
                createMobileCustomerTabs(response.data, customerId);
            } else {
                console.error('获取标签页配置失败：', response.message);
                // 降级到原有显示方式
                $('#mobileCustomerTabs').html('<div class="error">标签页配置加载失败</div>');
            }
        },
        error: function(xhr, status, error) {
            console.error('标签页配置请求失败：', error);
            // 降级到原有显示方式
            $('#mobileCustomerTabs').html('<div class="error">标签页配置请求失败</div>');
        }
    });
}

function createMobileCustomerTabs(tabsData, customerId) {
    var container = $('#mobileCustomerTabs');
    container.empty();

    // 创建标签页导航
    var tabNav = $('<div class="r-tabs mobile-tabs-nav">');

    // 创建内容容器
    var contentContainer = $('<div class="mobile-tabs-content">');

    tabsData.forEach(function(tab, index) {
        // 创建标签页按钮
        var tabItem = $('<div class="r-tabs-item mobile-tab-item">');
        tabItem.attr('data-index', index);

        // 添加图标
        if (tab.tab_icon) {
            tabItem.append('<i class="' + tab.tab_icon + '"></i> ');
        }
        tabItem.append(tab.tab_name);

        // 绑定点击事件
        tabItem.click(function() {
            switchMobileTab(index, tab, customerId);
        });

        tabNav.append(tabItem);

        // 创建标签页内容区域
        var tabContent = $('<div class="mobile-tab-content">');
        tabContent.attr('data-index', index);
        tabContent.html('<div class="loading">加载中...</div>');

        contentContainer.append(tabContent);
    });

    container.append(tabNav);
    container.append(contentContainer);

    // 激活第一个标签页
    switchMobileTab(0, tabsData[0], customerId);
}

function switchMobileTab(index, tab, customerId) {
    // 更新导航状态
    $('.mobile-tab-item').removeClass('active');
    $('.mobile-tab-item[data-index="' + index + '"]').addClass('active');

    // 更新内容显示
    $('.mobile-tab-content').hide();
    var currentContent = $('.mobile-tab-content[data-index="' + index + '"]');
    currentContent.show();

    // 加载标签页内容
    loadMobileTabContent(tab, customerId, currentContent);
}

function loadMobileTabContent(tab, customerId, container) {
    if (tab.content_type === 'html') {
        // 静态HTML内容，替换客户数据
        loadStaticMobileContent(tab, customerId, container);
    } else if (tab.content_type === 'ajax') {
        // AJAX动态内容
        loadAjaxMobileContent(tab, customerId, container);
    }
}

function loadStaticMobileContent(tab, customerId, container) {
    // 获取客户详细信息（从页面模板变量）
    var customerData = {
        name: '{name}',
        tel: '{tel}',
        lxr: '{linkname}',
        address: '{address}',
        khlx: '{type}',
        khly: '{laiyuan}',
        khzt: '{status}',
        optdt: '{optdt}',
        explain: '{explain}'
    };

    // 替换模板变量
    var content = tab.content_source;
    Object.keys(customerData).forEach(function(key) {
        var regex = new RegExp('\\{' + key + '\\}', 'g');
        content = content.replace(regex, customerData[key] || '');
    });

    // 处理条件显示
    if (customerData.explain && customerData.explain !== '') {
        content = content.replace(/\{if_explain\}/g, '').replace(/\{endif_explain\}/g, '');
    } else {
        content = content.replace(/\{if_explain\}[\s\S]*?\{endif_explain\}/g, '');
    }

    container.html(content);
}

function loadAjaxMobileContent(tab, customerId, container) {
    container.html('<div class="loading">加载中...</div>');

    $.ajax({
        url: tab.content_source + '&customer_id=' + customerId,
        type: 'GET',
        success: function(data) {
            container.html(data);
        },
        error: function(xhr, status, error) {
            container.html('<div class="error">加载失败：' + error + '</div>');
        }
    });
}
</script>