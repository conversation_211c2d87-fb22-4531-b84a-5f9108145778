<!-- 
移动端客户详情页面 - 动态标签页版本
创建时间：2025-01-03
功能：展示如何在现有页面中集成动态标签页功能
使用方法：将此文件重命名为 view_customer_1.html 替换原有模板
-->

<style>
/* 页面特定样式 */
.customer-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 15px;
    margin: -15px -15px 0 -15px;
}

.customer-name {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 5px;
}

.customer-info {
    font-size: 14px;
    opacity: 0.9;
}

.basic-info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-top: 15px;
}

.info-item {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 8px;
    border-left: 3px solid #007bff;
}

.info-label {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
}

.info-value {
    font-size: 14px;
    color: #333;
    font-weight: 500;
}

@media (max-width: 480px) {
    .basic-info-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<!-- 客户基本信息头部 -->
<div class="customer-header">
    <div class="customer-name">{name}</div>
    <div class="customer-info">
        <i class="fa fa-phone"></i> {tel} &nbsp;&nbsp;
        <i class="fa fa-user"></i> {lxr}
    </div>
</div>

<!-- 动态标签页容器 -->
<div class="mobile-tabs-container" id="customerTabs">
    <!-- 标签页将通过JavaScript动态生成 -->
</div>

<!-- 基本信息内容模板（用于标签页） -->
<script type="text/template" id="basicInfoTemplate">
    <div class="basic-info-grid">
        <div class="info-item">
            <div class="info-label">客户名称</div>
            <div class="info-value">{name}</div>
        </div>
        <div class="info-item">
            <div class="info-label">联系电话</div>
            <div class="info-value">{tel}</div>
        </div>
        <div class="info-item">
            <div class="info-label">联系人</div>
            <div class="info-value">{lxr}</div>
        </div>
        <div class="info-item">
            <div class="info-label">客户地址</div>
            <div class="info-value">{address}</div>
        </div>
        <div class="info-item">
            <div class="info-label">客户类型</div>
            <div class="info-value">{khlx}</div>
        </div>
        <div class="info-item">
            <div class="info-label">客户来源</div>
            <div class="info-value">{khly}</div>
        </div>
        <div class="info-item">
            <div class="info-label">客户状态</div>
            <div class="info-value">{khzt}</div>
        </div>
        <div class="info-item">
            <div class="info-label">创建时间</div>
            <div class="info-value">{optdt}</div>
        </div>
    </div>
    
    {if_explain}
    <div class="info-item" style="grid-column: 1 / -1; margin-top: 15px;">
        <div class="info-label">客户说明</div>
        <div class="info-value">{explain}</div>
    </div>
    {endif_explain}
</script>

<script>
// 页面加载完成后初始化标签页
$(document).ready(function() {
    // 确保组件脚本已加载
    if (typeof MobileTabs === 'undefined') {
        // 动态加载组件脚本
        $.getScript('webmain/we/component/mobileTabs.js', function() {
            initCustomerTabs();
        });
    } else {
        initCustomerTabs();
    }
});

function initCustomerTabs() {
    // 获取当前客户ID（从URL或全局变量获取）
    var customerId = getCustomerId();
    
    // 初始化标签页配置
    var tabsInstance = initMobileTabs({
        container: '#customerTabs',
        categoryCode: 'customer', // 客户详情分类
        relationData: {
            customer_id: customerId,
            id: customerId
        },
        defaultTab: 0,
        onTabChange: function(index, tab) {
            console.log('切换到标签页：', tab.tab_name);
            
            // 可以在这里添加特定的处理逻辑
            if (tab.tab_code === 'basic_info') {
                // 基本信息标签页的特殊处理
                processBasicInfoTab();
            }
        },
        onTabLoad: function(index, tab) {
            console.log('标签页加载完成：', tab.tab_name);
            
            // 可以在这里添加加载完成后的处理逻辑
            if (tab.tab_code === 'contact_record') {
                // 联系记录加载完成后的处理
                bindContactRecordEvents();
            }
        }
    });
    
    // 将实例保存到全局，方便其他地方调用
    window.customerTabsInstance = tabsInstance;
}

function getCustomerId() {
    // 从URL参数获取客户ID
    var urlParams = new URLSearchParams(window.location.search);
    var customerId = urlParams.get('mid') || urlParams.get('id');
    
    // 如果URL中没有，尝试从全局变量获取
    if (!customerId && typeof window.currentCustomerId !== 'undefined') {
        customerId = window.currentCustomerId;
    }
    
    // 如果还是没有，尝试从页面元素获取
    if (!customerId) {
        var customerIdElement = document.querySelector('[data-customer-id]');
        if (customerIdElement) {
            customerId = customerIdElement.getAttribute('data-customer-id');
        }
    }
    
    return customerId || 0;
}

function processBasicInfoTab() {
    // 处理基本信息标签页的特殊逻辑
    // 例如：格式化显示、添加交互功能等
    
    // 添加点击电话号码拨打功能
    $('.info-value').each(function() {
        var text = $(this).text();
        if (/^1[3-9]\d{9}$/.test(text)) {
            $(this).html('<a href="tel:' + text + '" style="color: #007bff;">' + text + '</a>');
        }
    });
}

function bindContactRecordEvents() {
    // 绑定联系记录相关事件
    $('.record-item').on('click', function() {
        // 点击记录项的处理逻辑
        var recordContent = $(this).find('.record-content').text();
        console.log('点击了联系记录：', recordContent);
    });
}

// 提供给外部调用的方法
window.refreshCustomerTabs = function() {
    if (window.customerTabsInstance) {
        window.customerTabsInstance.refreshCurrentTab();
    }
};

window.switchToTab = function(tabCode) {
    if (window.customerTabsInstance) {
        var tabs = window.customerTabsInstance.tabs;
        for (var i = 0; i < tabs.length; i++) {
            if (tabs[i].tab_code === tabCode) {
                window.customerTabsInstance.switchTab(i);
                break;
            }
        }
    }
};
</script>

<!-- 兼容性处理：如果标签页组件加载失败，显示传统内容 -->
<noscript>
    <div class="fallback-content">
        <h4>客户详情</h4>
        {contview}
    </div>
</noscript>

<!-- 加载指示器 -->
<div id="tabsLoadingIndicator" style="display: none; text-align: center; padding: 40px;">
    <i class="fa fa-spinner fa-spin"></i> 正在加载标签页...
</div>

<script>
// 显示加载指示器
$('#tabsLoadingIndicator').show();

// 监听标签页初始化完成
$(document).on('tabsInitialized', function() {
    $('#tabsLoadingIndicator').hide();
});

// 如果5秒后还没有初始化完成，隐藏加载指示器并显示错误信息
setTimeout(function() {
    if ($('#tabsLoadingIndicator').is(':visible')) {
        $('#tabsLoadingIndicator').html('<div class="error">标签页加载失败，请刷新页面重试</div>');
    }
}, 5000);
</script>
