<style>
/* 新增样式 - 优化表格布局和视觉效果 */
.electwork-table {
    width: 100%;
    border-collapse: collapse;
    margin: 15px 0;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}
.electwork-table td {
    padding: 12px 15px;
    vertical-align: top;
    border-bottom: 1px solid #f0f0f0;
}
/* 表头样式 */
.electwork-table .ys1 {
    background-color: #f5f7fa;
    font-weight: bold;
    color: #333;
    width: 50%;
    text-align: left; /* 表头文字居左 */
    padding-right: 20px;
    border-right: 8px solid #ffffff;
}

/* 数据行样式 */
.electwork-table .ys2 {
    color: #666;
    text-align: left; /* 数据行文字居左 */
    padding-right: 20px;
    border-right: 8px solid #ffffff;
}
/* 标题行样式 */
.electwork-table .zbtitle {
    background-color: #e8f0fe;
    color: #2c6ecb;
    font-size: 15px;
    text-align: left;
}
/* 服务明细区域样式 */
.electwork-table .ys0 {
    background-color: #fff;
    padding: 5px;
    border: 1px dashed #e0e0e0;
}
/* 响应式调整 */
@media (max-width: 768px) {
    .electwork-table td {
        padding: 8px 10px;
        font-size: 14px;
    }
}
</style>

<table class="electwork-table ke-zeroborder">
    <tbody>
        <tr class="autoyijianview">
            <td align="left" class="ys1">^workid^</td>
            <td align="left" class="ys1">^num^</td>
        </tr>
        <tr>
            <td class="ys2">{workid}</td>
            <td class="ys2">{num}</td>
        </tr>
        <tr>
            <td align="left" class="ys1">^prname^</td>
            <td align="left" class="ys1">^linkname^</td> 
        </tr>
        <tr>
            <td class="ys2">{prname}</td>
            <td class="ys2">{linkname}</td>
        </tr>
        <tr>
            <td colspan="2" align="left" class="ys1">^address^</td>
        </tr>
        <tr>
            <td colspan="2" class="ys2">{address}</td>
        </tr>
        <tr>
            <td align="left" class="ys1">^startdt^</td>
            <td align="left" class="ys1">^enddt^</td>
        </tr>
        <tr>
            <td class="ys2">{startdt}</td>
            <td class="ys2">{enddt}</td>    
        </tr>
        <tr>
            <td colspan="2" align="left" class="ys1">^fault^</td>
        </tr>
        <tr>
            <td colspan="2" class="ys2">{fault}</td>
        </tr>
        <tr>
            <td colspan="2" align="left" class="ys1">^process^</td>
        </tr>
        <tr>
            <td colspan="2" class="ys2">{process}</td>
        </tr>
        <tr>
            <td align="left" class="ys1">^recont^</td>
            <td align="left" class="ys1">^dist^</td>
        </tr>
        <tr>
            <td class="ys2">{recont}</td>
            <td class="ys2">{dist}</td>
        </tr>
        <tr>
            <td class="ys2 zbtitle" colspan="2">更换配件明细</td>
        </tr>
        <tr>
            <td class="ys0" colspan="2">{subdata0}</td>
        </tr>
        <tr>
            <td align="left" class="ys1">^charge^</td>
            <td align="left" class="ys1">^money^</td>
        </tr>
        <tr>
            <td class="ys2">{charge}</td>
            <td class="ys2">{money}</td>
        </tr>
        <tr>
            <td align="left" class="ys1">^imglod^</td>
            <td align="left" class="ys1">^imgnew^</td>
        </tr>
        <tr>
            <td class="ys2">{imglod}</td>
            <td class="ys2">{imgnew}</td>
        </tr>
    </tbody>
</table>