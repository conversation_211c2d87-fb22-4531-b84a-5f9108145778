<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>汕头市海风科技有限公司 - 售后服务单</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    
    <!-- 配置Tailwind自定义主题 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#2c6ecb',
                        secondary: '#e8f0fe',
                        accent: '#4CAF50',
                        neutral: {
                            100: '#f5f7fa',
                            200: '#f0f0f0',
                            300: '#e0e0e0',
                            700: '#333333',
                        }
                    },
                    fontFamily: {
                        sans: ['Inter', 'system-ui', 'sans-serif'],
                    },
                    boxShadow: {
                        'card': '0 2px 5px rgba(0,0,0,0.1)',
                    }
                }
            }
        }
    </script>
    
    <!-- 自定义工具类 -->
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .border-dashed-light {
                border-style: dashed;
                border-color: #e0e0e0;
            }
        }
        @media print {
            @page {
                size: A4 portrait;
                margin: 5mm;
            }
            body {
                background: #ffffff !important;
                padding: 0 !important;
            }
            .a4-page {
                width: 210mm !important;
                min-height: auto !important;
                box-shadow: none !important;
                margin: 0 !important;
                border: none !important;
            }
            .no-print {
                display: none !important;
            }
            .print-p-2 {
                padding: 5px !important;
            }
            .print-mb-1 {
                margin-bottom: 3px !important;
            }
            .text-sm {
                font-size: 12px !important;
            }
            .text-xs {
                font-size: 10px !important;
            }
            .bg-secondary {
                background-color: #e8f0fe !important;
                -webkit-print-color-adjust: exact;
            }
            .bg-gray-50 {
                background-color: #f9fafb !important;
                -webkit-print-color-adjust: exact;
            }
        }
        .a4-page {
            width: 210mm;
            min-height: 297mm;
            margin: 0 auto;
            background: #ffffff;
            border: 1px solid #e0e0e0;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen p-2 md:p-4 font-sans text-neutral-700">
    <div class="a4-page max-w-4xl mx-auto bg-white overflow-hidden">
        <!-- 头部信息 -->
        <header class="bg-primary text-white p-4">
            <h1 class="text-xl font-bold text-center mb-1">汕头市海风科技有限公司</h1>
            <h2 class="text-lg text-secondary font-medium text-center">售后服务单</h2>
        </header>
        
        <!-- 服务单信息 -->
        <div class="p-3">
            <div class="flex justify-between items-center mb-3">
                <div class="text-xs text-gray-500">打印日期: <span id="print-date">2025-06-22</span></div>
                <div class="font-bold text-sm">服务单号：<span id="service-number">{num}</span></div>
            </div>
            
            <!-- 基础信息表格 -->
            <div class="bg-white rounded overflow-hidden mb-3">
                <div class="bg-secondary text-primary p-2 font-medium text-sm">
                    <i class="fa fa-info-circle mr-1"></i>项目信息
                </div>
                <div class="p-2">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                        <div class="flex flex-col">
                            <span class="text-xs text-gray-500 mb-0.5">项目名称</span>
                            <span class="font-medium text-sm">{prname}</span>
                        </div>
                        <div class="flex flex-col">
                            <span class="text-xs text-gray-500 mb-0.5">项目地点</span>
                            <span class="font-medium text-sm">{address}</span>
                        </div>
                        <div class="flex flex-col">
                            <span class="text-xs text-gray-500 mb-0.5">报修联系人</span>
                            <span class="font-medium text-sm">{linkname}</span>
                        </div>
                        <div class="flex flex-col">
                            <span class="text-xs text-gray-500 mb-0.5">报修时间</span>
                            <span class="font-medium text-sm">{startdt}</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 故障现象 -->
            <div class="bg-white rounded overflow-hidden mb-3">
                <div class="bg-secondary text-primary p-2 font-medium text-sm">
                    <i class="fa fa-exclamation-triangle mr-1"></i>故障现象
                </div>
                <div class="p-2">
                    <p class="font-medium text-sm">{fault}</p>
                </div>
            </div>
            
            <!-- 处理方案 -->
            <div class="bg-white rounded overflow-hidden mb-3">
                <div class="bg-secondary text-primary p-2 font-medium text-sm">
                    <i class="fa fa-cogs mr-1"></i>处理方案
                </div>
                <div class="p-2">
                    <p class="font-medium text-sm">{process}</p>
                </div>
            </div>
            
            <!-- 处理信息 -->
            <div class="bg-white rounded overflow-hidden mb-3">
                <div class="bg-secondary text-primary p-2 font-medium text-sm">
                    <i class="fa fa-clock-o mr-1"></i>处理信息
                </div>
                <div class="p-2">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                        <div class="flex flex-col">
                            <span class="text-xs text-gray-500 mb-0.5">处理时长</span>
                            <span class="font-medium text-sm">{recont}</span>
                        </div>
                        <div class="flex flex-col">
                            <span class="text-xs text-gray-500 mb-0.5">服务工程师</span>
                            <span class="font-medium text-sm">{dist}</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 服务明细 -->
            <div class="bg-white rounded overflow-hidden mb-3">
                <div class="bg-secondary text-primary p-2 font-medium text-sm">
                    <i class="fa fa-list-alt mr-1"></i>服务明细
                </div>
                <div class="p-2 bg-gray-50 border border-dashed-light rounded">
                    <p class="font-medium text-sm">{subdata0}</p>
                </div>
            </div>
            
            <!-- 收费信息 -->
            <div class="bg-white rounded overflow-hidden mb-3">
                <div class="bg-secondary text-primary p-2 font-medium text-sm">
                    <i class="fa fa-money mr-1"></i>收费信息
                </div>
                <div class="p-2">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                        <div class="flex flex-col">
                            <span class="text-xs text-gray-500 mb-0.5">收费金额</span>
                            <span class="font-medium text-lg text-primary">¥{money}</span>
                        </div>
                        <div class="flex flex-col">
                            <span class="text-xs text-gray-500 mb-0.5">大写</span>
                            <span class="font-medium text-sm">{moneyupper}</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 图片对比 -->
            <div class="bg-white rounded overflow-hidden mb-3">
                <div class="bg-secondary text-primary p-2 font-medium text-sm">
                    <i class="fa fa-image mr-1"></i>前后对比图
                </div>
                <div class="p-2">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <div class="bg-gray-50 rounded p-1 text-center text-xs">
                            <div class="aspect-video bg-gray-100 rounded overflow-hidden flex items-center justify-center">
                                {imglod}
                            </div>
                        </div>
                        <div class="bg-gray-50 rounded p-1 text-center text-xs">
                            <div class="aspect-video bg-gray-100 rounded overflow-hidden flex items-center justify-center">
                                {imgnew}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 签名 -->
            <div class="bg-white rounded overflow-hidden mb-3">
                <div class="bg-secondary text-primary p-2 font-medium text-sm">
                    <i class="fa fa-signature mr-1"></i>客户签名
                </div>
                <div class="p-2">
                    <div class="flex flex-col">{autograph}
                    </div>
                </div>
            </div>
            
            <!-- 底部信息 -->
            <div class="bg-gray-100 p-2 rounded text-xs">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <div class="font-medium mb-1 md:mb-0">汕头市海风科技有限公司</div>
                    <div class="text-gray-600">
                        <i class="fa fa-phone mr-1"></i>服务投诉电话：18923906887
                    </div>
                </div>
            </div>
        </div>
    </div>

    
    <!-- JavaScript -->
    <script>
        // 设置当前日期
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date();
            const year = today.getFullYear();
            const month = String(today.getMonth() + 1).padStart(2, '0');
            const day = String(today.getDate()).padStart(2, '0');
            document.getElementById('print-date').textContent = `${year}-${month}-${day}`;
        });
    </script>
</body>
</html>