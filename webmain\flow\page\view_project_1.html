<!-- 项目基本信息 -->
<table width="100%" border="0" class="ke-zeroborder">
    <tbody>
    <tr class="autoyijianview">
        <td height="34" width="15%" align="right" class="ys1">^num^</td>
        <td width="35%" class="ys2">{num}</td>
        <td height="34" width="15%" align="right" class="ys1">^type^</td>
        <td width="35%" class="ys2">{type}</td>
    </tr>
    <tr>
        <td height="34" width="15%" align="right" class="ys1">^title^</td>
        <td width="35%" class="ys2">{title}</td>
        <td height="34" width="15%" align="right" class="ys1">^fuze^</td>
        <td width="35%" class="ys2">{fuze}</td>
    </tr>
    <tr>
        <td height="34" align="right" class="ys1">^address^</td>
        <td colspan="3" class="ys2">{address}</td>
    </tr>
    </tbody>
</table>

<!-- 移动端选项卡导航 -->
<div class="r-tabs" tabid="a" style="margin-top:10px;">
<div index="0" class="r-tabs-item active">项目详情</div>
<div index="1" class="r-tabs-item">项目联系人</div>
<div index="2" class="r-tabs-item">项目远程</div>
<div index="3" class="r-tabs-item">^prtswd^</div>
</div>

<!-- 选项卡内容区域 -->
<div tabitem="0" tabid="a" style="border:var(--border);border-top:0px;padding:10px;background:white;">
{contview}
</div>

<div tabitem="1" tabid="a" style="border:var(--border);border-top:0px;padding:10px;background:white;display:none;">
{subcontview_1}
</div>

<div tabitem="2" tabid="a" style="border:var(--border);border-top:0px;padding:10px;background:white;display:none;">
{subcontview_2}
</div>

<div tabitem="3" tabid="a" style="border:var(--border);border-top:0px;padding:10px;background:white;display:none;">
{subcontview_3}
</div>