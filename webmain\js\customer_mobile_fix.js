/**
 * 客户移动端JavaScript修复脚本
 * 创建时间：2025-01-03
 * 用途：修复客户详情页面中的JavaScript语法错误，特别是八进制数字问题
 */

// 全局安全的客户ID获取函数
window.getSafeCustomerId = function() {
    try {
        // 方法1：从URL参数获取（最可靠）
        var urlParams = new URLSearchParams(window.location.search);
        var urlId = urlParams.get('mid') || urlParams.get('id') || urlParams.get('custid');
        if (urlId) {
            var numId = parseInt(urlId, 10);
            if (!isNaN(numId) && numId > 0) {
                return numId;
            }
        }
        
        // 方法2：从全局变量获取
        if (typeof window.custid !== 'undefined' && window.custid) {
            var numId = parseInt(window.custid, 10);
            if (!isNaN(numId) && numId > 0) {
                return numId;
            }
        }
        
        // 方法3：从页面元素获取
        var custidInput = document.querySelector('input[name="custid"]');
        if (custidInput && custidInput.value) {
            var numId = parseInt(custidInput.value, 10);
            if (!isNaN(numId) && numId > 0) {
                return numId;
            }
        }
        
        // 方法4：从页面属性获取
        var custidAttr = document.querySelector('[custid]');
        if (custidAttr) {
            var attrValue = custidAttr.getAttribute('custid');
            if (attrValue && attrValue !== '{id}') {
                var numId = parseInt(attrValue, 10);
                if (!isNaN(numId) && numId > 0) {
                    return numId;
                }
            }
        }
        
        return 0;
    } catch (error) {
        console.error('获取客户ID失败：', error);
        return 0;
    }
};

// 安全的parseInt函数，避免八进制问题
window.safeParseInt = function(value, defaultValue) {
    try {
        if (typeof value === 'string') {
            return parseInt(value, 10) || (defaultValue || 0);
        } else if (typeof value === 'number') {
            return value;
        } else {
            return defaultValue || 0;
        }
    } catch (error) {
        console.error('数字解析失败：', error);
        return defaultValue || 0;
    }
};

// 修复现有的全局变量
$(document).ready(function() {
    try {
        // 设置安全的客户ID
        window.safeCustomerId = window.getSafeCustomerId();
        
        // 修复可能存在的全局变量
        if (typeof window.custid === 'string' && window.custid.match(/^0\d+$/)) {
            // 如果custid是八进制格式的字符串，转换为安全的数字
            window.custid = window.safeParseInt(window.custid);
        }
        
        console.log('客户ID修复完成，安全客户ID：', window.safeCustomerId);
        
    } catch (error) {
        console.error('客户ID修复失败：', error);
    }
});

// 重写可能有问题的函数
window.initMobileCustomerTabsSafe = function(customerId) {
    try {
        // 确保客户ID是安全的数字
        var safeId = window.safeParseInt(customerId);
        
        if (safeId <= 0) {
            safeId = window.getSafeCustomerId();
        }
        
        if (safeId > 0) {
            // 如果原始的initMobileCustomerTabs函数存在，调用它
            if (typeof window.initMobileCustomerTabs === 'function') {
                window.initMobileCustomerTabs(safeId);
            } else {
                // 否则加载标签页配置
                loadMobileTabsConfig(safeId);
            }
        } else {
            console.warn('无法获取有效的客户ID');
            $('#mobileCustomerTabs').html('<div class="error">客户ID获取失败</div>');
        }
    } catch (error) {
        console.error('移动端标签页初始化失败：', error);
        $('#mobileCustomerTabs').html('<div class="error">标签页初始化失败</div>');
    }
};

// 加载移动端标签页配置
function loadMobileTabsConfig(customerId) {
    try {
        $.ajax({
            url: 'index.php?d=we&m=component&a=getMobileTabs&category_code=customer',
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.success && response.data.length > 0) {
                    createMobileCustomerTabsSafe(response.data, customerId);
                } else {
                    console.error('获取标签页配置失败：', response.message);
                    $('#mobileCustomerTabs').html('<div class="error">标签页配置加载失败</div>');
                }
            },
            error: function(xhr, status, error) {
                console.error('标签页配置请求失败：', error);
                $('#mobileCustomerTabs').html('<div class="error">标签页配置请求失败</div>');
            }
        });
    } catch (error) {
        console.error('加载标签页配置失败：', error);
        $('#mobileCustomerTabs').html('<div class="error">配置加载失败</div>');
    }
}

// 安全的标签页创建函数
function createMobileCustomerTabsSafe(tabsData, customerId) {
    try {
        var container = $('#mobileCustomerTabs');
        container.empty();

        // 创建标签页导航
        var tabNav = $('<div class="r-tabs mobile-tabs-nav">');

        // 创建内容容器
        var contentContainer = $('<div class="mobile-tabs-content">');

        tabsData.forEach(function(tab, index) {
            // 创建标签页按钮
            var tabItem = $('<div class="r-tabs-item mobile-tab-item">');
            tabItem.attr('data-index', index);

            // 添加图标
            if (tab.tab_icon) {
                tabItem.html('<i class="' + tab.tab_icon + '"></i> ' + tab.tab_name);
            } else {
                tabItem.text(tab.tab_name);
            }

            // 绑定点击事件
            tabItem.click(function() {
                switchMobileTabSafe(index, tab, customerId);
            });

            tabNav.append(tabItem);

            // 创建标签页内容区域
            var tabContent = $('<div class="mobile-tab-content">');
            tabContent.attr('data-index', index);
            tabContent.html('<div class="loading">加载中...</div>');

            contentContainer.append(tabContent);
        });

        container.append(tabNav);
        container.append(contentContainer);

        // 激活第一个标签页
        switchMobileTabSafe(0, tabsData[0], customerId);
        
        console.log('移动端标签页创建成功');
    } catch (error) {
        console.error('创建移动端标签页失败：', error);
        $('#mobileCustomerTabs').html('<div class="error">标签页创建失败</div>');
    }
}

// 安全的标签页切换函数
function switchMobileTabSafe(index, tab, customerId) {
    try {
        // 更新导航状态
        $('.mobile-tab-item').removeClass('active');
        $('.mobile-tab-item[data-index="' + index + '"]').addClass('active');

        // 更新内容显示
        $('.mobile-tab-content').hide();
        var currentContent = $('.mobile-tab-content[data-index="' + index + '"]');
        currentContent.show();

        // 加载标签页内容
        loadMobileTabContentSafe(tab, customerId, currentContent);
    } catch (error) {
        console.error('切换标签页失败：', error);
    }
}

// 安全的标签页内容加载函数
function loadMobileTabContentSafe(tab, customerId, container) {
    try {
        if (tab.content_type === 'html') {
            // 静态HTML内容
            container.html(tab.content_source || '<div class="no-data">暂无内容</div>');
        } else if (tab.content_type === 'ajax') {
            // AJAX动态内容
            container.html('<div class="loading">正在加载...</div>');

            $.ajax({
                url: tab.content_source + '&customer_id=' + customerId,
                type: 'GET',
                success: function(data) {
                    container.html(data);
                },
                error: function(xhr, status, error) {
                    container.html('<div class="error">加载失败：' + error + '</div>');
                }
            });
        }
    } catch (error) {
        console.error('加载标签页内容失败：', error);
        container.html('<div class="error">内容加载失败</div>');
    }
}

// 自动初始化
$(document).ready(function() {
    try {
        // 检测是否为移动端
        var isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || window.innerWidth <= 768;

        if (isMobile) {
            // 延迟初始化，确保页面完全加载
            setTimeout(function() {
                var customerId = window.getSafeCustomerId();
                if (customerId > 0) {
                    window.initMobileCustomerTabsSafe(customerId);
                }
            }, 1000);
        }
    } catch (error) {
        console.error('自动初始化失败：', error);
    }
});

console.log('客户移动端修复脚本加载完成');
