<?php
class flow_contactsClassModel extends flowModel
{
	public $statearr;
	
	public function initModel()
	{
		$this->statearr = c('array')->strtoarray('停用|#888888,启用|green');
	}
	
	//高级搜索下
	public function flowsearchfields()
	{
		$arr[] = array('name'=>'所属人...','fields'=>'uid');
		$arr[] = array('name'=>'创建人...','fields'=>'createid');
		$arr[] = array('name'=>'姓名...','fields'=>'name');
		$arr[] = array('name'=>'手机号...','fields'=>'mobile');
		return $arr;
	}
	
	public function flowrsreplace($rs, $lx=0)
	{
		if(isset($rs['status'])){
			if($rs['status']==0)$rs['ishui'] = 1;
			$zt = $this->statearr[$rs['status']];
			$rs['statuss'] = $rs['status'];
			$rs['status'] = '<font color="'.$zt[1].'">'.$zt[0].'</font>';
		}
		
		//详情时，移动端
		if($lx==1 && $this->rock->ismobile()){
			if(!isempt($rs['mobile']))$rs['mobile']='<a onclick="return callPhone(this)" href="tel:'.$rs['mobile'].'">'.$rs['mobile'].'</a>';
			if(!isempt($rs['tel']))$rs['tel']='<a onclick="return callPhone(this)" href="tel:'.$rs['tel'].'">'.$rs['tel'].'</a>';
		}
		
		return $rs;
	}
	
	/**
	 * 流程模块条件处理 - 处理客户关联查询
	 * $uid: 用户ID
	 * $lx: 流程模块条件编号(atype值)
	 */
	public function flowbillwhere($uid, $lx)
	{
		$where = '';
		
		// 从POST或GET参数获取custid
		$custid = (int)$this->rock->get('custid', 0);
		if($custid == 0) {
			$custid = (int)$this->rock->post('custid', 0);
		}
		
		// 如果有custid参数，说明是从客户模块调用的联系人查询
		if($custid > 0) {
			// 使用[Q]前缀来正确引用带前缀的表名
			// rel_type: 1=客户联系人, 2=项目联系人
			$where = " and `id` in (select contact_id from [Q]custcontrel where customer_id=$custid and rel_type in (1,2))";
		}
		
		return $where;
	}
	/**
	 * 流程删除前的处理 - 删除联系人关联数据
	 * 这个方法会在flow.php的deletebill方法中被自动调用
	 */
	public function flowdeletebillbefore($sm)
	{
		$id = $this->id;
		if($id > 0) {
			// 清理custcontrel所有类型关联
            // rel_type: 1=客户关联, 2=客户项目关联
			m('custcontrel')->delete("contact_id=$id AND rel_type in (1,2)");
		}
		return '';
	}
}
?>