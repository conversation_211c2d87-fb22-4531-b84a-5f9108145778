<?php
class flow_electworkClassModel extends flowModel
{
    public $minwidth	= 600;//子表最小宽
	public $goodsobj,$workobj,$crmobj;
	
	public function initModel()
	{
		$this->goodsobj 	= m('goods');
		$this->workobj 	= m('work');
	}
	//审核完成处理,是否直接出入库
	protected function flowcheckfinsh($zt){
		if($zt==1){
			// 检查子表是否有数据，如果没有数据则不执行出库操作
			$subData = m('goodn')->getall('`mid`='.$this->id);
			if(empty($subData)){
				// 子表无数据，不需要出库，直接更新状态为已出库
				$this->update(array('state'=>1), $this->id);
				return;
			}
			
			// 使用新的出库逻辑：从物品入库的仓库进行出库
			$barr = $this->electworkChukuoptFromStockDepots();
			if(!$barr['success']) {
				m('log')->addlogs('电子服务单出库', $this->modename.'('.$this->id.'):'.$barr['msg'], 2);
			}
		}
	}
	
	/**
	 * 电子服务单专用出库方法，只从出库操作人员对应的仓库进行出库
	 * 如果出库操作人员仓库库存不足，则报错不允许出库
	 */
	private function electworkChukuoptFromStockDepots()
	{
		// 获取电子服务单号
		$electworkNum = $this->rs['num'] ? $this->rs['num'] : $this->sericnum;
		$explain = "电子服务单出库(单号：{$electworkNum})";
		
		// 检查单据状态
		$mrs = m('goodm')->getone("`id`='".$this->id."' and `status`=1");
		if(!$mrs) return returnerror('该单据还未审核完成，不能出入库操作');
		
		$comid = $mrs['comid'];
		$mtype = (int)$mrs['type'];
		$typv = (int)$mrs['type'];
		
		$typa = explode(',', '1,0,1,0,0,0,1');
		$kina = explode(',', '0,0,1,3,1,4,2');
		
		if(!isset($typa[$typv]) || !isset($kina[$typv])) return returnerror('未设置出入库类型');
		$type = $typa[$typv];
		$kind = $kina[$typv];
		
		// 获取出库操作人员对应的仓库ID
		$optUserId = (int)$this->adminid; // 当前操作人员ID
		$userInfo = m('admin')->getone($optUserId, 'num,uname');
		$userNum = $userInfo ? $userInfo['num'] : '';
		$userName = $userInfo ? $userInfo['uname'] : '未知用户';
		
		if(empty($userNum)) {
			return returnerror("出库操作人员【{$userName}】没有工号，无法确定出库仓库。请联系管理员为该用户设置工号。");
		}
		
		$depotInfo = m('godepot')->getone("`depotnum`='$userNum'", 'id,depotname');
		if(!$depotInfo) {
			return returnerror("出库操作人员【{$userName}】（工号：{$userNum}）没有对应的仓库，无法出库。请联系管理员创建工号为【{$userNum}】的仓库。");
		}
		
		$userDepotId = (int)$depotInfo['id'];
		$userDepotName = $depotInfo['depotname'];
		
		$ndbs = m('goodn');
		
		// 读取需要出库的子表数据
		$arwos = $ndbs->getall('`mid`='.$this->id.' and `couns`<`count`');
		if(!$arwos) {
			// 检查是否确实有子表数据
			$subData = $ndbs->getall('`mid`='.$this->id);
			if(empty($subData)) {
				// 子表无数据，无需出库，直接更新状态为已出库
				$this->update(array('state'=>1), $this->id);
				return returnsuccess('无配件更换，无需出库');
			} else {
				// 有子表数据但无可出库项目，也更新状态为已出库
				$this->update(array('state'=>1), $this->id);
				return returnsuccess('配件无需出库');
			}
		}
		
		$aid = '0';
		$totalProcessed = 0;
		
		// 对每个需要出库的物品，检查出库操作人员仓库的库存
		foreach($arwos as $k1=>$rs1){
			$count = floatval($rs1['count']) - floatval($rs1['couns']);
			if($count<=0) continue;
			
			$goodsId = (int)$rs1['aid'];
			
			// 查询该物品在出库操作人员仓库的库存
			$stockRecords = $this->db->getall("
				SELECT SUM(`count`) as stock 
				FROM `[Q]goodss` 
				WHERE `aid`='$goodsId' AND `depotid`='$userDepotId' AND `status`=1
			");
			
			$availableStock = ($stockRecords && count($stockRecords) > 0) ? floatval($stockRecords[0]['stock']) : 0;
			
			// 检查库存是否足够
			if($count > $availableStock) {
				// 获取物品名称用于错误提示
				$goodsInfo = m('goods')->getone("`id`='$goodsId'", 'name,xinghao');
				$goodsName = $goodsInfo ? $goodsInfo['name'] : "物品ID:$goodsId";
				if($goodsInfo && !isempt($goodsInfo['xinghao'])) {
					$goodsName .= '(' . $goodsInfo['xinghao'] . ')';
				}
				
				// 提供更详细的错误信息和解决建议
				$errorMsg = "物品「{$goodsName}」需要出库{$count}个，但出库操作人员【{$userName}】的仓库「{$userDepotName}」只有{$availableStock}个库存，库存不足无法出库。\n\n";
				$errorMsg .= "解决方案：\n";
				$errorMsg .= "1. 请联系仓库管理员为仓库「{$userDepotName}」补充该物品的库存\n";
				$errorMsg .= "2. 或者联系管理员将该物品从其他仓库调拨到「{$userDepotName}」\n";
				$errorMsg .= "3. 或者让有该物品库存的人员来进行出库操作";
				
				return returnerror($errorMsg);
			}
			
			// 准备出库记录数据
			$arr = array();
			$arr['applydt'] = $this->rock->date;
			$arr['type'] = $type;
			$arr['kind'] = $kind;
			$arr['depotid'] = $userDepotId;
			$arr['explain'] = $explain;
			$arr['uid'] = $this->adminid;
			$arr['optid'] = $this->adminid;
			$arr['optdt'] = $this->rock->now;
			$arr['comid'] = $comid;
			$arr['optname'] = $this->adminname;
			$arr['status'] = 1;
			$arr['mid'] = $this->id;
			$arr['aid'] = $goodsId;
			$arr['count'] = ($type==1) ? (0 - $count) : $count; // 出库为负数
			
			// 创建出库记录
			$ussid = $this->db->record('[Q]goodss', $arr);
			
			if($ussid){
				$totalProcessed++;
				$aid .= ','.$goodsId.'';
				
				// 更新子表的出库数量
				$newCouns = floatval($rs1['couns']) + $count;
				$ndbs->update("`couns`='$newCouns'", $rs1['id']);
			}
		}
		
		// 如果有物品被处理，更新库存和主表状态
		if($totalProcessed > 0) {
			if($aid!='0') {
				$aid = substr($aid, 1); // 去掉开头的逗号
				m('goods')->setstock($aid);
			}
			m('goods')->upstatem($this->id);
			return returnsuccess("出库完成，共处理{$totalProcessed}个物品，全部从出库操作人员【{$userName}】的仓库「{$userDepotName}」出库");
		} else {
			return returnerror('没有可出库的物品');
		}
	}
	


	
	// 提交后处理
	protected function flowsubmit($na, $sm)
	{
		// 获取当前服务单的工单ID
		$workid = (int)$this->rs['workid'];
		$num = '';
		
		// 如果关联了工单，从工单获取编号
		if($workid > 0) {
			$workrs = $this->workobj->getone($workid, '`num`');
			if($workrs && !empty($workrs['num'])) {
				$num = $workrs['num'];
			}
		}
		
		// 如果没有从工单获取到编号，则使用系统自动生成的编号
		if(empty($num)) {
			$num = $this->sericnum;
		}
		

		
		// 设置单据编号、初始出库状态和出库类型
		$this->update(array('num'=>$num, 'state'=>0),$this->id); // state=0表示待出库状态，type=1表示出库类型
		m('custfina')->update("`htnum`='$num'", "`htid`='".$this->id."'");
		
		// 计算处理时长并保存到字段 recont（格式：X天Y小时Z分）
		$startdt = arrvalue($this->rs,'startdt');
		$enddt   = arrvalue($this->rs,'enddt');
		if(!isempt($startdt) && !isempt($enddt)){
			$recont = $this->getDurationStr($startdt, $enddt);
			$this->update(array('recont'=>$recont), $this->id);
		}
	}
	

	
	/**
	 * 根据开始、结束时间计算时长，返回"X天Y小时Z分"
	 * @param string $start 开始时间
	 * @param string $end   结束时间
	 * @return string
	 */
	private function getDurationStr($start, $end){
		$ts = strtotime($end) - strtotime($start);
		if($ts<=0)return '';
		$day = floor($ts/86400);
		$hour= floor(($ts%86400)/3600);
		$min = floor(($ts%3600)/60);
		$str='';
		if($day>0)$str.=$day.'天';
		if($hour>0)$str.=$hour.'小时';
		if($min>0)$str.=$min.'分';
		return $str;
	}
	
    //子表数据替换处理
	protected function flowsubdata($rows, $lx=0){
		$db = m('goods');
		foreach($rows as $k=>$rs){
			$one = $db->getone($rs['aid']);
			if($one){
				$name = $one['name'];
				if(!isempt($one['xinghao']))$name.='('.$one['xinghao'].')';
				if($lx==1)$rows[$k]['aid'] = $name; //1展示时
				$rows[$k]['temp_aid'] = $name;
			}
		}
		return $rows;
	}
	
	/**
	 * 重写子表数据显示方法，当无数据时显示"未更换配件"
	 * @param int $xu 子表序号
	 * @param array $rows 子表数据
	 * @param int $lx 显示类型，0=PC，1=移动
	 * @return string
	 */
	public function getsubdata($xu, $rows, $lx=0)
	{
		// 无论status的值如何，子表无数据时都显示"未更换配件"
		if(empty($rows)){
			return '<div style="text-align:center;padding:10px;color:#666;border:1px solid #e0e0e0;background:#f9f9f9;">未更换配件</div>';
		}
		
		// 如果有数据，调用父类的方法处理
		$iscz			= 0;
		$iszb			= $xu+1;
		$fields			= 'subdata'.$xu.'';
		$subrows 		= $this->db->getrows('[Q]flow_element','`mid`='.$this->modeid.' and `iszb`='.$iszb.' and `iszs`=1','`fields`,`name`,`isalign`','`sort`');
		$cont 			= '';
		if($this->db->count > 0){
			$iscz		= 1;
			$headstr	= '@xuhaos,,center'; $colorbb = getconfig('bcolorxiang', '#cccccc');
			//if($lx==1){$headstr = '';$colorbb = 'black';}
			foreach($subrows as $k=>$rs){
				$headstr.='@'.$rs['fields'].','.$rs['name'].'';
				if($rs['isalign']=='1')$headstr.=',left';
				if($rs['isalign']=='2')$headstr.=',right';
			}
			foreach($rows as $k=>$rs)$rows[$k]['xuhaos'] = $k+1;
			$slex 		= ($lx==0) ? 'noborder':'';
			if($this->subsubdatastyle!='')$slex = $this->subsubdatastyle;
			$cont 	 	= c('html')->createrows($rows, substr($headstr,1), $colorbb, $slex);
		}
		return $cont;
	}
    //作废或删除时
	protected function flowzuofeibill($sm)
	{
		$this->workobj->update('`electid`=0', "`electid`='".$this->id."'");//工单取消关联服务单
		
		$this->update('`workid`=0', $this->id);//取消关联工单
		
		//删除关联收付款单 - 同时删除新格式和旧格式的记录
		$this->deletebilljoin('custfina',"`htid`='".$this->id."'", $sm); // 新格式
		$this->deletebilljoin('custfina',"`htid`='-electwork_".$this->id."'", $sm); // 旧格式
		
		//删除出库详情，清除已出库记录
		// 获取要删除的出库记录中涉及的物品ID，用于重新计算库存
		$goodssRecords = m('goodss')->getall("`mid`='$this->id'", 'aid');
		$affectedGoods = array();
		if($goodssRecords) {
			foreach($goodssRecords as $record) {
				if($record['aid'] > 0) {
					$affectedGoods[] = $record['aid'];
				}
			}
		}
		
		// 删除所有相关的出库记录
		m('goodss')->delete("`mid`='$this->id'");
		
		// 重新计算受影响物品的库存
		if(!empty($affectedGoods)) {
			$affectedGoods = array_unique($affectedGoods); // 去重
			$aidStr = implode(',', $affectedGoods);
			m('goods')->setstock($aidStr); // 重新计算库存
		}
		
		// 重置电子服务单的出库状态
		$this->update(array('state'=>0), $this->id); // 重置为待出库状态
	}
	

   //$lx,0默认,1详情展示，2列表显示
	/**
	 * 流程数据替换处理
	 * @param array $rs 原始数据记录
	 * @param int $lx 显示类型，0默认,1详情展示，2列表显示
	 * @return array 处理后的数据记录
	 */
	public function flowrsreplace($rs, $lx=0)
	{
		// 处理出库状态
		$rs['states']= $rs['state']; // 原始状态值
		$rs['state'] = $this->goodsobj->crkstate($rs['state'],1); // 使用goodsobj的crkstate方法格式化出库状态，1表示出库类型
		
		// 读取物品列表，仅在列表显示时（$lx=2）进行
		if($lx==2){
			$rs['wupinlist'] = $this->goodsobj->getgoodninfo($rs['id'], 1); // 第二个参数1表示返回字符串格式
		}
		
		$workid = (int)$rs['workid']; // 获取关联的工单ID并转换为整数类型
		
		// 设置工单ID相关信息
		if($workid>0){
			// 获取工单编号用于显示
			$workrs = $this->workobj->getone($rs['workid'], '`num`');
			$worknum = $workrs && !empty($workrs['num']) ? $workrs['num'] : $workid;
			
			// 如果是详情展示（$lx=1），则将工单ID格式化为可点击的链接，在新窗口打开
			if($lx==1){
				$workid = '<a href="'.$this->getxiangurl('work',$rs['workid'],'auto').'" target="_blank">'.$worknum.'</a>';
			}else{
				$workid = $worknum;
			}
		}else{
			// 如果工单ID为0，表示无关联，显示"无关联"文本
			$workid = '<font color=#aaaaaa>无关联</font>';
		}
		
		// 处理收款状态
		$dsmoney = ''; // 初始化收款状态显示文本
		// 查询关联的收款记录，同时查询新格式、旧格式和E/e前缀格式
		$finrows1 = $this->db->getall('select * from `[Q]custfina` where `htid`=\''.$rs['id'].'\' '); // 新格式：直接使用ID
		$finrows2 = $this->db->getall('select * from `[Q]custfina` where `htid`=\'-electwork_'.$rs['id'].'\' '); // 旧格式：-electwork_ID
		$finrows3 = $this->db->getall('select * from `[Q]custfina` where (`htid`=\'e'.$rs['id'].'\' or `htid`=\'E'.$rs['id'].'\')'); // E/e前缀格式：e123或E123
		$finrows = array_merge($finrows1, $finrows2, $finrows3); // 合并三种格式的记录
		
		$shou = 0; // 已收款金额
		$shou1 = 0; // 已创建收款单金额
		
		// 遍历收款记录，计算已收款和已创建收款单金额
		foreach($finrows as $k1=>$rs1){
			if($rs1['ispay']=='1')$shou += floatval($rs1['money']); // 如果已支付，累加到已收款金额
			$shou1 += floatval($rs1['money']); // 累加到已创建收款单金额
		}
		
		$wshou = floatval($rs['money']) - $shou; // 计算待收款金额 (总金额 - 已收款金额)
		$wshou1 = floatval($rs['money']) - $shou1; // 计算未创建收款单金额 (总金额 - 已创建收款单金额)
		
		// 处理负数情况，确保金额不为负
		if($wshou < 0) $wshou = 0;
		if($wshou1 <= 0) $wshou1 = 0;
		
		// 根据待收款金额设置收款状态显示（不在这里更新数据库状态）
		if($wshou == 0){
			$dsmoney = '<font color=green>已全部收款</font>'; // 如果待收款金额为0，显示"已全部收款"
		}else{
			$dsmoney = '待收<font color=#ff6600>'.$wshou.'</font>'; // 如果有待收款金额，显示"待收"及金额
		}
		
		$rs['workid'] = $workid; // 将处理后的工单ID赋值回结果数组
		$rs['shoukuzt'] = $dsmoney; // 将处理后的收款状态赋值回结果数组
		
		// 如果 recont 为空，实时计算用于展示
		if(isempt($rs['recont'])){
			$startdt = arrvalue($rs,'startdt');
			$enddt   = arrvalue($rs,'enddt');
			if(!isempt($startdt) && !isempt($enddt)){
				$rs['recont'] = $this->getDurationStr($startdt, $enddt);
			}
		}
		
		// 处理开票状态显示
		$receipt = isset($rs['receipt']) ? $rs['receipt'] : '0';
		switch($receipt) {
			case '1':
				$rs['receipt'] = '<font color=#ff6600>待开票</font>';
				break;
			case '3':
				$rs['receipt'] = '<font color=green>已开票</font>';
				break;
			default:
				// 其他状态保持原值或可以设置默认显示
				break;
		}
		
		return $rs; // 返回处理后的数据记录
	}

	/**
	 * 重写保存前的金额计算逻辑
	 * 当status=1时，无论子表如何计算，金额都强制为0
	 */
	protected function flowsavebefore($table, $arr, $mid, $isadd)
	{
		$result = parent::flowsavebefore($table, $arr, $mid, $isadd);
		
		// 检查status字段的值
		$status = isset($arr['status']) ? $arr['status'] : '0';
		
		if($status === '1') {
			// 保内服务单：强制设置money和charge字段为0
			if(is_array($result)) {
				if(!isset($result['rows'])) $result['rows'] = array();
				$result['rows']['money'] = '0';
				$result['rows']['charge'] = '0';
			} else {
				$result = array(
					'rows' => array(
						'money' => '0',
						'charge' => '0'
					)
				);
			}
		}
		
		return $result;
	}
	
	/**
	 * 重写子表金额自动计算方法
	 * 当status=1时，无论子表数据如何变化，总金额都保持为0
	 */
	public function flowcalcmoney($subdata = null)
	{
		// 获取当前记录的status值
		$status = '0';
		if($this->rs && isset($this->rs['status'])) {
			$status = $this->rs['status'];
		} else if(isset($_POST['status'])) {
			$status = $_POST['status'];
		}
		
		// 如果status=1，直接返回0，不进行任何计算
		if($status === '1') {
			return 0;
		}
		
		// 其他情况调用父类的计算方法
		return parent::flowcalcmoney($subdata);
	}

}