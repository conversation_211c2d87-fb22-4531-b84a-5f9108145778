<?php
/**
 * 开票申请模块Model
 * 处理开票申请相关的业务逻辑和数据显示
 */
class flow_finkaiClassModel extends flowModel
{
	/**
	 * 流程数据替换处理
	 * @param array $rs 原始数据记录
	 * @param int $lx 显示类型，0默认,1详情展示，2列表显示
	 * @return array 处理后的数据记录
	 */
	public function flowrsreplace($rs, $lx=0)
	{
		// 处理销售方名称显示
		if(isset($rs['name']) && !empty($rs['name'])){
			$companyId = $rs['name'];
			
			// 如果是数字ID，则获取对应的公司名称
			if(is_numeric($companyId) && $companyId > 0){
				$companyInfo = m('company')->getone($companyId, 'name,nameen');
				if($companyInfo){
					// 优先使用英文名称，如果没有则使用中文名称
					$displayName = !empty($companyInfo['nameen']) ? $companyInfo['nameen'] : $companyInfo['name'];
					$rs['name'] = $displayName;
					
					// 如果是详情展示，可以添加更多信息或链接
					if($lx == 1){
						// 保持简单显示，不添加链接
						$rs['name'] = $displayName;
					}
				}
			}
		}
		
		// 处理开票项目显示
		if(isset($rs['num'])){
			$numValue = $rs['num'];

			// 检查是否需要隐藏开票项目
			$shouldHideNum = false;

			// 判断当前是否为编辑模式
			$isEditMode = $this->isEditMode();

			// 只有在非编辑模式下，且流程已通过时才隐藏开票项目
			if(!$isEditMode && isset($rs['status']) && $rs['status'] == 1 && isset($rs['isturn']) && $rs['isturn'] == 1) {
				// 流程已通过且已提交，且不是编辑模式，隐藏开票项目
				$shouldHideNum = true;
			}

			if($shouldHideNum) {
				// 开票流程通过时不显示开票项目
				$rs['num'] = '<font color=#888888>已通过</font>';
			} else if($lx == 2) {
				// 仅在列表页面显示开票项目详情
				if(empty($numValue) || $numValue == '0'){
					// 没有关联项目
					$rs['num'] = '<font color=#aaaaaa>无关联</font>';
				} else {
					// 有关联项目，判断类型并生成链接
					$displayText = '';
					$linkUrl = '';

					if((int)$numValue < 0) {
						// 负数表示销售单
						$salesId = abs((int)$numValue);
						$salesInfo = $this->db->getone('[Q]goodm', '`id`='.$salesId.' AND `type`=2', 'num');
						if($salesInfo && !empty($salesInfo['num'])) {
							$displayText = $salesInfo['num'];
							$linkUrl = $this->getxiangurl('custxiao', $salesId, 'auto');
						}
					} else if((int)$numValue > 0) {
						// 正数，需要判断是合同还是电子服务单
						// 先尝试作为合同查询
						$contractInfo = m('custract')->getone((int)$numValue, 'num');
						if($contractInfo && !empty($contractInfo['num'])) {
							$displayText = $contractInfo['num'];
							$linkUrl = $this->getxiangurl('custract', (int)$numValue, 'auto');
						} else {
							// 尝试作为电子服务单查询
							$electInfo = $this->db->getone('[Q]goodm', '`id`='.(int)$numValue.' AND `type`=6', 'num');
							if($electInfo && !empty($electInfo['num'])) {
								$displayText = $electInfo['num'];
								$linkUrl = $this->getxiangurl('electwork', (int)$numValue, 'auto');
							}
						}
					}

					// 生成显示内容
					if(!empty($displayText)) {
						if(!empty($linkUrl)) {
							$rs['num'] = '<a href="'.$linkUrl.'" target="_blank">'.$displayText.'</a>';
						} else {
							$rs['num'] = $displayText;
						}
					} else {
						$rs['num'] = '<font color=#ff0000>关联错误</font>';
					}
				}
			} else if($lx == 1) {
				// 详情页面显示
				if($shouldHideNum) {
					$rs['num'] = '<font color=#888888>开票流程已通过</font>';
				} else if(!empty($numValue) && $numValue != '0') {
					// 显示简化的项目信息
					$displayText = '';
					if((int)$numValue < 0) {
						$salesId = abs((int)$numValue);
						$salesInfo = $this->db->getone('[Q]goodm', '`id`='.$salesId.' AND `type`=2', 'num');
						if($salesInfo && !empty($salesInfo['num'])) {
							$displayText = '销售单：' . $salesInfo['num'];
						}
					} else if((int)$numValue > 0) {
						$contractInfo = m('custract')->getone((int)$numValue, 'num');
						if($contractInfo && !empty($contractInfo['num'])) {
							$displayText = '合同：' . $contractInfo['num'];
						} else {
							$electInfo = $this->db->getone('[Q]goodm', '`id`='.(int)$numValue.' AND `type`=6', 'num');
							if($electInfo && !empty($electInfo['num'])) {
								$displayText = '电子服务单：' . $electInfo['num'];
							}
						}
					}
					$rs['num'] = !empty($displayText) ? $displayText : '<font color=#ff0000>关联错误</font>';
				} else {
					$rs['num'] = '<font color=#aaaaaa>无关联项目</font>';
				}
			}
		}
		
		return $rs;
	}
	
	/**
	 * 删除单据时的处理
	 * @param string $sm 删除说明
	 */
	protected function flowdeletebill($sm)
	{
		$this->resetElectworkReceiptStatus();
	}
	
	/**
	 * 作废单据时的处理
	 * @param string $sm 作废说明
	 */
	protected function flowzuofeibill($sm)
	{
		$this->resetElectworkReceiptStatus();
	}
	
	/**
	 * 判断当前是否为编辑模式
	 * @return bool
	 */
	private function isEditMode()
	{
		// 检查URL参数或请求参数来判断是否为编辑模式
		$action = isset($_GET['a']) ? $_GET['a'] : '';
		$mode = isset($_GET['mode']) ? $_GET['mode'] : '';
		$edit = isset($_GET['edit']) ? $_GET['edit'] : '';

		// 常见的编辑模式标识
		if($action == 'edit' || $action == 'update' || $mode == 'edit' || $edit == '1') {
			return true;
		}

		// 检查是否为表单编辑页面
		if(isset($_GET['num']) && isset($_GET['d']) && $_GET['d'] == 'flow') {
			$flowAction = isset($_GET['m']) ? $_GET['m'] : '';
			if($flowAction == 'finkai' && isset($_GET['a']) && $_GET['a'] == 'input') {
				return true;
			}
		}

		// 检查POST请求中的编辑标识
		if($_SERVER['REQUEST_METHOD'] == 'POST') {
			if(isset($_POST['action']) && ($_POST['action'] == 'edit' || $_POST['action'] == 'update')) {
				return true;
			}
			if(isset($_POST['id']) && !empty($_POST['id'])) {
				// 如果POST中有ID，通常表示是编辑操作
				return true;
			}
		}

		return false;
	}

	/**
	 * 流程审核完成后的处理
	 * @param int $zt 审核状态 1=通过 2=不通过
	 */
	protected function flowcheckfinsh($zt)
	{
		if($zt == 1) {
			// 开票流程通过时的处理
			$this->handleInvoiceApproved();
		}
	}

	/**
	 * 处理开票流程通过后的逻辑
	 */
	private function handleInvoiceApproved()
	{
		// 获取当前开票申请的关联项目编号
		$numValue = isset($this->rs['num']) ? $this->rs['num'] : '';

		if(!empty($numValue) && is_numeric($numValue) && (int)$numValue > 0){
			$electId = (int)$numValue;

			// 检查是否为电子服务单（type=6）
			$electInfo = $this->db->getone('[Q]goodm', '`id`='.$electId.' AND `type`=6', 'id,receipt');
			if($electInfo){
				// 更新电子服务单的receipt字段为3（表示已开票）
				m('goodm')->update(array('receipt' => '3'), $electId);
			}
		}

		// 可以在这里添加其他开票流程通过后需要执行的逻辑
		// 例如：发送通知、更新相关状态等
	}

	/**
	 * 重置电子服务单的开票状态
	 * 将关联的电子服务单的receipt字段从3改回1
	 */
	private function resetElectworkReceiptStatus()
	{
		// 获取当前开票申请的关联项目编号
		$numValue = isset($this->rs['num']) ? $this->rs['num'] : '';

		if(!empty($numValue) && is_numeric($numValue) && (int)$numValue > 0){
			$electId = (int)$numValue;

			// 检查是否为电子服务单（type=6）且当前receipt=3
			$electInfo = $this->db->getone('[Q]goodm', '`id`='.$electId.' AND `type`=6 AND `receipt`=3', 'id');
			if($electInfo){
				// 将电子服务单的receipt字段改回1（待开票状态）
				m('goodm')->update(array('receipt' => '1'), $electId);
			}
		}
	}
} 