<?php
/**
 * 移动端标签页配置模型（基于配置文件）
 * 创建时间：2025-01-03
 * 功能：不依赖数据库的标签页管理
 */

class mobileTabConfigClassModel extends Model
{
    private $config;
    private $configFile;
    
    public function __construct()
    {
        parent::__construct();
        $this->configFile = dirname(__FILE__) . '/../config/mobileTabsConfig.php';
        $this->loadConfig();
    }

    /**
     * 加载配置文件
     */
    private function loadConfig()
    {
        if (file_exists($this->configFile)) {
            $this->config = include $this->configFile;
        } else {
            $this->config = $this->getDefaultConfig();
        }
    }

    /**
     * 获取默认配置
     */
    private function getDefaultConfig()
    {
        return [
            'categories' => [],
            'tabs' => [],
            'relations' => [],
            'settings' => [
                'enable_stats' => false,
                'cache_enabled' => true,
                'default_load_method' => 'immediate',
                'max_tabs' => 10,
                'enable_permissions' => false
            ]
        ];
    }

    /**
     * 获取标签页分类列表
     * @param array $where 查询条件
     * @return array
     */
    public function getCategoryList($where = [])
    {
        $categories = [];
        $index = 0;
        
        foreach ($this->config['categories'] as $code => $category) {
            if (isset($where['status']) && $category['status'] != $where['status']) {
                continue;
            }
            
            $categories[] = [
                'id' => $index++,
                'name' => $category['name'],
                'code' => $code,
                'description' => $category['description'] ?? '',
                'sort' => $category['sort'] ?? 0,
                'status' => $category['status'] ?? 1
            ];
        }
        
        // 按排序号排序
        usort($categories, function($a, $b) {
            return $a['sort'] - $b['sort'];
        });
        
        return $categories;
    }

    /**
     * 获取指定分类的标签页配置
     * @param int $categoryId 分类ID（这里用不到）
     * @param string $categoryCode 分类代码
     * @return array
     */
    public function getTabsByCategory($categoryId = 0, $categoryCode = '')
    {
        if (empty($categoryCode)) {
            return [];
        }
        
        if (!isset($this->config['tabs'][$categoryCode])) {
            return [];
        }
        
        $tabs = [];
        $index = 0;
        
        foreach ($this->config['tabs'][$categoryCode] as $tab) {
            if ($tab['status'] != 1) {
                continue;
            }
            
            $tabs[] = array_merge($tab, [
                'id' => $index++,
                'category_code' => $categoryCode,
                'category_name' => $this->config['categories'][$categoryCode]['name'] ?? ''
            ]);
        }
        
        // 按排序号排序
        usort($tabs, function($a, $b) {
            return $a['sort'] - $b['sort'];
        });
        
        return $tabs;
    }

    /**
     * 根据关联条件获取标签页
     * @param string $relationType 关联类型
     * @param mixed $relationId 关联ID
     * @param string $relationCode 关联代码
     * @return array
     */
    public function getTabsByRelation($relationType, $relationId = null, $relationCode = '')
    {
        // 根据关联类型映射到分类代码
        if (isset($this->config['relations'][$relationType])) {
            $categoryCode = $this->config['relations'][$relationType];
            return $this->getTabsByCategory(0, $categoryCode);
        }
        
        return [];
    }

    /**
     * 获取标签页内容
     * @param int $tabId 标签页ID（这里用分类代码和标签代码）
     * @param array $params 参数
     * @param string $categoryCode 分类代码
     * @param string $tabCode 标签代码
     * @return string
     */
    public function getTabContent($tabId, $params = [], $categoryCode = '', $tabCode = '')
    {
        // 如果提供了分类代码和标签代码，直接查找
        if (!empty($categoryCode) && !empty($tabCode)) {
            $tab = $this->findTabByCode($categoryCode, $tabCode);
        } else {
            // 否则通过ID查找（需要遍历）
            $tab = $this->findTabById($tabId);
        }
        
        if (!$tab) {
            return '<div class="error">标签页不存在</div>';
        }

        switch ($tab['content_type']) {
            case 'html':
                return $this->processHtmlContent($tab['content_source'], $params);
            case 'ajax':
                return $this->processAjaxContent($tab['content_source'], $params);
            case 'iframe':
                return $this->processIframeContent($tab['content_source'], $params);
            default:
                return $tab['content_source'];
        }
    }

    /**
     * 根据代码查找标签页
     */
    private function findTabByCode($categoryCode, $tabCode)
    {
        if (!isset($this->config['tabs'][$categoryCode])) {
            return null;
        }
        
        foreach ($this->config['tabs'][$categoryCode] as $tab) {
            if ($tab['tab_code'] === $tabCode) {
                return $tab;
            }
        }
        
        return null;
    }

    /**
     * 根据ID查找标签页（遍历所有分类）
     */
    private function findTabById($tabId)
    {
        $currentId = 0;
        
        foreach ($this->config['tabs'] as $categoryCode => $tabs) {
            foreach ($tabs as $tab) {
                if ($currentId == $tabId) {
                    return $tab;
                }
                $currentId++;
            }
        }
        
        return null;
    }

    /**
     * 处理HTML内容
     * @param string $content HTML内容
     * @param array $params 参数
     * @return string
     */
    private function processHtmlContent($content, $params)
    {
        // 替换模板变量
        foreach ($params as $key => $value) {
            $content = str_replace('{' . $key . '}', $value, $content);
        }
        
        // 处理条件显示标签
        $content = $this->processConditionalTags($content, $params);
        
        return $content;
    }

    /**
     * 处理条件显示标签
     * @param string $content 内容
     * @param array $params 参数
     * @return string
     */
    private function processConditionalTags($content, $params)
    {
        // 处理 {if_字段名}...{endif_字段名} 语法
        $pattern = '/\{if_([^}]+)\}(.*?)\{endif_\1\}/s';
        
        return preg_replace_callback($pattern, function($matches) use ($params) {
            $fieldNames = explode('_', $matches[1]);
            $innerContent = $matches[2];
            
            // 检查所有字段是否都有值
            $hasValue = false;
            foreach ($fieldNames as $fieldName) {
                if (isset($params[$fieldName]) && !$this->isEmpty($params[$fieldName])) {
                    $hasValue = true;
                    break;
                }
            }
            
            return $hasValue ? $innerContent : '';
        }, $content);
    }

    /**
     * 检查值是否为空
     */
    private function isEmpty($value)
    {
        if (empty($value)) {
            return true;
        }
        
        // 去除HTML标签和空白符后检查
        $cleanValue = strip_tags($value);
        $cleanValue = trim($cleanValue);
        $cleanValue = str_replace(['&nbsp;', ' ', '-'], '', $cleanValue);
        
        return $cleanValue === '';
    }

    /**
     * 处理AJAX内容
     * @param string $source AJAX源
     * @param array $params 参数
     * @return string
     */
    private function processAjaxContent($source, $params)
    {
        // 解析AJAX源：module,action,method
        $parts = explode(',', $source);
        if (count($parts) >= 3) {
            $module = $parts[0];
            $action = $parts[1];
            $method = $parts[2];

            // 返回AJAX加载的HTML结构
            $paramsJson = json_encode($params);
            return '<div class="ajax-content" data-module="' . $module . '" data-action="' . $action . '" data-method="' . $method . '" data-params=\'' . htmlspecialchars($paramsJson) . '\'>加载中...</div>';
        }
        return '<div class="error">配置错误</div>';
    }

    /**
     * 处理iframe内容
     * @param string $url iframe URL
     * @param array $params 参数
     * @return string
     */
    private function processIframeContent($url, $params)
    {
        // 替换URL中的参数
        foreach ($params as $key => $value) {
            $url = str_replace('{' . $key . '}', urlencode($value), $url);
        }
        return '<iframe src="' . htmlspecialchars($url) . '" width="100%" height="400" frameborder="0"></iframe>';
    }

    /**
     * 保存配置到文件
     * @param array $config 配置数据
     * @return bool
     */
    public function saveConfig($config)
    {
        $configContent = "<?php\n/**\n * 移动端标签页配置文件\n * 自动生成时间：" . date('Y-m-d H:i:s') . "\n */\n\nif (!defined('HOST')) die('not access');\n\nreturn " . var_export($config, true) . ";\n?>";
        
        return file_put_contents($this->configFile, $configContent) !== false;
    }

    /**
     * 获取当前配置
     * @return array
     */
    public function getConfig()
    {
        return $this->config;
    }

    /**
     * 添加或更新分类
     * @param string $code 分类代码
     * @param array $data 分类数据
     * @return bool
     */
    public function saveCategory($code, $data)
    {
        $this->config['categories'][$code] = $data;
        return $this->saveConfig($this->config);
    }

    /**
     * 添加或更新标签页
     * @param string $categoryCode 分类代码
     * @param string $tabCode 标签代码
     * @param array $data 标签页数据
     * @return bool
     */
    public function saveTab($categoryCode, $tabCode, $data)
    {
        if (!isset($this->config['tabs'][$categoryCode])) {
            $this->config['tabs'][$categoryCode] = [];
        }
        
        // 查找现有标签页
        $found = false;
        foreach ($this->config['tabs'][$categoryCode] as $index => $tab) {
            if ($tab['tab_code'] === $tabCode) {
                $this->config['tabs'][$categoryCode][$index] = $data;
                $found = true;
                break;
            }
        }
        
        // 如果没找到，添加新标签页
        if (!$found) {
            $this->config['tabs'][$categoryCode][] = $data;
        }
        
        return $this->saveConfig($this->config);
    }

    /**
     * 删除标签页
     * @param string $categoryCode 分类代码
     * @param string $tabCode 标签代码
     * @return bool
     */
    public function deleteTab($categoryCode, $tabCode)
    {
        if (!isset($this->config['tabs'][$categoryCode])) {
            return false;
        }
        
        foreach ($this->config['tabs'][$categoryCode] as $index => $tab) {
            if ($tab['tab_code'] === $tabCode) {
                unset($this->config['tabs'][$categoryCode][$index]);
                // 重新索引数组
                $this->config['tabs'][$categoryCode] = array_values($this->config['tabs'][$categoryCode]);
                return $this->saveConfig($this->config);
            }
        }
        
        return false;
    }
}
