<?php
/**
 * 移动端标签页管理模型
 * 创建时间：2025-01-03
 * 功能：管理移动端展示页面的动态标签页
 */

class mobileTabClassModel extends Model
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 获取标签页分类列表
     * @param array $where 查询条件
     * @return array
     */
    public function getCategoryList($where = [])
    {
        $sql = "SELECT * FROM `{$this->perfix}mobile_tab_category` WHERE 1=1";
        
        if (!empty($where['status'])) {
            $sql .= " AND `status` = " . intval($where['status']);
        }
        
        if (!empty($where['comid'])) {
            $sql .= " AND `comid` = " . intval($where['comid']);
        }
        
        $sql .= " ORDER BY `sort` ASC, `id` ASC";
        
        return $this->db->getall($sql);
    }

    /**
     * 获取指定分类的标签页配置
     * @param int $categoryId 分类ID
     * @param string $categoryCode 分类代码
     * @return array
     */
    public function getTabsByCategory($categoryId = 0, $categoryCode = '')
    {
        $sql = "SELECT t.*, c.name as category_name, c.code as category_code 
                FROM `{$this->perfix}mobile_tab_config` t 
                LEFT JOIN `{$this->perfix}mobile_tab_category` c ON t.category_id = c.id 
                WHERE t.status = 1";
        
        if ($categoryId > 0) {
            $sql .= " AND t.category_id = " . intval($categoryId);
        }
        
        if (!empty($categoryCode)) {
            $sql .= " AND c.code = '" . $this->db->escape($categoryCode) . "'";
        }
        
        $sql .= " ORDER BY t.sort ASC, t.id ASC";
        
        return $this->db->getall($sql);
    }

    /**
     * 根据关联条件获取标签页
     * @param string $relationType 关联类型
     * @param mixed $relationId 关联ID
     * @param string $relationCode 关联代码
     * @return array
     */
    public function getTabsByRelation($relationType, $relationId = null, $relationCode = '')
    {
        $sql = "SELECT DISTINCT t.*, c.name as category_name 
                FROM `{$this->perfix}mobile_tab_config` t 
                LEFT JOIN `{$this->perfix}mobile_tab_category` c ON t.category_id = c.id 
                LEFT JOIN `{$this->perfix}mobile_tab_relation` r ON t.id = r.tab_id 
                WHERE t.status = 1 AND r.status = 1 
                AND r.relation_type = '" . $this->db->escape($relationType) . "'";
        
        if ($relationId !== null) {
            $sql .= " AND r.relation_id = " . intval($relationId);
        }
        
        if (!empty($relationCode)) {
            $sql .= " AND r.relation_code = '" . $this->db->escape($relationCode) . "'";
        }
        
        $sql .= " ORDER BY t.sort ASC, t.id ASC";
        
        return $this->db->getall($sql);
    }

    /**
     * 保存标签页分类
     * @param array $data 分类数据
     * @return bool|int
     */
    public function saveCategory($data)
    {
        $data['optdt'] = date('Y-m-d H:i:s');
        $data['optid'] = $this->adminid;
        $data['optname'] = $this->adminname;
        
        if (isset($data['id']) && $data['id'] > 0) {
            $id = intval($data['id']);
            unset($data['id']);
            return $this->db->update('mobile_tab_category', $data, "`id` = $id");
        } else {
            return $this->db->insert('mobile_tab_category', $data);
        }
    }

    /**
     * 保存标签页配置
     * @param array $data 标签页数据
     * @return bool|int
     */
    public function saveTabConfig($data)
    {
        $data['optdt'] = date('Y-m-d H:i:s');
        $data['optid'] = $this->adminid;
        $data['optname'] = $this->adminname;
        
        if (isset($data['id']) && $data['id'] > 0) {
            $id = intval($data['id']);
            unset($data['id']);
            return $this->db->update('mobile_tab_config', $data, "`id` = $id");
        } else {
            return $this->db->insert('mobile_tab_config', $data);
        }
    }

    /**
     * 删除标签页配置
     * @param int $id 标签页ID
     * @return bool
     */
    public function deleteTabConfig($id)
    {
        $id = intval($id);
        // 软删除，设置状态为0
        return $this->db->update('mobile_tab_config', ['status' => 0], "`id` = $id");
    }

    /**
     * 更新标签页排序
     * @param array $sortData 排序数据 [['id' => 1, 'sort' => 1], ...]
     * @return bool
     */
    public function updateTabSort($sortData)
    {
        $success = true;
        foreach ($sortData as $item) {
            $id = intval($item['id']);
            $sort = intval($item['sort']);
            $result = $this->db->update('mobile_tab_config', ['sort' => $sort], "`id` = $id");
            if (!$result) {
                $success = false;
            }
        }
        return $success;
    }

    /**
     * 获取标签页内容
     * @param int $tabId 标签页ID
     * @param array $params 参数
     * @return string
     */
    public function getTabContent($tabId, $params = [])
    {
        $tab = $this->db->getmou('mobile_tab_config', '*', "`id` = " . intval($tabId));
        if (!$tab) {
            return '标签页不存在';
        }

        switch ($tab['content_type']) {
            case 'html':
                return $this->processHtmlContent($tab['content_source'], $params);
            case 'ajax':
                return $this->processAjaxContent($tab['content_source'], $params);
            case 'iframe':
                return $this->processIframeContent($tab['content_source'], $params);
            default:
                return $tab['content_source'];
        }
    }

    /**
     * 处理HTML内容
     * @param string $content HTML内容
     * @param array $params 参数
     * @return string
     */
    private function processHtmlContent($content, $params)
    {
        // 替换模板变量
        foreach ($params as $key => $value) {
            $content = str_replace('{' . $key . '}', $value, $content);
        }
        return $content;
    }

    /**
     * 处理AJAX内容
     * @param string $source AJAX源
     * @param array $params 参数
     * @return string
     */
    private function processAjaxContent($source, $params)
    {
        // 解析AJAX源：module,action,method
        $parts = explode(',', $source);
        if (count($parts) >= 3) {
            $module = $parts[0];
            $action = $parts[1];
            $method = $parts[2];
            
            // 这里可以调用相应的模块方法获取内容
            // 返回AJAX加载的HTML结构
            return '<div class="ajax-content" data-module="' . $module . '" data-action="' . $action . '" data-method="' . $method . '">加载中...</div>';
        }
        return '配置错误';
    }

    /**
     * 处理iframe内容
     * @param string $url iframe URL
     * @param array $params 参数
     * @return string
     */
    private function processIframeContent($url, $params)
    {
        // 替换URL中的参数
        foreach ($params as $key => $value) {
            $url = str_replace('{' . $key . '}', urlencode($value), $url);
        }
        return '<iframe src="' . htmlspecialchars($url) . '" width="100%" height="400" frameborder="0"></iframe>';
    }

    /**
     * 记录标签页访问统计
     * @param int $tabId 标签页ID
     * @param int $userId 用户ID
     * @param int $duration 停留时间（秒）
     * @return bool
     */
    public function recordTabAccess($tabId, $userId, $duration = 0)
    {
        $today = date('Y-m-d');
        $existing = $this->db->getmou('mobile_tab_stats', '*', 
            "`tab_id` = " . intval($tabId) . " AND `user_id` = " . intval($userId) . " AND `date_created` = '$today'");
        
        if ($existing) {
            // 更新统计
            $data = [
                'access_count' => $existing['access_count'] + 1,
                'last_access_time' => date('Y-m-d H:i:s'),
                'total_duration' => $existing['total_duration'] + intval($duration)
            ];
            return $this->db->update('mobile_tab_stats', $data, "`id` = " . $existing['id']);
        } else {
            // 新增统计
            $data = [
                'tab_id' => intval($tabId),
                'user_id' => intval($userId),
                'access_count' => 1,
                'last_access_time' => date('Y-m-d H:i:s'),
                'total_duration' => intval($duration),
                'date_created' => $today,
                'comid' => 0
            ];
            return $this->db->insert('mobile_tab_stats', $data);
        }
    }
}
