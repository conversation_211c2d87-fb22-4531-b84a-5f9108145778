/**
 * 移动端标签页管理 JavaScript
 * 创建时间：2025-01-03
 */

var MobileTabManager = {
    // 当前页码
    categoryPage: 1,
    configPage: 1,
    
    // 初始化
    init: function() {
        this.loadCategoryTable();
        this.loadConfigTable();
        this.loadCategoryOptions();
        this.bindEvents();
    },
    
    // 绑定事件
    bindEvents: function() {
        var self = this;
        
        // 分类搜索回车事件
        $('#categorySearch').on('keypress', function(e) {
            if (e.which === 13) {
                self.searchCategory();
            }
        });
        
        // 配置搜索回车事件
        $('#configSearch').on('keypress', function(e) {
            if (e.which === 13) {
                self.searchConfig();
            }
        });
        
        // 分类筛选变化事件
        $('#categoryFilter').on('change', function() {
            self.loadConfigTable();
        });
    },
    
    // 加载分类表格
    loadCategoryTable: function(page) {
        var self = this;
        page = page || 1;
        this.categoryPage = page;
        
        var search = $('#categorySearch').val();
        
        $.ajax({
            url: 'index.php?d=system&m=mobiletab&a=getCategoryData',
            type: 'GET',
            data: {
                page: page,
                limit: 20,
                search: search
            },
            success: function(response) {
                var data = JSON.parse(response);
                self.renderCategoryTable(data.rows);
                self.renderCategoryPagination(data.total, page);
            },
            error: function() {
                alert('加载分类数据失败');
            }
        });
    },
    
    // 渲染分类表格
    renderCategoryTable: function(rows) {
        var tbody = $('#categoryTable tbody');
        tbody.empty();
        
        if (rows.length === 0) {
            tbody.append('<tr><td colspan="8" class="text-center">暂无数据</td></tr>');
            return;
        }
        
        $.each(rows, function(index, row) {
            var statusClass = row.status == 1 ? 'status-active' : 'status-inactive';
            var statusText = row.status == 1 ? '启用' : '禁用';
            
            var tr = $('<tr>').append(
                $('<td>').text(row.id),
                $('<td>').text(row.name),
                $('<td>').text(row.code),
                $('<td>').text(row.description || ''),
                $('<td>').text(row.sort),
                $('<td>').html('<span class="status-badge ' + statusClass + '">' + statusText + '</span>'),
                $('<td>').text(row.optdt || ''),
                $('<td>').html(
                    '<button class="btn btn-xs btn-primary" onclick="MobileTabManager.editCategory(' + row.id + ')">编辑</button> ' +
                    '<button class="btn btn-xs btn-danger" onclick="MobileTabManager.deleteCategory(' + row.id + ')">删除</button>'
                )
            );
            tbody.append(tr);
        });
    },
    
    // 渲染分类分页
    renderCategoryPagination: function(total, currentPage) {
        var totalPages = Math.ceil(total / 20);
        var pagination = $('#categoryPagination');
        pagination.empty();
        
        if (totalPages <= 1) return;
        
        var nav = $('<nav>').append($('<ul class="pagination">'));
        var ul = nav.find('ul');
        
        // 上一页
        if (currentPage > 1) {
            ul.append('<li><a href="javascript:void(0)" onclick="MobileTabManager.loadCategoryTable(' + (currentPage - 1) + ')">上一页</a></li>');
        }
        
        // 页码
        for (var i = 1; i <= totalPages; i++) {
            var activeClass = i === currentPage ? ' class="active"' : '';
            ul.append('<li' + activeClass + '><a href="javascript:void(0)" onclick="MobileTabManager.loadCategoryTable(' + i + ')">' + i + '</a></li>');
        }
        
        // 下一页
        if (currentPage < totalPages) {
            ul.append('<li><a href="javascript:void(0)" onclick="MobileTabManager.loadCategoryTable(' + (currentPage + 1) + ')">下一页</a></li>');
        }
        
        pagination.append(nav);
    },
    
    // 加载配置表格
    loadConfigTable: function(page) {
        var self = this;
        page = page || 1;
        this.configPage = page;
        
        var search = $('#configSearch').val();
        var categoryId = $('#categoryFilter').val();
        
        $.ajax({
            url: 'index.php?d=system&m=mobiletab&a=getConfigData',
            type: 'GET',
            data: {
                page: page,
                limit: 20,
                search: search,
                category_id: categoryId
            },
            success: function(response) {
                var data = JSON.parse(response);
                self.renderConfigTable(data.rows);
                self.renderConfigPagination(data.total, page);
            },
            error: function() {
                alert('加载配置数据失败');
            }
        });
    },
    
    // 渲染配置表格
    renderConfigTable: function(rows) {
        var tbody = $('#configTable tbody');
        tbody.empty();
        
        if (rows.length === 0) {
            tbody.append('<tr><td colspan="12" class="text-center">暂无数据</td></tr>');
            return;
        }
        
        $.each(rows, function(index, row) {
            var statusClass = row.status == 1 ? 'status-active' : 'status-inactive';
            var statusText = row.status == 1 ? '启用' : '禁用';
            var defaultText = row.is_default == 1 ? '是' : '否';
            var contentPreview = row.content_source ? row.content_source.substring(0, 50) + '...' : '';
            
            var tr = $('<tr>').append(
                $('<td>').text(row.id),
                $('<td>').text(row.category_name || ''),
                $('<td>').text(row.tab_name),
                $('<td>').text(row.tab_code),
                $('<td>').html(row.tab_icon ? '<i class="fa ' + row.tab_icon + '"></i>' : ''),
                $('<td>').text(row.content_type),
                $('<td>').html('<div class="content-preview" title="' + (row.content_source || '') + '">' + contentPreview + '</div>'),
                $('<td>').text(row.load_method),
                $('<td>').text(row.sort),
                $('<td>').html('<span class="status-badge ' + statusClass + '">' + statusText + '</span>'),
                $('<td>').text(defaultText),
                $('<td>').html(
                    '<button class="btn btn-xs btn-primary" onclick="MobileTabManager.editConfig(' + row.id + ')">编辑</button> ' +
                    '<button class="btn btn-xs btn-info" onclick="MobileTabManager.previewConfig(' + row.id + ')">预览</button> ' +
                    '<button class="btn btn-xs btn-danger" onclick="MobileTabManager.deleteConfig(' + row.id + ')">删除</button>'
                )
            );
            tbody.append(tr);
        });
    },
    
    // 渲染配置分页
    renderConfigPagination: function(total, currentPage) {
        var totalPages = Math.ceil(total / 20);
        var pagination = $('#configPagination');
        pagination.empty();
        
        if (totalPages <= 1) return;
        
        var nav = $('<nav>').append($('<ul class="pagination">'));
        var ul = nav.find('ul');
        
        // 上一页
        if (currentPage > 1) {
            ul.append('<li><a href="javascript:void(0)" onclick="MobileTabManager.loadConfigTable(' + (currentPage - 1) + ')">上一页</a></li>');
        }
        
        // 页码
        for (var i = 1; i <= totalPages; i++) {
            var activeClass = i === currentPage ? ' class="active"' : '';
            ul.append('<li' + activeClass + '><a href="javascript:void(0)" onclick="MobileTabManager.loadConfigTable(' + i + ')">' + i + '</a></li>');
        }
        
        // 下一页
        if (currentPage < totalPages) {
            ul.append('<li><a href="javascript:void(0)" onclick="MobileTabManager.loadConfigTable(' + (currentPage + 1) + ')">下一页</a></li>');
        }
        
        pagination.append(nav);
    },
    
    // 加载分类选项
    loadCategoryOptions: function() {
        $.ajax({
            url: 'index.php?d=system&m=mobiletab&a=getCategoryData',
            type: 'GET',
            data: { limit: 1000 },
            success: function(response) {
                var data = JSON.parse(response);
                var categoryFilter = $('#categoryFilter');
                var configCategoryId = $('#configCategoryId');
                
                // 清空现有选项
                categoryFilter.find('option:not(:first)').remove();
                configCategoryId.find('option:not(:first)').remove();
                
                // 添加分类选项
                $.each(data.rows, function(index, row) {
                    if (row.status == 1) {
                        var option = '<option value="' + row.id + '">' + row.name + '</option>';
                        categoryFilter.append(option);
                        configCategoryId.append(option);
                    }
                });
            }
        });
    },
    
    // 搜索分类
    searchCategory: function() {
        this.loadCategoryTable(1);
    },
    
    // 搜索配置
    searchConfig: function() {
        this.loadConfigTable(1);
    },
    
    // 刷新分类表格
    refreshCategoryTable: function() {
        $('#categorySearch').val('');
        this.loadCategoryTable(1);
    },
    
    // 刷新配置表格
    refreshConfigTable: function() {
        $('#configSearch').val('');
        $('#categoryFilter').val('');
        this.loadConfigTable(1);
    }
};

// 分类管理函数
function addCategory() {
    $('#categoryModal .modal-title').text('新增分类');
    $('#categoryForm')[0].reset();
    $('#categoryId').val('');
    $('#categoryModal').modal('show');
}

function editCategory(id) {
    // 这里需要获取分类详情并填充表单
    // 简化处理，实际应该通过AJAX获取详情
    $('#categoryModal .modal-title').text('编辑分类');
    $('#categoryId').val(id);
    $('#categoryModal').modal('show');
}

function saveCategory() {
    var formData = $('#categoryForm').serialize();
    
    $.ajax({
        url: 'index.php?d=system&m=mobiletab&a=saveCategory',
        type: 'POST',
        data: formData,
        success: function(response) {
            var result = JSON.parse(response);
            if (result.success) {
                alert('保存成功');
                $('#categoryModal').modal('hide');
                MobileTabManager.loadCategoryTable();
                MobileTabManager.loadCategoryOptions();
            } else {
                alert('保存失败：' + result.message);
            }
        },
        error: function() {
            alert('保存失败');
        }
    });
}

function deleteCategory(id) {
    if (!confirm('确定要删除这个分类吗？')) return;
    
    $.ajax({
        url: 'index.php?d=system&m=mobiletab&a=deleteCategory',
        type: 'POST',
        data: { id: id },
        success: function(response) {
            var result = JSON.parse(response);
            if (result.success) {
                alert('删除成功');
                MobileTabManager.loadCategoryTable();
            } else {
                alert('删除失败：' + result.message);
            }
        }
    });
}

// 配置管理函数
function addTabConfig() {
    $('#configModal .modal-title').text('新增标签页');
    $('#configForm')[0].reset();
    $('#configId').val('');
    $('#configModal').modal('show');
}

function editConfig(id) {
    $('#configModal .modal-title').text('编辑标签页');
    $('#configId').val(id);
    $('#configModal').modal('show');
}

function saveConfig() {
    var formData = $('#configForm').serialize();
    
    $.ajax({
        url: 'index.php?d=system&m=mobiletab&a=saveConfig',
        type: 'POST',
        data: formData,
        success: function(response) {
            var result = JSON.parse(response);
            if (result.success) {
                alert('保存成功');
                $('#configModal').modal('hide');
                MobileTabManager.loadConfigTable();
            } else {
                alert('保存失败：' + result.message);
            }
        },
        error: function() {
            alert('保存失败');
        }
    });
}

function deleteConfig(id) {
    if (!confirm('确定要删除这个标签页吗？')) return;
    
    $.ajax({
        url: 'index.php?d=system&m=mobiletab&a=deleteConfig',
        type: 'POST',
        data: { id: id },
        success: function(response) {
            var result = JSON.parse(response);
            if (result.success) {
                alert('删除成功');
                MobileTabManager.loadConfigTable();
            } else {
                alert('删除失败：' + result.message);
            }
        }
    });
}

function previewConfig(id) {
    window.open('index.php?d=system&m=mobiletab&a=preview&tab_id=' + id, '_blank');
}

function previewTab() {
    var tabId = $('#configId').val();
    if (tabId) {
        previewConfig(tabId);
    } else {
        alert('请先保存标签页配置');
    }
}

function sortTabs() {
    alert('排序功能开发中...');
}

// 页面加载完成后初始化
$(document).ready(function() {
    MobileTabManager.init();
});
