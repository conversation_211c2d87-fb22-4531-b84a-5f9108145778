<?php
/**
 * 移动端标签页管理控制器
 * 创建时间：2025-01-03
 * 功能：PC端管理移动端标签页配置
 */

class mobileTabClassAction extends Action
{
    public function __construct()
    {
        parent::__construct();
        $this->model = m('mobileTab');
    }

    /**
     * 默认页面 - 标签页分类管理
     */
    public function defaultAction()
    {
        $this->title = '移动端标签页管理';
    }

    /**
     * 标签页配置管理
     */
    public function configAction()
    {
        $this->title = '标签页配置管理';
        $categoryId = $this->rock->get('category_id', 0);
        $this->assign('category_id', $categoryId);
        
        // 获取分类列表用于下拉选择
        $categories = $this->model->getCategoryList(['status' => 1]);
        $this->assign('categories', $categories);
    }

    /**
     * 获取分类数据
     */
    public function getCategoryDataAction()
    {
        $this->display = false;
        
        $page = $this->rock->get('page', 1);
        $limit = $this->rock->get('limit', 20);
        $start = ($page - 1) * $limit;
        
        $where = "WHERE 1=1";
        $search = $this->rock->get('search');
        if (!empty($search)) {
            $where .= " AND (`name` LIKE '%{$search}%' OR `code` LIKE '%{$search}%')";
        }
        
        $sql = "SELECT * FROM `{$this->model->perfix}mobile_tab_category` {$where} ORDER BY `sort` ASC, `id` ASC LIMIT {$start}, {$limit}";
        $rows = $this->model->db->getall($sql);
        
        $countSql = "SELECT COUNT(*) as total FROM `{$this->model->perfix}mobile_tab_category` {$where}";
        $total = $this->model->db->getmou($countSql)['total'];
        
        echo json_encode([
            'rows' => $rows,
            'total' => $total
        ]);
    }

    /**
     * 获取标签页配置数据
     */
    public function getConfigDataAction()
    {
        $this->display = false;
        
        $page = $this->rock->get('page', 1);
        $limit = $this->rock->get('limit', 20);
        $start = ($page - 1) * $limit;
        $categoryId = $this->rock->get('category_id', 0);
        
        $where = "WHERE t.status = 1";
        if ($categoryId > 0) {
            $where .= " AND t.category_id = " . intval($categoryId);
        }
        
        $search = $this->rock->get('search');
        if (!empty($search)) {
            $where .= " AND (t.tab_name LIKE '%{$search}%' OR t.tab_code LIKE '%{$search}%')";
        }
        
        $sql = "SELECT t.*, c.name as category_name 
                FROM `{$this->model->perfix}mobile_tab_config` t 
                LEFT JOIN `{$this->model->perfix}mobile_tab_category` c ON t.category_id = c.id 
                {$where} ORDER BY t.sort ASC, t.id ASC LIMIT {$start}, {$limit}";
        $rows = $this->model->db->getall($sql);
        
        $countSql = "SELECT COUNT(*) as total 
                     FROM `{$this->model->perfix}mobile_tab_config` t 
                     LEFT JOIN `{$this->model->perfix}mobile_tab_category` c ON t.category_id = c.id 
                     {$where}";
        $total = $this->model->db->getmou($countSql)['total'];
        
        echo json_encode([
            'rows' => $rows,
            'total' => $total
        ]);
    }

    /**
     * 保存分类
     */
    public function saveCategoryAction()
    {
        $this->display = false;
        
        $data = [
            'name' => $this->rock->get('name'),
            'code' => $this->rock->get('code'),
            'description' => $this->rock->get('description'),
            'sort' => intval($this->rock->get('sort', 0)),
            'status' => intval($this->rock->get('status', 1))
        ];
        
        $id = $this->rock->get('id', 0);
        if ($id > 0) {
            $data['id'] = $id;
        }
        
        // 验证必填字段
        if (empty($data['name']) || empty($data['code'])) {
            echo json_encode(['success' => false, 'message' => '名称和代码不能为空']);
            return;
        }
        
        // 检查代码唯一性
        $existing = $this->model->db->getmou('mobile_tab_category', 'id', 
            "`code` = '{$data['code']}'" . ($id > 0 ? " AND `id` != $id" : ""));
        if ($existing) {
            echo json_encode(['success' => false, 'message' => '分类代码已存在']);
            return;
        }
        
        $result = $this->model->saveCategory($data);
        if ($result) {
            echo json_encode(['success' => true, 'message' => '保存成功']);
        } else {
            echo json_encode(['success' => false, 'message' => '保存失败']);
        }
    }

    /**
     * 保存标签页配置
     */
    public function saveConfigAction()
    {
        $this->display = false;
        
        $data = [
            'category_id' => intval($this->rock->get('category_id')),
            'tab_name' => $this->rock->get('tab_name'),
            'tab_code' => $this->rock->get('tab_code'),
            'tab_icon' => $this->rock->get('tab_icon'),
            'content_type' => $this->rock->get('content_type', 'html'),
            'content_source' => $this->rock->get('content_source'),
            'load_method' => $this->rock->get('load_method', 'immediate'),
            'sort' => intval($this->rock->get('sort', 0)),
            'status' => intval($this->rock->get('status', 1)),
            'is_default' => intval($this->rock->get('is_default', 0)),
            'permissions' => $this->rock->get('permissions')
        ];
        
        $id = $this->rock->get('id', 0);
        if ($id > 0) {
            $data['id'] = $id;
        }
        
        // 验证必填字段
        if (empty($data['tab_name']) || empty($data['tab_code']) || $data['category_id'] <= 0) {
            echo json_encode(['success' => false, 'message' => '标签名称、代码和分类不能为空']);
            return;
        }
        
        // 检查代码唯一性
        $existing = $this->model->db->getmou('mobile_tab_config', 'id', 
            "`tab_code` = '{$data['tab_code']}' AND `category_id` = {$data['category_id']}" . 
            ($id > 0 ? " AND `id` != $id" : ""));
        if ($existing) {
            echo json_encode(['success' => false, 'message' => '同分类下标签代码已存在']);
            return;
        }
        
        $result = $this->model->saveTabConfig($data);
        if ($result) {
            echo json_encode(['success' => true, 'message' => '保存成功']);
        } else {
            echo json_encode(['success' => false, 'message' => '保存失败']);
        }
    }

    /**
     * 删除标签页配置
     */
    public function deleteConfigAction()
    {
        $this->display = false;
        
        $id = intval($this->rock->get('id'));
        if ($id <= 0) {
            echo json_encode(['success' => false, 'message' => '参数错误']);
            return;
        }
        
        $result = $this->model->deleteTabConfig($id);
        if ($result) {
            echo json_encode(['success' => true, 'message' => '删除成功']);
        } else {
            echo json_encode(['success' => false, 'message' => '删除失败']);
        }
    }

    /**
     * 更新排序
     */
    public function updateSortAction()
    {
        $this->display = false;
        
        $sortData = $this->rock->get('sortData');
        if (empty($sortData)) {
            echo json_encode(['success' => false, 'message' => '排序数据不能为空']);
            return;
        }
        
        $sortArray = json_decode($sortData, true);
        if (!is_array($sortArray)) {
            echo json_encode(['success' => false, 'message' => '排序数据格式错误']);
            return;
        }
        
        $result = $this->model->updateTabSort($sortArray);
        if ($result) {
            echo json_encode(['success' => true, 'message' => '排序更新成功']);
        } else {
            echo json_encode(['success' => false, 'message' => '排序更新失败']);
        }
    }

    /**
     * 预览标签页
     */
    public function previewAction()
    {
        $tabId = intval($this->rock->get('tab_id'));
        $this->assign('tab_id', $tabId);
        
        if ($tabId > 0) {
            $tab = $this->model->db->getmou('mobile_tab_config', '*', "`id` = $tabId");
            $this->assign('tab', $tab);
        }
    }

    /**
     * 获取标签页内容（用于预览）
     */
    public function getTabContentAction()
    {
        $this->display = false;
        
        $tabId = intval($this->rock->get('tab_id'));
        $params = $this->rock->get('params', []);
        
        if (is_string($params)) {
            $params = json_decode($params, true) ?: [];
        }
        
        $content = $this->model->getTabContent($tabId, $params);
        echo $content;
    }
}
