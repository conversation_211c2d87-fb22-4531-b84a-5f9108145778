<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>移动端标签页管理</title>
    <link rel="stylesheet" type="text/css" href="mode/bootstrapplugin/bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="web/res/fontawesome/css/font-awesome.min.css">
    <style>
        .tab-management {
            padding: 20px;
        }
        .category-section {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        .btn-group-custom {
            margin-bottom: 15px;
        }
        .form-inline-custom {
            margin-bottom: 15px;
        }
        .form-inline-custom .form-control {
            margin-right: 10px;
        }
        .table-responsive {
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .status-badge {
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 12px;
        }
        .status-active {
            background-color: #d4edda;
            color: #155724;
        }
        .status-inactive {
            background-color: #f8d7da;
            color: #721c24;
        }
        .content-preview {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    </style>
</head>
<body>
    <div class="tab-management">
        <!-- 标签页分类管理 -->
        <div class="category-section">
            <div class="section-title">
                <i class="fa fa-folder"></i> 标签页分类管理
            </div>
            
            <div class="btn-group-custom">
                <button type="button" class="btn btn-primary" onclick="addCategory()">
                    <i class="fa fa-plus"></i> 新增分类
                </button>
                <button type="button" class="btn btn-success" onclick="refreshCategoryTable()">
                    <i class="fa fa-refresh"></i> 刷新
                </button>
            </div>
            
            <div class="form-inline-custom">
                <input type="text" class="form-control" id="categorySearch" placeholder="搜索分类名称或代码">
                <button type="button" class="btn btn-default" onclick="searchCategory()">
                    <i class="fa fa-search"></i> 搜索
                </button>
            </div>
            
            <div class="table-responsive">
                <table class="table table-striped table-hover" id="categoryTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>分类名称</th>
                            <th>分类代码</th>
                            <th>描述</th>
                            <th>排序</th>
                            <th>状态</th>
                            <th>操作时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 数据通过AJAX加载 -->
                    </tbody>
                </table>
            </div>
            
            <div id="categoryPagination"></div>
        </div>

        <!-- 标签页配置管理 -->
        <div class="category-section">
            <div class="section-title">
                <i class="fa fa-tags"></i> 标签页配置管理
            </div>
            
            <div class="btn-group-custom">
                <button type="button" class="btn btn-primary" onclick="addTabConfig()">
                    <i class="fa fa-plus"></i> 新增标签页
                </button>
                <button type="button" class="btn btn-success" onclick="refreshConfigTable()">
                    <i class="fa fa-refresh"></i> 刷新
                </button>
                <button type="button" class="btn btn-info" onclick="sortTabs()">
                    <i class="fa fa-sort"></i> 排序管理
                </button>
            </div>
            
            <div class="form-inline-custom">
                <select class="form-control" id="categoryFilter">
                    <option value="">全部分类</option>
                    <!-- 分类选项通过AJAX加载 -->
                </select>
                <input type="text" class="form-control" id="configSearch" placeholder="搜索标签名称或代码">
                <button type="button" class="btn btn-default" onclick="searchConfig()">
                    <i class="fa fa-search"></i> 搜索
                </button>
            </div>
            
            <div class="table-responsive">
                <table class="table table-striped table-hover" id="configTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>分类</th>
                            <th>标签名称</th>
                            <th>标签代码</th>
                            <th>图标</th>
                            <th>内容类型</th>
                            <th>内容源</th>
                            <th>加载方式</th>
                            <th>排序</th>
                            <th>状态</th>
                            <th>默认</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 数据通过AJAX加载 -->
                    </tbody>
                </table>
            </div>
            
            <div id="configPagination"></div>
        </div>
    </div>

    <!-- 分类编辑模态框 -->
    <div class="modal fade" id="categoryModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">编辑分类</h4>
                </div>
                <div class="modal-body">
                    <form id="categoryForm">
                        <input type="hidden" id="categoryId" name="id">
                        <div class="form-group">
                            <label>分类名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="categoryName" name="name" required>
                        </div>
                        <div class="form-group">
                            <label>分类代码 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="categoryCode" name="code" required>
                            <small class="text-muted">用于程序识别，只能包含字母、数字和下划线</small>
                        </div>
                        <div class="form-group">
                            <label>描述</label>
                            <textarea class="form-control" id="categoryDescription" name="description" rows="3"></textarea>
                        </div>
                        <div class="form-group">
                            <label>排序号</label>
                            <input type="number" class="form-control" id="categorySort" name="sort" value="0">
                        </div>
                        <div class="form-group">
                            <label>状态</label>
                            <select class="form-control" id="categoryStatus" name="status">
                                <option value="1">启用</option>
                                <option value="0">禁用</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveCategory()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 标签页配置编辑模态框 -->
    <div class="modal fade" id="configModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">编辑标签页配置</h4>
                </div>
                <div class="modal-body">
                    <form id="configForm">
                        <input type="hidden" id="configId" name="id">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>所属分类 <span class="text-danger">*</span></label>
                                    <select class="form-control" id="configCategoryId" name="category_id" required>
                                        <option value="">请选择分类</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>标签名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="configTabName" name="tab_name" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>标签代码 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="configTabCode" name="tab_code" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>标签图标</label>
                                    <input type="text" class="form-control" id="configTabIcon" name="tab_icon" placeholder="如：icon-info">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>内容类型</label>
                                    <select class="form-control" id="configContentType" name="content_type">
                                        <option value="html">HTML内容</option>
                                        <option value="ajax">AJAX加载</option>
                                        <option value="iframe">iframe嵌入</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>加载方式</label>
                                    <select class="form-control" id="configLoadMethod" name="load_method">
                                        <option value="immediate">立即加载</option>
                                        <option value="lazy">懒加载</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>内容源</label>
                            <textarea class="form-control" id="configContentSource" name="content_source" rows="4" placeholder="HTML内容、AJAX地址或iframe URL"></textarea>
                            <small class="text-muted">
                                HTML类型：直接输入HTML代码<br>
                                AJAX类型：格式为 module,action,method<br>
                                iframe类型：输入完整的URL地址
                            </small>
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>排序号</label>
                                    <input type="number" class="form-control" id="configSort" name="sort" value="0">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>状态</label>
                                    <select class="form-control" id="configStatus" name="status">
                                        <option value="1">启用</option>
                                        <option value="0">禁用</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>默认选中</label>
                                    <select class="form-control" id="configIsDefault" name="is_default">
                                        <option value="0">否</option>
                                        <option value="1">是</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>权限设置</label>
                                    <input type="text" class="form-control" id="configPermissions" name="permissions" placeholder="可选">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-info" onclick="previewTab()">预览</button>
                    <button type="button" class="btn btn-primary" onclick="saveConfig()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <script src="js/jquery.js"></script>
    <script src="mode/bootstrapplugin/bootstrap.min.js"></script>
    <script src="webmain/system/mobiletab/mobileTab.js"></script>
</body>
</html>
