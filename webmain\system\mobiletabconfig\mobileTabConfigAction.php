<?php
/**
 * 移动端标签页配置管理控制器（基于配置文件）
 * 创建时间：2025-01-03
 * 功能：管理配置文件中的标签页设置
 */

class mobileTabConfigClassAction extends Action
{
    public function __construct()
    {
        parent::__construct();
        $this->model = m('mobileTabConfig');
    }

    /**
     * 默认页面 - 配置管理
     */
    public function defaultAction()
    {
        $this->title = '移动端标签页配置管理';
        $config = $this->model->getConfig();
        $this->assign('config', $config);
    }

    /**
     * 获取配置数据（JSON格式）
     */
    public function getConfigAction()
    {
        $this->display = false;
        $config = $this->model->getConfig();
        echo json_encode($config, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }

    /**
     * 保存配置
     */
    public function saveConfigAction()
    {
        $this->display = false;
        
        $configJson = $this->rock->get('config');
        if (empty($configJson)) {
            echo json_encode(['success' => false, 'message' => '配置数据不能为空']);
            return;
        }
        
        try {
            $config = json_decode($configJson, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                echo json_encode(['success' => false, 'message' => 'JSON格式错误：' . json_last_error_msg()]);
                return;
            }
            
            $result = $this->model->saveConfig($config);
            if ($result) {
                echo json_encode(['success' => true, 'message' => '保存成功']);
            } else {
                echo json_encode(['success' => false, 'message' => '保存失败，请检查文件权限']);
            }
            
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => '保存失败：' . $e->getMessage()]);
        }
    }

    /**
     * 添加分类
     */
    public function addCategoryAction()
    {
        $this->display = false;
        
        $code = $this->rock->get('code');
        $name = $this->rock->get('name');
        $description = $this->rock->get('description');
        $sort = intval($this->rock->get('sort', 0));
        
        if (empty($code) || empty($name)) {
            echo json_encode(['success' => false, 'message' => '分类代码和名称不能为空']);
            return;
        }
        
        $data = [
            'name' => $name,
            'description' => $description,
            'sort' => $sort,
            'status' => 1
        ];
        
        $result = $this->model->saveCategory($code, $data);
        if ($result) {
            echo json_encode(['success' => true, 'message' => '添加分类成功']);
        } else {
            echo json_encode(['success' => false, 'message' => '添加分类失败']);
        }
    }

    /**
     * 添加标签页
     */
    public function addTabAction()
    {
        $this->display = false;
        
        $categoryCode = $this->rock->get('category_code');
        $tabCode = $this->rock->get('tab_code');
        $tabName = $this->rock->get('tab_name');
        $tabIcon = $this->rock->get('tab_icon');
        $contentType = $this->rock->get('content_type', 'html');
        $contentSource = $this->rock->get('content_source');
        $loadMethod = $this->rock->get('load_method', 'immediate');
        $sort = intval($this->rock->get('sort', 0));
        $isDefault = intval($this->rock->get('is_default', 0));
        
        if (empty($categoryCode) || empty($tabCode) || empty($tabName)) {
            echo json_encode(['success' => false, 'message' => '分类代码、标签代码和标签名称不能为空']);
            return;
        }
        
        $data = [
            'tab_name' => $tabName,
            'tab_code' => $tabCode,
            'tab_icon' => $tabIcon,
            'content_type' => $contentType,
            'content_source' => $contentSource,
            'load_method' => $loadMethod,
            'sort' => $sort,
            'status' => 1,
            'is_default' => $isDefault,
            'permissions' => ''
        ];
        
        $result = $this->model->saveTab($categoryCode, $tabCode, $data);
        if ($result) {
            echo json_encode(['success' => true, 'message' => '添加标签页成功']);
        } else {
            echo json_encode(['success' => false, 'message' => '添加标签页失败']);
        }
    }

    /**
     * 删除标签页
     */
    public function deleteTabAction()
    {
        $this->display = false;
        
        $categoryCode = $this->rock->get('category_code');
        $tabCode = $this->rock->get('tab_code');
        
        if (empty($categoryCode) || empty($tabCode)) {
            echo json_encode(['success' => false, 'message' => '参数错误']);
            return;
        }
        
        $result = $this->model->deleteTab($categoryCode, $tabCode);
        if ($result) {
            echo json_encode(['success' => true, 'message' => '删除成功']);
        } else {
            echo json_encode(['success' => false, 'message' => '删除失败']);
        }
    }

    /**
     * 预览标签页
     */
    public function previewAction()
    {
        $categoryCode = $this->rock->get('category_code');
        $tabCode = $this->rock->get('tab_code');
        
        $this->assign('category_code', $categoryCode);
        $this->assign('tab_code', $tabCode);
        
        // 获取示例数据
        $sampleData = [
            'id' => 123,
            'name' => '示例客户',
            'tel' => '13800138000',
            'lxr' => '张经理',
            'address' => '北京市朝阳区',
            'khlx' => '企业客户',
            'khly' => '网络推广',
            'khzt' => '正常',
            'optdt' => date('Y-m-d H:i:s'),
            'explain' => '这是一个示例客户的说明信息'
        ];
        
        $content = $this->model->getTabContent(0, $sampleData, $categoryCode, $tabCode);
        $this->assign('content', $content);
        $this->assign('sample_data', $sampleData);
    }

    /**
     * 导出配置
     */
    public function exportAction()
    {
        $this->display = false;
        
        $config = $this->model->getConfig();
        $filename = 'mobile_tabs_config_' . date('Y-m-d_H-i-s') . '.json';
        
        header('Content-Type: application/json');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        echo json_encode($config, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }

    /**
     * 导入配置
     */
    public function importAction()
    {
        $this->display = false;
        
        if (!isset($_FILES['config_file'])) {
            echo json_encode(['success' => false, 'message' => '请选择配置文件']);
            return;
        }
        
        $file = $_FILES['config_file'];
        if ($file['error'] !== UPLOAD_ERR_OK) {
            echo json_encode(['success' => false, 'message' => '文件上传失败']);
            return;
        }
        
        $content = file_get_contents($file['tmp_name']);
        if (empty($content)) {
            echo json_encode(['success' => false, 'message' => '文件内容为空']);
            return;
        }
        
        try {
            $config = json_decode($content, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                echo json_encode(['success' => false, 'message' => 'JSON格式错误：' . json_last_error_msg()]);
                return;
            }
            
            $result = $this->model->saveConfig($config);
            if ($result) {
                echo json_encode(['success' => true, 'message' => '导入成功']);
            } else {
                echo json_encode(['success' => false, 'message' => '导入失败']);
            }
            
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => '导入失败：' . $e->getMessage()]);
        }
    }

    /**
     * 重置为默认配置
     */
    public function resetAction()
    {
        $this->display = false;
        
        // 备份当前配置
        $currentConfig = $this->model->getConfig();
        $backupFile = dirname(__FILE__) . '/../../config/mobileTabsConfig_backup_' . date('Y-m-d_H-i-s') . '.php';
        $backupContent = "<?php\nreturn " . var_export($currentConfig, true) . ";\n?>";
        file_put_contents($backupFile, $backupContent);
        
        // 重置为默认配置
        $defaultConfigFile = dirname(__FILE__) . '/../../config/mobileTabsConfig.php';
        if (file_exists($defaultConfigFile . '.default')) {
            copy($defaultConfigFile . '.default', $defaultConfigFile);
            echo json_encode(['success' => true, 'message' => '重置成功，原配置已备份']);
        } else {
            echo json_encode(['success' => false, 'message' => '默认配置文件不存在']);
        }
    }
}
