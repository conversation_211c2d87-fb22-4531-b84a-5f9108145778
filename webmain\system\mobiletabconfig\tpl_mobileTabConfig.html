<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>移动端标签页配置管理</title>
    <link rel="stylesheet" type="text/css" href="mode/bootstrapplugin/bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="web/res/fontawesome/css/font-awesome.min.css">
    <style>
        .config-container {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        .section-card {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin-bottom: 20px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 8px;
        }
        .config-editor {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.4;
            max-height: 400px;
            overflow-y: auto;
        }
        .btn-group-custom {
            margin-bottom: 15px;
        }
        .btn-group-custom .btn {
            margin-right: 10px;
        }
        .quick-add-form {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 10px;
            align-items: end;
        }
        .form-row .form-group {
            flex: 1;
            margin-bottom: 0;
        }
        .form-row .form-group label {
            font-size: 12px;
            margin-bottom: 3px;
        }
        .category-list, .tab-list {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
        }
        .category-item, .tab-item {
            padding: 10px 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .category-item:last-child, .tab-item:last-child {
            border-bottom: none;
        }
        .item-info {
            flex: 1;
        }
        .item-name {
            font-weight: bold;
            color: #333;
        }
        .item-code {
            font-size: 12px;
            color: #666;
            font-family: monospace;
        }
        .item-actions {
            display: flex;
            gap: 5px;
        }
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .status-active {
            background-color: #28a745;
        }
        .status-inactive {
            background-color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="config-container">
        <h2><i class="fa fa-cogs"></i> 移动端标签页配置管理</h2>
        <p class="text-muted">基于配置文件的标签页管理，无需数据库支持</p>

        <!-- 操作按钮组 -->
        <div class="section-card">
            <div class="btn-group-custom">
                <button type="button" class="btn btn-primary" onclick="loadConfig()">
                    <i class="fa fa-refresh"></i> 刷新配置
                </button>
                <button type="button" class="btn btn-success" onclick="saveConfig()">
                    <i class="fa fa-save"></i> 保存配置
                </button>
                <button type="button" class="btn btn-info" onclick="exportConfig()">
                    <i class="fa fa-download"></i> 导出配置
                </button>
                <button type="button" class="btn btn-warning" onclick="$('#importModal').modal('show')">
                    <i class="fa fa-upload"></i> 导入配置
                </button>
                <button type="button" class="btn btn-danger" onclick="resetConfig()">
                    <i class="fa fa-undo"></i> 重置默认
                </button>
            </div>
        </div>

        <!-- 分类管理 -->
        <div class="section-card">
            <div class="section-title">
                <i class="fa fa-folder"></i> 分类管理
            </div>
            
            <!-- 快速添加分类 -->
            <div class="quick-add-form">
                <h5>快速添加分类</h5>
                <div class="form-row">
                    <div class="form-group">
                        <label>分类代码</label>
                        <input type="text" class="form-control" id="newCategoryCode" placeholder="如：customer">
                    </div>
                    <div class="form-group">
                        <label>分类名称</label>
                        <input type="text" class="form-control" id="newCategoryName" placeholder="如：客户详情">
                    </div>
                    <div class="form-group">
                        <label>描述</label>
                        <input type="text" class="form-control" id="newCategoryDesc" placeholder="可选">
                    </div>
                    <div class="form-group">
                        <label>排序</label>
                        <input type="number" class="form-control" id="newCategorySort" value="0" style="width: 80px;">
                    </div>
                    <div class="form-group">
                        <button type="button" class="btn btn-primary" onclick="addCategory()">添加</button>
                    </div>
                </div>
            </div>
            
            <!-- 分类列表 -->
            <div class="category-list" id="categoryList">
                <!-- 动态加载 -->
            </div>
        </div>

        <!-- 标签页管理 -->
        <div class="section-card">
            <div class="section-title">
                <i class="fa fa-tags"></i> 标签页管理
            </div>
            
            <!-- 快速添加标签页 -->
            <div class="quick-add-form">
                <h5>快速添加标签页</h5>
                <div class="form-row">
                    <div class="form-group">
                        <label>所属分类</label>
                        <select class="form-control" id="newTabCategory">
                            <option value="">选择分类</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>标签代码</label>
                        <input type="text" class="form-control" id="newTabCode" placeholder="如：basic_info">
                    </div>
                    <div class="form-group">
                        <label>标签名称</label>
                        <input type="text" class="form-control" id="newTabName" placeholder="如：基本信息">
                    </div>
                    <div class="form-group">
                        <label>图标</label>
                        <input type="text" class="form-control" id="newTabIcon" placeholder="如：icon-info">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>内容类型</label>
                        <select class="form-control" id="newTabContentType">
                            <option value="html">HTML</option>
                            <option value="ajax">AJAX</option>
                            <option value="iframe">iframe</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>内容源</label>
                        <input type="text" class="form-control" id="newTabContentSource" placeholder="HTML代码或AJAX地址">
                    </div>
                    <div class="form-group">
                        <label>加载方式</label>
                        <select class="form-control" id="newTabLoadMethod">
                            <option value="immediate">立即加载</option>
                            <option value="lazy">懒加载</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>排序</label>
                        <input type="number" class="form-control" id="newTabSort" value="0" style="width: 80px;">
                    </div>
                    <div class="form-group">
                        <button type="button" class="btn btn-primary" onclick="addTab()">添加</button>
                    </div>
                </div>
            </div>
            
            <!-- 标签页列表 -->
            <div class="tab-list" id="tabList">
                <!-- 动态加载 -->
            </div>
        </div>

        <!-- 配置编辑器 -->
        <div class="section-card">
            <div class="section-title">
                <i class="fa fa-code"></i> 高级配置编辑器
            </div>
            <p class="text-muted">直接编辑JSON配置（高级用户）</p>
            <textarea class="form-control config-editor" id="configEditor" rows="20" placeholder="配置将在这里显示..."></textarea>
        </div>
    </div>

    <!-- 导入配置模态框 -->
    <div class="modal fade" id="importModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">导入配置</h4>
                </div>
                <div class="modal-body">
                    <form id="importForm" enctype="multipart/form-data">
                        <div class="form-group">
                            <label>选择配置文件（JSON格式）</label>
                            <input type="file" class="form-control" name="config_file" accept=".json">
                        </div>
                        <div class="alert alert-warning">
                            <strong>注意：</strong>导入配置将覆盖当前所有设置，请确保已备份重要配置。
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="importConfig()">导入</button>
                </div>
            </div>
        </div>
    </div>

    <script src="js/jquery.js"></script>
    <script src="mode/bootstrapplugin/bootstrap.min.js"></script>
    <script>
        var currentConfig = {};

        // 页面加载完成后初始化
        $(document).ready(function() {
            loadConfig();
        });

        // 加载配置
        function loadConfig() {
            $.get('index.php?d=system&m=mobiletabconfig&a=getConfig', function(response) {
                try {
                    currentConfig = JSON.parse(response);
                    $('#configEditor').val(JSON.stringify(currentConfig, null, 2));
                    renderCategories();
                    renderTabs();
                    updateCategorySelect();
                } catch (e) {
                    alert('加载配置失败：' + e.message);
                }
            });
        }

        // 保存配置
        function saveConfig() {
            var configText = $('#configEditor').val();
            try {
                var config = JSON.parse(configText);
                $.post('index.php?d=system&m=mobiletabconfig&a=saveConfig', {
                    config: configText
                }, function(response) {
                    var result = JSON.parse(response);
                    if (result.success) {
                        alert('保存成功');
                        currentConfig = config;
                        renderCategories();
                        renderTabs();
                    } else {
                        alert('保存失败：' + result.message);
                    }
                });
            } catch (e) {
                alert('JSON格式错误：' + e.message);
            }
        }

        // 渲染分类列表
        function renderCategories() {
            var html = '';
            for (var code in currentConfig.categories) {
                var category = currentConfig.categories[code];
                var statusClass = category.status ? 'status-active' : 'status-inactive';
                html += '<div class="category-item">';
                html += '<div class="item-info">';
                html += '<div class="item-name"><span class="status-indicator ' + statusClass + '"></span>' + category.name + '</div>';
                html += '<div class="item-code">' + code + '</div>';
                html += '</div>';
                html += '<div class="item-actions">';
                html += '<button class="btn btn-xs btn-danger" onclick="deleteCategory(\'' + code + '\')">删除</button>';
                html += '</div>';
                html += '</div>';
            }
            $('#categoryList').html(html || '<div class="text-center text-muted" style="padding: 20px;">暂无分类</div>');
        }

        // 渲染标签页列表
        function renderTabs() {
            var html = '';
            for (var categoryCode in currentConfig.tabs) {
                var categoryName = currentConfig.categories[categoryCode] ? currentConfig.categories[categoryCode].name : categoryCode;
                var tabs = currentConfig.tabs[categoryCode];
                
                html += '<div style="background: #f8f9fa; padding: 8px 15px; font-weight: bold; border-bottom: 1px solid #ddd;">' + categoryName + ' (' + categoryCode + ')</div>';
                
                for (var i = 0; i < tabs.length; i++) {
                    var tab = tabs[i];
                    var statusClass = tab.status ? 'status-active' : 'status-inactive';
                    html += '<div class="tab-item">';
                    html += '<div class="item-info">';
                    html += '<div class="item-name"><span class="status-indicator ' + statusClass + '"></span>' + tab.tab_name;
                    if (tab.is_default) html += ' <span class="label label-primary">默认</span>';
                    html += '</div>';
                    html += '<div class="item-code">' + tab.tab_code + ' | ' + tab.content_type + '</div>';
                    html += '</div>';
                    html += '<div class="item-actions">';
                    html += '<button class="btn btn-xs btn-info" onclick="previewTab(\'' + categoryCode + '\', \'' + tab.tab_code + '\')">预览</button>';
                    html += '<button class="btn btn-xs btn-danger" onclick="deleteTab(\'' + categoryCode + '\', \'' + tab.tab_code + '\')">删除</button>';
                    html += '</div>';
                    html += '</div>';
                }
            }
            $('#tabList').html(html || '<div class="text-center text-muted" style="padding: 20px;">暂无标签页</div>');
        }

        // 更新分类选择框
        function updateCategorySelect() {
            var html = '<option value="">选择分类</option>';
            for (var code in currentConfig.categories) {
                html += '<option value="' + code + '">' + currentConfig.categories[code].name + '</option>';
            }
            $('#newTabCategory').html(html);
        }

        // 添加分类
        function addCategory() {
            var code = $('#newCategoryCode').val();
            var name = $('#newCategoryName').val();
            var desc = $('#newCategoryDesc').val();
            var sort = parseInt($('#newCategorySort').val()) || 0;

            if (!code || !name) {
                alert('分类代码和名称不能为空');
                return;
            }

            $.post('index.php?d=system&m=mobiletabconfig&a=addCategory', {
                code: code,
                name: name,
                description: desc,
                sort: sort
            }, function(response) {
                var result = JSON.parse(response);
                if (result.success) {
                    alert('添加成功');
                    loadConfig();
                    // 清空表单
                    $('#newCategoryCode, #newCategoryName, #newCategoryDesc').val('');
                    $('#newCategorySort').val('0');
                } else {
                    alert('添加失败：' + result.message);
                }
            });
        }

        // 添加标签页
        function addTab() {
            var categoryCode = $('#newTabCategory').val();
            var tabCode = $('#newTabCode').val();
            var tabName = $('#newTabName').val();
            var tabIcon = $('#newTabIcon').val();
            var contentType = $('#newTabContentType').val();
            var contentSource = $('#newTabContentSource').val();
            var loadMethod = $('#newTabLoadMethod').val();
            var sort = parseInt($('#newTabSort').val()) || 0;

            if (!categoryCode || !tabCode || !tabName) {
                alert('分类、标签代码和标签名称不能为空');
                return;
            }

            $.post('index.php?d=system&m=mobiletabconfig&a=addTab', {
                category_code: categoryCode,
                tab_code: tabCode,
                tab_name: tabName,
                tab_icon: tabIcon,
                content_type: contentType,
                content_source: contentSource,
                load_method: loadMethod,
                sort: sort
            }, function(response) {
                var result = JSON.parse(response);
                if (result.success) {
                    alert('添加成功');
                    loadConfig();
                    // 清空表单
                    $('#newTabCode, #newTabName, #newTabIcon, #newTabContentSource').val('');
                    $('#newTabSort').val('0');
                } else {
                    alert('添加失败：' + result.message);
                }
            });
        }

        // 删除分类
        function deleteCategory(code) {
            if (!confirm('确定要删除分类 "' + code + '" 吗？这将同时删除该分类下的所有标签页。')) {
                return;
            }
            
            delete currentConfig.categories[code];
            delete currentConfig.tabs[code];
            $('#configEditor').val(JSON.stringify(currentConfig, null, 2));
            renderCategories();
            renderTabs();
            updateCategorySelect();
        }

        // 删除标签页
        function deleteTab(categoryCode, tabCode) {
            if (!confirm('确定要删除标签页 "' + tabCode + '" 吗？')) {
                return;
            }

            $.post('index.php?d=system&m=mobiletabconfig&a=deleteTab', {
                category_code: categoryCode,
                tab_code: tabCode
            }, function(response) {
                var result = JSON.parse(response);
                if (result.success) {
                    alert('删除成功');
                    loadConfig();
                } else {
                    alert('删除失败：' + result.message);
                }
            });
        }

        // 预览标签页
        function previewTab(categoryCode, tabCode) {
            window.open('index.php?d=system&m=mobiletabconfig&a=preview&category_code=' + categoryCode + '&tab_code=' + tabCode, '_blank');
        }

        // 导出配置
        function exportConfig() {
            window.open('index.php?d=system&m=mobiletabconfig&a=export', '_blank');
        }

        // 导入配置
        function importConfig() {
            var formData = new FormData($('#importForm')[0]);
            
            $.ajax({
                url: 'index.php?d=system&m=mobiletabconfig&a=import',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    var result = JSON.parse(response);
                    if (result.success) {
                        alert('导入成功');
                        $('#importModal').modal('hide');
                        loadConfig();
                    } else {
                        alert('导入失败：' + result.message);
                    }
                }
            });
        }

        // 重置配置
        function resetConfig() {
            if (!confirm('确定要重置为默认配置吗？当前配置将被备份。')) {
                return;
            }

            $.post('index.php?d=system&m=mobiletabconfig&a=reset', function(response) {
                var result = JSON.parse(response);
                if (result.success) {
                    alert('重置成功');
                    loadConfig();
                } else {
                    alert('重置失败：' + result.message);
                }
            });
        }
    </script>
</body>
</html>
