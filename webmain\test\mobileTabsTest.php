<?php
/**
 * 移动端标签页功能测试脚本
 * 创建时间：2025-01-03
 * 用途：测试标签页功能的各种场景
 */

// 防止直接访问
if (!defined('HOST')) {
    define('HOST', true);
    require_once('../../config/config.php');
}

class MobileTabsTest
{
    private $model;
    private $testResults = [];
    
    public function __construct()
    {
        $this->model = m('mobileTab');
    }
    
    /**
     * 运行所有测试
     */
    public function runAllTests()
    {
        echo "<h2>移动端标签页功能测试</h2>\n";
        echo "<div style='font-family: monospace; background: #f5f5f5; padding: 20px;'>\n";
        
        $this->testDatabaseTables();
        $this->testModelMethods();
        $this->testCategoryOperations();
        $this->testTabConfigOperations();
        $this->testContentGeneration();
        $this->testPermissions();
        
        $this->printSummary();
        echo "</div>\n";
    }
    
    /**
     * 测试数据库表结构
     */
    private function testDatabaseTables()
    {
        echo "<h3>📊 数据库表结构测试</h3>\n";
        
        $tables = [
            'mobile_tab_category',
            'mobile_tab_config', 
            'mobile_tab_relation',
            'mobile_tab_stats'
        ];
        
        foreach ($tables as $table) {
            $fullTableName = PREFIX . $table;
            $exists = $this->model->db->query("SHOW TABLES LIKE '$fullTableName'");
            
            if ($exists) {
                $this->addResult("✅ 表 {$table} 存在", true);
                
                // 检查表结构
                $fields = $this->model->db->getall("DESCRIBE `$fullTableName`");
                $this->addResult("   - 字段数量: " . count($fields), true);
            } else {
                $this->addResult("❌ 表 {$table} 不存在", false);
            }
        }
    }
    
    /**
     * 测试模型方法
     */
    private function testModelMethods()
    {
        echo "<h3>🔧 模型方法测试</h3>\n";
        
        $methods = [
            'getCategoryList',
            'getTabsByCategory',
            'getTabsByRelation',
            'saveCategory',
            'saveTabConfig',
            'getTabContent'
        ];
        
        foreach ($methods as $method) {
            if (method_exists($this->model, $method)) {
                $this->addResult("✅ 方法 {$method} 存在", true);
            } else {
                $this->addResult("❌ 方法 {$method} 不存在", false);
            }
        }
    }
    
    /**
     * 测试分类操作
     */
    private function testCategoryOperations()
    {
        echo "<h3>📁 分类操作测试</h3>\n";
        
        try {
            // 测试创建分类
            $categoryData = [
                'name' => '测试分类',
                'code' => 'test_category_' . time(),
                'description' => '这是一个测试分类',
                'sort' => 999,
                'status' => 1
            ];
            
            $categoryId = $this->model->saveCategory($categoryData);
            if ($categoryId) {
                $this->addResult("✅ 创建分类成功，ID: {$categoryId}", true);
                
                // 测试获取分类列表
                $categories = $this->model->getCategoryList(['status' => 1]);
                $found = false;
                foreach ($categories as $category) {
                    if ($category['id'] == $categoryId) {
                        $found = true;
                        break;
                    }
                }
                
                if ($found) {
                    $this->addResult("✅ 获取分类列表成功", true);
                } else {
                    $this->addResult("❌ 在分类列表中未找到新创建的分类", false);
                }
                
                // 清理测试数据
                $this->model->db->delete('mobile_tab_category', "`id` = $categoryId");
                $this->addResult("🧹 清理测试分类数据", true);
                
            } else {
                $this->addResult("❌ 创建分类失败", false);
            }
            
        } catch (Exception $e) {
            $this->addResult("❌ 分类操作测试异常: " . $e->getMessage(), false);
        }
    }
    
    /**
     * 测试标签页配置操作
     */
    private function testTabConfigOperations()
    {
        echo "<h3>🏷️ 标签页配置测试</h3>\n";
        
        try {
            // 先创建一个测试分类
            $categoryData = [
                'name' => '测试分类',
                'code' => 'test_category_' . time(),
                'status' => 1
            ];
            $categoryId = $this->model->saveCategory($categoryData);
            
            if ($categoryId) {
                // 测试创建标签页配置
                $tabData = [
                    'category_id' => $categoryId,
                    'tab_name' => '测试标签页',
                    'tab_code' => 'test_tab_' . time(),
                    'tab_icon' => 'icon-test',
                    'content_type' => 'html',
                    'content_source' => '<div>测试内容 {id}</div>',
                    'load_method' => 'immediate',
                    'sort' => 1,
                    'status' => 1,
                    'is_default' => 1
                ];
                
                $tabId = $this->model->saveTabConfig($tabData);
                if ($tabId) {
                    $this->addResult("✅ 创建标签页配置成功，ID: {$tabId}", true);
                    
                    // 测试获取标签页配置
                    $tabs = $this->model->getTabsByCategory($categoryId);
                    if (count($tabs) > 0) {
                        $this->addResult("✅ 获取标签页配置成功，数量: " . count($tabs), true);
                    } else {
                        $this->addResult("❌ 获取标签页配置失败", false);
                    }
                    
                    // 清理测试数据
                    $this->model->db->delete('mobile_tab_config', "`id` = $tabId");
                    $this->addResult("🧹 清理测试标签页数据", true);
                    
                } else {
                    $this->addResult("❌ 创建标签页配置失败", false);
                }
                
                // 清理测试分类
                $this->model->db->delete('mobile_tab_category', "`id` = $categoryId");
                
            } else {
                $this->addResult("❌ 创建测试分类失败", false);
            }
            
        } catch (Exception $e) {
            $this->addResult("❌ 标签页配置测试异常: " . $e->getMessage(), false);
        }
    }
    
    /**
     * 测试内容生成
     */
    private function testContentGeneration()
    {
        echo "<h3>📄 内容生成测试</h3>\n";
        
        try {
            // 创建测试数据
            $categoryId = $this->model->saveCategory([
                'name' => '内容测试分类',
                'code' => 'content_test_' . time(),
                'status' => 1
            ]);
            
            $tabId = $this->model->saveTabConfig([
                'category_id' => $categoryId,
                'tab_name' => '内容测试',
                'tab_code' => 'content_test',
                'content_type' => 'html',
                'content_source' => '<div>用户ID: {id}, 姓名: {name}</div>',
                'status' => 1
            ]);
            
            // 测试内容生成
            $params = ['id' => 123, 'name' => '测试用户'];
            $content = $this->model->getTabContent($tabId, $params);
            
            if (strpos($content, '123') !== false && strpos($content, '测试用户') !== false) {
                $this->addResult("✅ HTML内容生成成功", true);
            } else {
                $this->addResult("❌ HTML内容生成失败", false);
            }
            
            // 清理测试数据
            $this->model->db->delete('mobile_tab_config', "`id` = $tabId");
            $this->model->db->delete('mobile_tab_category', "`id` = $categoryId");
            
        } catch (Exception $e) {
            $this->addResult("❌ 内容生成测试异常: " . $e->getMessage(), false);
        }
    }
    
    /**
     * 测试权限功能
     */
    private function testPermissions()
    {
        echo "<h3>🔐 权限功能测试</h3>\n";
        
        // 这里可以添加权限相关的测试
        $this->addResult("ℹ️ 权限功能测试待实现", true);
    }
    
    /**
     * 添加测试结果
     */
    private function addResult($message, $success)
    {
        $this->testResults[] = ['message' => $message, 'success' => $success];
        echo $message . "\n";
    }
    
    /**
     * 打印测试总结
     */
    private function printSummary()
    {
        echo "<h3>📊 测试总结</h3>\n";
        
        $total = count($this->testResults);
        $passed = 0;
        $failed = 0;
        
        foreach ($this->testResults as $result) {
            if ($result['success']) {
                $passed++;
            } else {
                $failed++;
            }
        }
        
        echo "总测试项: {$total}\n";
        echo "通过: {$passed}\n";
        echo "失败: {$failed}\n";
        echo "成功率: " . round(($passed / $total) * 100, 2) . "%\n";
        
        if ($failed == 0) {
            echo "\n🎉 所有测试通过！标签页功能可以正常使用。\n";
        } else {
            echo "\n⚠️ 有 {$failed} 项测试失败，请检查相关配置。\n";
        }
    }
}

// 运行测试
if (isset($_GET['run_test'])) {
    $test = new MobileTabsTest();
    $test->runAllTests();
} else {
    echo '<h2>移动端标签页功能测试</h2>';
    echo '<p><a href="?run_test=1" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">开始测试</a></p>';
    echo '<p>此测试将验证标签页功能的各个组件是否正常工作。</p>';
}
?>
