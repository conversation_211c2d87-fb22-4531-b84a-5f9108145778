<?php
/**
 * 移动端组件API控制器
 * 创建时间：2025-01-03
 * 功能：为移动端组件提供数据接口
 */

class componentClassAction extends ActionNot
{
    public function __construct()
    {
        parent::__construct();
        // 优先使用配置文件模式，如果不存在则使用数据库模式
        $configFile = dirname(__FILE__) . '/../config/mobileTabsConfig.php';
        if (file_exists($configFile)) {
            // 手动加载配置模型
            require_once(dirname(__FILE__) . '/../model/mobileTabConfigModel.php');
            $this->model = new mobileTabConfigClassModel();
        } else {
            $this->model = m('mobileTab');
        }
    }

    /**
     * 获取移动端标签页配置
     */
    public function getMobileTabsAction()
    {
        $this->display = false;

        // 设置JSON响应头
        header('Content-Type: application/json; charset=utf-8');

        $categoryCode = $this->rock->get('category_code');
        $relationType = $this->rock->get('relation_type');
        $relationId = $this->rock->get('relation_id');
        $relationCode = $this->rock->get('relation_code');

        try {
            $tabs = [];

            if (!empty($categoryCode)) {
                // 根据分类代码获取标签页
                $tabs = $this->model->getTabsByCategory(0, $categoryCode);
            } elseif (!empty($relationType)) {
                // 根据关联关系获取标签页
                $tabs = $this->model->getTabsByRelation($relationType, $relationId, $relationCode);
            } else {
                // 获取默认标签页
                $tabs = $this->model->getTabsByCategory(1); // 通用分类
            }

            // 过滤权限和状态
            $filteredTabs = [];
            foreach ($tabs as $tab) {
                if ($tab['status'] == 1 && $this->checkTabPermission($tab)) {
                    $filteredTabs[] = $tab;
                }
            }

            echo json_encode([
                'success' => true,
                'data' => $filteredTabs,
                'message' => '获取成功'
            ], JSON_UNESCAPED_UNICODE);

        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'data' => [],
                'message' => '获取标签页失败：' . $e->getMessage()
            ], JSON_UNESCAPED_UNICODE);
        }
    }

    /**
     * 获取标签页内容
     */
    public function getTabContentAction()
    {
        $this->display = false;
        
        $tabId = intval($this->rock->get('tab_id'));
        $params = $this->rock->get('params', []);
        
        if (is_string($params)) {
            $params = json_decode($params, true) ?: [];
        }
        
        try {
            $content = $this->model->getTabContent($tabId, $params);
            echo $content;
        } catch (Exception $e) {
            echo '<div class="error">加载内容失败：' . $e->getMessage() . '</div>';
        }
    }

    /**
     * 记录标签页访问统计
     */
    public function recordTabAccessAction()
    {
        $this->display = false;
        
        $tabId = intval($this->rock->get('tab_id'));
        $timestamp = $this->rock->get('timestamp');
        
        if ($tabId <= 0) {
            echo json_encode(['success' => false, 'message' => '参数错误']);
            return;
        }
        
        try {
            // 计算停留时间（简化处理）
            $duration = 0;
            if ($timestamp) {
                $currentTime = time() * 1000;
                $duration = max(0, ($currentTime - intval($timestamp)) / 1000);
            }
            
            $result = $this->model->recordTabAccess($tabId, $this->adminid, $duration);
            
            echo json_encode([
                'success' => $result,
                'message' => $result ? '记录成功' : '记录失败'
            ]);
            
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => '记录失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取客户相关标签页内容
     */
    public function getCustomerTabsAction()
    {
        $this->display = false;

        $customerId = intval($this->rock->get('customer_id'));
        $tabType = $this->rock->get('tab_type', 'contact_record');

        // 如果没有customer_id，尝试从id参数获取
        if ($customerId <= 0) {
            $customerId = intval($this->rock->get('id'));
        }

        if ($customerId <= 0) {
            echo '<div class="error">客户ID不能为空</div>';
            return;
        }

        try {
            $content = '';

            switch ($tabType) {
                case 'contact_record':
                    $content = $this->getCustomerContactRecord($customerId);
                    break;
                case 'sales_opportunity':
                    $content = $this->getCustomerSalesOpportunity($customerId);
                    break;
                case 'contract_info':
                    $content = $this->getCustomerContractInfo($customerId);
                    break;
                case 'service_record':
                    $content = $this->getCustomerServiceRecord($customerId);
                    break;
                default:
                    $content = '<div class="no-data">暂无数据</div>';
            }

            echo $content;

        } catch (Exception $e) {
            echo '<div class="error">加载失败：' . $e->getMessage() . '</div>';
        }
    }

    /**
     * 检查标签页权限
     * @param array $tab 标签页配置
     * @return bool
     */
    private function checkTabPermission($tab)
    {
        // 如果没有设置权限，默认允许访问
        if (empty($tab['permissions'])) {
            return true;
        }
        
        // 这里可以根据实际需求实现权限检查逻辑
        // 例如检查用户角色、部门等
        $permissions = explode(',', $tab['permissions']);
        
        // 简化处理：检查用户是否有相应权限
        foreach ($permissions as $permission) {
            $permission = trim($permission);
            if ($this->checkUserPermission($permission)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 检查用户权限
     * @param string $permission 权限标识
     * @return bool
     */
    private function checkUserPermission($permission)
    {
        // 这里应该调用系统的权限检查方法
        // 简化处理，返回true
        return true;
    }

    /**
     * 获取客户联系记录
     * @param int $customerId 客户ID
     * @return string
     */
    private function getCustomerContactRecord($customerId)
    {
        try {
            // 包含客户模块控制器文件
            require_once(dirname(__FILE__) . '/../../flow/input/mode_customerAction.php');

            // 调用现有的客户模块获取联系人数据
            $customerAction = new mode_customerClassAction();
            $customerAction->rock = $this->rock;

            // 设置参数
            $_GET['custid'] = $customerId;
            $_GET['ind'] = 8; // 联系人选项卡索引

            // 调用现有方法获取数据
            $content = $customerAction->getothernrAjax();

            return $content ? $content : '<div class="no-data">暂无联系记录</div>';

        } catch (Exception $e) {
            return '<div class="error">加载联系记录失败</div>';
        }
    }

    /**
     * 获取客户销售机会
     * @param int $customerId 客户ID
     * @return string
     */
    private function getCustomerSalesOpportunity($customerId)
    {
        try {
            // 包含客户模块控制器文件
            require_once(dirname(__FILE__) . '/../../flow/input/mode_customerAction.php');

            // 调用现有的客户模块获取销售机会数据
            $customerAction = new mode_customerClassAction();
            $customerAction->rock = $this->rock;

            // 设置参数
            $_GET['custid'] = $customerId;
            $_GET['ind'] = 1; // 销售机会选项卡索引

            // 调用现有方法获取数据
            $content = $customerAction->getothernrAjax();

            return $content ? $content : '<div class="no-data">暂无销售机会</div>';

        } catch (Exception $e) {
            return '<div class="error">加载销售机会失败</div>';
        }
    }

    /**
     * 获取客户合同信息
     * @param int $customerId 客户ID
     * @return string
     */
    private function getCustomerContractInfo($customerId)
    {
        try {
            // 包含客户模块控制器文件
            require_once(dirname(__FILE__) . '/../../flow/input/mode_customerAction.php');

            // 调用现有的客户模块获取合同数据
            $customerAction = new mode_customerClassAction();
            $customerAction->rock = $this->rock;

            // 设置参数
            $_GET['custid'] = $customerId;
            $_GET['ind'] = 2; // 客户合同选项卡索引

            // 调用现有方法获取数据
            $content = $customerAction->getothernrAjax();

            return $content ? $content : '<div class="no-data">暂无合同信息</div>';

        } catch (Exception $e) {
            return '<div class="error">加载合同信息失败</div>';
        }
    }

    /**
     * 获取客户服务记录
     * @param int $customerId 客户ID
     * @return string
     */
    private function getCustomerServiceRecord($customerId)
    {
        try {
            // 包含客户模块控制器文件
            require_once(dirname(__FILE__) . '/../../flow/input/mode_customerAction.php');

            // 调用现有的客户模块获取售后服务数据
            $customerAction = new mode_customerClassAction();
            $customerAction->rock = $this->rock;

            // 设置参数
            $_GET['custid'] = $customerId;
            $_GET['ind'] = 5; // 售后单选项卡索引

            // 调用现有方法获取数据
            $content = $customerAction->getothernrAjax();

            return $content ? $content : '<div class="no-data">暂无服务记录</div>';

        } catch (Exception $e) {
            return '<div class="error">加载服务记录失败</div>';
        }
    }
}
