<?php
/**
 * 移动端组件API控制器
 * 创建时间：2025-01-03
 * 功能：为移动端组件提供数据接口
 */

class componentClassAction extends ActionNot
{
    public function __construct()
    {
        parent::__construct();
        // 优先使用配置文件模式，如果不存在则使用数据库模式
        $configFile = dirname(__FILE__) . '/../config/mobileTabsConfig.php';
        if (file_exists($configFile)) {
            // 手动加载配置模型
            $modelFile = dirname(__FILE__) . '/../../model/mobileTabConfigModel.php';
            if (file_exists($modelFile)) {
                require_once($modelFile);
                $this->model = new mobileTabConfigClassModel();
            } else {
                $this->model = null;
            }
        } else {
            $this->model = m('mobileTab');
        }
    }

    /**
     * 获取移动端标签页配置
     */
    public function getMobileTabsAction()
    {
        $this->display = false;

        // 设置JSON响应头
        header('Content-Type: application/json; charset=utf-8');

        try {
            // 直接从配置文件读取
            $configFile = dirname(__FILE__) . '/../../config/mobileTabsConfig.php';

            // 调试信息
            $debugInfo = [
                'current_dir' => dirname(__FILE__),
                'config_path' => $configFile,
                'file_exists' => file_exists($configFile),
                'real_path' => realpath($configFile)
            ];

            if (!file_exists($configFile)) {
                echo json_encode([
                    'success' => false,
                    'data' => [],
                    'message' => '配置文件不存在',
                    'debug' => $debugInfo
                ], JSON_UNESCAPED_UNICODE);
                return;
            }

            // 确保HOST常量已定义
            if (!defined('HOST')) {
                define('HOST', true);
            }

            $config = include $configFile;
            $categoryCode = $this->rock->get('category_code', 'customer');

            if (!is_array($config)) {
                echo json_encode([
                    'success' => false,
                    'data' => [],
                    'message' => '配置文件格式错误',
                    'debug' => ['config_type' => gettype($config)]
                ], JSON_UNESCAPED_UNICODE);
                return;
            }

            if (!isset($config['tabs'][$categoryCode])) {
                echo json_encode([
                    'success' => false,
                    'data' => [],
                    'message' => '分类不存在：' . $categoryCode,
                    'debug' => ['available_categories' => array_keys($config['tabs'] ?? [])]
                ], JSON_UNESCAPED_UNICODE);
                return;
            }

            $tabs = $config['tabs'][$categoryCode];

            // 过滤启用的标签页
            $filteredTabs = [];
            foreach ($tabs as $index => $tab) {
                if ($tab['status'] == 1) {
                    $tab['id'] = $index;
                    $tab['category_code'] = $categoryCode;
                    $filteredTabs[] = $tab;
                }
            }

            echo json_encode([
                'success' => true,
                'data' => $filteredTabs,
                'message' => '获取成功'
            ], JSON_UNESCAPED_UNICODE);

        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'data' => [],
                'message' => '获取标签页失败：' . $e->getMessage()
            ], JSON_UNESCAPED_UNICODE);
        }
    }

    /**
     * 获取标签页内容
     */
    public function getTabContentAction()
    {
        $this->display = false;
        
        $tabId = intval($this->rock->get('tab_id'));
        $params = $this->rock->get('params', []);
        
        if (is_string($params)) {
            $params = json_decode($params, true) ?: [];
        }
        
        try {
            $content = $this->model->getTabContent($tabId, $params);
            echo $content;
        } catch (Exception $e) {
            echo '<div class="error">加载内容失败：' . $e->getMessage() . '</div>';
        }
    }

    /**
     * 记录标签页访问统计
     */
    public function recordTabAccessAction()
    {
        $this->display = false;
        
        $tabId = intval($this->rock->get('tab_id'));
        $timestamp = $this->rock->get('timestamp');
        
        if ($tabId <= 0) {
            echo json_encode(['success' => false, 'message' => '参数错误']);
            return;
        }
        
        try {
            // 计算停留时间（简化处理）
            $duration = 0;
            if ($timestamp) {
                $currentTime = time() * 1000;
                $duration = max(0, ($currentTime - intval($timestamp)) / 1000);
            }
            
            $result = $this->model->recordTabAccess($tabId, $this->adminid, $duration);
            
            echo json_encode([
                'success' => $result,
                'message' => $result ? '记录成功' : '记录失败'
            ]);
            
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => '记录失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取客户相关标签页内容
     */
    public function getCustomerTabsAction()
    {
        $this->display = false;

        $customerId = intval($this->rock->get('customer_id'));
        $tabType = $this->rock->get('tab_type', 'contact_record');

        // 如果没有customer_id，尝试从id参数获取
        if ($customerId <= 0) {
            $customerId = intval($this->rock->get('id'));
        }

        if ($customerId <= 0) {
            echo '<div class="error">客户ID不能为空</div>';
            return;
        }

        try {
            // 尝试调用现有的客户模块方法
            $customerActionFile = dirname(__FILE__) . '/../../flow/input/mode_customerAction.php';
            if (file_exists($customerActionFile)) {
                require_once($customerActionFile);

                if (class_exists('mode_customerClassAction')) {
                    $customerAction = new mode_customerClassAction();
                    $customerAction->rock = $this->rock;
                    $customerAction->adminid = $this->adminid;

                    // 调用getothernrAjax方法
                    $index = $this->getTabIndex($tabType);
                    if ($index !== false) {
                        $_GET['id'] = $customerId;
                        $_GET['lx'] = $index;

                        ob_start();
                        $customerAction->getothernrAjax();
                        $content = ob_get_clean();

                        echo $content;
                        return;
                    }
                }
            }

            // 处理特殊的标签页类型
            if ($tabType === 'sales_stats') {
                echo $this->getCustomerSalesStats($customerId);
                return;
            }

            // 如果无法调用现有方法，返回简单信息
            echo '<div class="info">暂无' . $this->getTabTypeName($tabType) . '数据</div>';

        } catch (Exception $e) {
            echo '<div class="error">加载失败：' . $e->getMessage() . '</div>';
        }
    }

    /**
     * 获取标签页类型对应的索引
     */
    private function getTabIndex($tabType)
    {
        $mapping = [
            'contact_record' => 8,      // 联系记录
            'sales_opportunity' => 1,   // 销售机会
            'contract_info' => 2,       // 合同信息
            'service_record' => 5,      // 服务记录
            'follow_plan' => 7,         // 跟进计划
            'payment_record' => 3,      // 收款记录
            'customer_timeline' => 9    // 客户动态
        ];

        return isset($mapping[$tabType]) ? $mapping[$tabType] : false;
    }

    /**
     * 获取标签页类型名称
     */
    private function getTabTypeName($tabType)
    {
        $names = [
            'contact_record' => '联系记录',
            'sales_opportunity' => '销售机会',
            'contract_info' => '合同信息',
            'service_record' => '服务记录',
            'follow_plan' => '跟进计划',
            'payment_record' => '收款记录',
            'sales_stats' => '销售统计',
            'customer_timeline' => '客户动态'
        ];

        return isset($names[$tabType]) ? $names[$tabType] : '相关';
    }

    /**
     * 获取客户销售统计
     * @param int $customerId 客户ID
     * @return string
     */
    private function getCustomerSalesStats($customerId)
    {
        try {
            global $db;

            if (!$db) {
                return '<div class="error">数据库连接失败</div>';
            }

            // 获取合同统计
            $contractStats = $db->getone("
                SELECT
                    COUNT(*) as total_contracts,
                    COALESCE(SUM(money), 0) as total_amount,
                    COALESCE(SUM(CASE WHEN status = '已完成' THEN money ELSE 0 END), 0) as completed_amount
                FROM {$db->prefix}custract
                WHERE custid = {$customerId} AND status != '已删除'
            ");

            // 获取收款统计
            $paymentStats = $db->getone("
                SELECT
                    COALESCE(SUM(money), 0) as paid_amount
                FROM {$db->prefix}custxiao
                WHERE custid = {$customerId} AND ispay = '1'
            ");

            $totalContracts = $contractStats['total_contracts'] ?? 0;
            $totalAmount = $contractStats['total_amount'] ?? 0;
            $completedAmount = $contractStats['completed_amount'] ?? 0;
            $paidAmount = $paymentStats['paid_amount'] ?? 0;
            $unpaidAmount = $totalAmount - $paidAmount;
            $paymentProgress = $totalAmount > 0 ? round(($paidAmount / $totalAmount) * 100, 1) : 0;

            // 生成HTML内容
            $html = '
                <div class="stats-section">
                    <div class="section-header">
                        <h4><i class="icon-bar-chart"></i> 销售统计概览</h4>
                    </div>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-value">' . $totalContracts . '</div>
                            <div class="stat-label">合同总数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">¥' . number_format($totalAmount, 2) . '</div>
                            <div class="stat-label">合同总额</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">¥' . number_format($paidAmount, 2) . '</div>
                            <div class="stat-label">已收金额</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">¥' . number_format($unpaidAmount, 2) . '</div>
                            <div class="stat-label">待收金额</div>
                        </div>
                    </div>
                    <div class="progress-section">
                        <div class="progress-item">
                            <div class="progress-label">回款进度</div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ' . $paymentProgress . '%"></div>
                            </div>
                            <div class="progress-text">' . $paymentProgress . '%</div>
                        </div>
                    </div>
                </div>
            ';

            return $html;

        } catch (Exception $e) {
            return '<div class="error">统计数据加载失败：' . $e->getMessage() . '</div>';
        }
    }

    /**
     * 检查标签页权限
     * @param array $tab 标签页配置
     * @return bool
     */
    private function checkTabPermission($tab)
    {
        // 如果没有设置权限，默认允许访问
        if (empty($tab['permissions'])) {
            return true;
        }
        
        // 这里可以根据实际需求实现权限检查逻辑
        // 例如检查用户角色、部门等
        $permissions = explode(',', $tab['permissions']);
        
        // 简化处理：检查用户是否有相应权限
        foreach ($permissions as $permission) {
            $permission = trim($permission);
            if ($this->checkUserPermission($permission)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 检查用户权限
     * @param string $permission 权限标识
     * @return bool
     */
    private function checkUserPermission($permission)
    {
        // 这里应该调用系统的权限检查方法
        // 简化处理，返回true
        return true;
    }






}
