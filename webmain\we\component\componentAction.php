<?php
/**
 * 移动端组件API控制器
 * 创建时间：2025-01-03
 * 功能：为移动端组件提供数据接口
 */

class componentClassAction extends ActionNot
{
    public function __construct()
    {
        parent::__construct();
        // 优先使用配置文件模式，如果不存在则使用数据库模式
        if (file_exists(dirname(__FILE__) . '/../config/mobileTabsConfig.php')) {
            $this->model = m('mobileTabConfig');
        } else {
            $this->model = m('mobileTab');
        }
    }

    /**
     * 获取移动端标签页配置
     */
    public function getMobileTabsAction()
    {
        $this->display = false;
        
        $categoryCode = $this->rock->get('category_code');
        $relationType = $this->rock->get('relation_type');
        $relationId = $this->rock->get('relation_id');
        $relationCode = $this->rock->get('relation_code');
        
        try {
            $tabs = [];
            
            if (!empty($categoryCode)) {
                // 根据分类代码获取标签页
                $tabs = $this->model->getTabsByCategory(0, $categoryCode);
            } elseif (!empty($relationType)) {
                // 根据关联关系获取标签页
                $tabs = $this->model->getTabsByRelation($relationType, $relationId, $relationCode);
            } else {
                // 获取默认标签页
                $tabs = $this->model->getTabsByCategory(1); // 通用分类
            }
            
            // 过滤权限和状态
            $filteredTabs = [];
            foreach ($tabs as $tab) {
                if ($tab['status'] == 1 && $this->checkTabPermission($tab)) {
                    $filteredTabs[] = $tab;
                }
            }
            
            echo json_encode([
                'success' => true,
                'data' => $filteredTabs,
                'message' => '获取成功'
            ]);
            
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'data' => [],
                'message' => '获取标签页失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取标签页内容
     */
    public function getTabContentAction()
    {
        $this->display = false;
        
        $tabId = intval($this->rock->get('tab_id'));
        $params = $this->rock->get('params', []);
        
        if (is_string($params)) {
            $params = json_decode($params, true) ?: [];
        }
        
        try {
            $content = $this->model->getTabContent($tabId, $params);
            echo $content;
        } catch (Exception $e) {
            echo '<div class="error">加载内容失败：' . $e->getMessage() . '</div>';
        }
    }

    /**
     * 记录标签页访问统计
     */
    public function recordTabAccessAction()
    {
        $this->display = false;
        
        $tabId = intval($this->rock->get('tab_id'));
        $timestamp = $this->rock->get('timestamp');
        
        if ($tabId <= 0) {
            echo json_encode(['success' => false, 'message' => '参数错误']);
            return;
        }
        
        try {
            // 计算停留时间（简化处理）
            $duration = 0;
            if ($timestamp) {
                $currentTime = time() * 1000;
                $duration = max(0, ($currentTime - intval($timestamp)) / 1000);
            }
            
            $result = $this->model->recordTabAccess($tabId, $this->adminid, $duration);
            
            echo json_encode([
                'success' => $result,
                'message' => $result ? '记录成功' : '记录失败'
            ]);
            
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => '记录失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取客户相关标签页内容
     */
    public function getCustomerTabsAction()
    {
        $this->display = false;
        
        $customerId = intval($this->rock->get('customer_id'));
        $tabType = $this->rock->get('tab_type', 'contact_record');
        
        if ($customerId <= 0) {
            echo json_encode(['success' => false, 'message' => '客户ID不能为空']);
            return;
        }
        
        try {
            $content = '';
            
            switch ($tabType) {
                case 'contact_record':
                    $content = $this->getCustomerContactRecord($customerId);
                    break;
                case 'sales_opportunity':
                    $content = $this->getCustomerSalesOpportunity($customerId);
                    break;
                case 'contract_info':
                    $content = $this->getCustomerContractInfo($customerId);
                    break;
                case 'service_record':
                    $content = $this->getCustomerServiceRecord($customerId);
                    break;
                default:
                    $content = '<div class="no-data">暂无数据</div>';
            }
            
            echo $content;
            
        } catch (Exception $e) {
            echo '<div class="error">加载失败：' . $e->getMessage() . '</div>';
        }
    }

    /**
     * 检查标签页权限
     * @param array $tab 标签页配置
     * @return bool
     */
    private function checkTabPermission($tab)
    {
        // 如果没有设置权限，默认允许访问
        if (empty($tab['permissions'])) {
            return true;
        }
        
        // 这里可以根据实际需求实现权限检查逻辑
        // 例如检查用户角色、部门等
        $permissions = explode(',', $tab['permissions']);
        
        // 简化处理：检查用户是否有相应权限
        foreach ($permissions as $permission) {
            $permission = trim($permission);
            if ($this->checkUserPermission($permission)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 检查用户权限
     * @param string $permission 权限标识
     * @return bool
     */
    private function checkUserPermission($permission)
    {
        // 这里应该调用系统的权限检查方法
        // 简化处理，返回true
        return true;
    }

    /**
     * 获取客户联系记录
     * @param int $customerId 客户ID
     * @return string
     */
    private function getCustomerContactRecord($customerId)
    {
        // 这里应该调用相应的模型获取数据
        // 简化处理，返回示例HTML
        return '
        <div class="contact-records">
            <div class="record-item">
                <div class="record-time">2025-01-03 10:30</div>
                <div class="record-content">电话沟通项目需求</div>
                <div class="record-person">联系人：张经理</div>
            </div>
            <div class="record-item">
                <div class="record-time">2025-01-02 14:20</div>
                <div class="record-content">现场拜访，了解具体情况</div>
                <div class="record-person">联系人：李总</div>
            </div>
        </div>';
    }

    /**
     * 获取客户销售机会
     * @param int $customerId 客户ID
     * @return string
     */
    private function getCustomerSalesOpportunity($customerId)
    {
        return '
        <div class="sales-opportunities">
            <div class="opportunity-item">
                <div class="opportunity-title">ERP系统升级项目</div>
                <div class="opportunity-amount">预计金额：50万</div>
                <div class="opportunity-stage">当前阶段：需求确认</div>
                <div class="opportunity-probability">成功概率：70%</div>
            </div>
            <div class="opportunity-item">
                <div class="opportunity-title">移动办公平台</div>
                <div class="opportunity-amount">预计金额：30万</div>
                <div class="opportunity-stage">当前阶段：方案设计</div>
                <div class="opportunity-probability">成功概率：50%</div>
            </div>
        </div>';
    }

    /**
     * 获取客户合同信息
     * @param int $customerId 客户ID
     * @return string
     */
    private function getCustomerContractInfo($customerId)
    {
        return '
        <div class="contracts">
            <div class="contract-item">
                <div class="contract-title">软件开发服务合同</div>
                <div class="contract-amount">合同金额：80万</div>
                <div class="contract-date">签订日期：2024-12-15</div>
                <div class="contract-status">状态：执行中</div>
            </div>
            <div class="contract-item">
                <div class="contract-title">技术支持服务合同</div>
                <div class="contract-amount">合同金额：20万</div>
                <div class="contract-date">签订日期：2024-11-20</div>
                <div class="contract-status">状态：已完成</div>
            </div>
        </div>';
    }

    /**
     * 获取客户服务记录
     * @param int $customerId 客户ID
     * @return string
     */
    private function getCustomerServiceRecord($customerId)
    {
        return '
        <div class="service-records">
            <div class="service-item">
                <div class="service-title">系统故障处理</div>
                <div class="service-date">2025-01-02</div>
                <div class="service-status">已解决</div>
                <div class="service-description">数据库连接异常，已修复</div>
            </div>
            <div class="service-item">
                <div class="service-title">功能优化需求</div>
                <div class="service-date">2024-12-28</div>
                <div class="service-status">处理中</div>
                <div class="service-description">用户界面优化建议</div>
            </div>
        </div>';
    }
}
