<?php
/**
 * 移动端组件API控制器
 * 创建时间：2025-01-03
 * 功能：为移动端组件提供数据接口
 */

class componentClassAction extends ActionNot
{
    public function __construct()
    {
        parent::__construct();
        // 优先使用配置文件模式，如果不存在则使用数据库模式
        $configFile = dirname(__FILE__) . '/../config/mobileTabsConfig.php';
        if (file_exists($configFile)) {
            // 手动加载配置模型
            $modelFile = dirname(__FILE__) . '/../../model/mobileTabConfigModel.php';
            if (file_exists($modelFile)) {
                require_once($modelFile);
                $this->model = new mobileTabConfigClassModel();
            } else {
                $this->model = null;
            }
        } else {
            $this->model = m('mobileTab');
        }
    }

    /**
     * 获取移动端标签页配置
     */
    public function getMobileTabsAction()
    {
        $this->display = false;

        // 设置JSON响应头
        header('Content-Type: application/json; charset=utf-8');

        try {
            // 直接从配置文件读取
            $configFile = dirname(__FILE__) . '/../../config/mobileTabsConfig.php';

            // 调试信息
            $debugInfo = [
                'current_dir' => dirname(__FILE__),
                'config_path' => $configFile,
                'file_exists' => file_exists($configFile),
                'real_path' => realpath($configFile)
            ];

            if (!file_exists($configFile)) {
                echo json_encode([
                    'success' => false,
                    'data' => [],
                    'message' => '配置文件不存在',
                    'debug' => $debugInfo
                ], JSON_UNESCAPED_UNICODE);
                return;
            }

            // 确保HOST常量已定义
            if (!defined('HOST')) {
                define('HOST', true);
            }

            $config = include $configFile;
            $categoryCode = $this->rock->get('category_code', 'customer');

            if (!is_array($config)) {
                echo json_encode([
                    'success' => false,
                    'data' => [],
                    'message' => '配置文件格式错误',
                    'debug' => ['config_type' => gettype($config)]
                ], JSON_UNESCAPED_UNICODE);
                return;
            }

            if (!isset($config['tabs'][$categoryCode])) {
                echo json_encode([
                    'success' => false,
                    'data' => [],
                    'message' => '分类不存在：' . $categoryCode,
                    'debug' => ['available_categories' => array_keys($config['tabs'] ?? [])]
                ], JSON_UNESCAPED_UNICODE);
                return;
            }

            $tabs = $config['tabs'][$categoryCode];

            // 过滤启用的标签页
            $filteredTabs = [];
            foreach ($tabs as $index => $tab) {
                if ($tab['status'] == 1) {
                    $tab['id'] = $index;
                    $tab['category_code'] = $categoryCode;
                    $filteredTabs[] = $tab;
                }
            }

            echo json_encode([
                'success' => true,
                'data' => $filteredTabs,
                'message' => '获取成功'
            ], JSON_UNESCAPED_UNICODE);

        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'data' => [],
                'message' => '获取标签页失败：' . $e->getMessage()
            ], JSON_UNESCAPED_UNICODE);
        }
    }

    /**
     * 获取标签页内容
     */
    public function getTabContentAction()
    {
        $this->display = false;
        
        $tabId = intval($this->rock->get('tab_id'));
        $params = $this->rock->get('params', []);
        
        if (is_string($params)) {
            $params = json_decode($params, true) ?: [];
        }
        
        try {
            $content = $this->model->getTabContent($tabId, $params);
            echo $content;
        } catch (Exception $e) {
            echo '<div class="error">加载内容失败：' . $e->getMessage() . '</div>';
        }
    }

    /**
     * 记录标签页访问统计
     */
    public function recordTabAccessAction()
    {
        $this->display = false;
        
        $tabId = intval($this->rock->get('tab_id'));
        $timestamp = $this->rock->get('timestamp');
        
        if ($tabId <= 0) {
            echo json_encode(['success' => false, 'message' => '参数错误']);
            return;
        }
        
        try {
            // 计算停留时间（简化处理）
            $duration = 0;
            if ($timestamp) {
                $currentTime = time() * 1000;
                $duration = max(0, ($currentTime - intval($timestamp)) / 1000);
            }
            
            $result = $this->model->recordTabAccess($tabId, $this->adminid, $duration);
            
            echo json_encode([
                'success' => $result,
                'message' => $result ? '记录成功' : '记录失败'
            ]);
            
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => '记录失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取客户相关标签页内容
     */
    public function getCustomerTabsAction()
    {
        $this->display = false;

        $customerId = intval($this->rock->get('customer_id'));
        $tabType = $this->rock->get('tab_type', 'contact_record');

        // 如果没有customer_id，尝试从id参数获取
        if ($customerId <= 0) {
            $customerId = intval($this->rock->get('id'));
        }

        if ($customerId <= 0) {
            echo '<div class="error">客户ID不能为空</div>';
            return;
        }

        try {
            // 直接从数据库获取数据，避免复杂的Action依赖
            echo $this->getCustomerDataByType($customerId, $tabType);
            return;

            // 处理特殊的标签页类型
            if ($tabType === 'sales_stats') {
                echo $this->getCustomerSalesStats($customerId);
                return;
            }

            // 如果无法调用现有方法，返回简单信息
            echo '<div class="info">暂无' . $this->getTabTypeName($tabType) . '数据</div>';

        } catch (Exception $e) {
            echo '<div class="error">加载失败：' . $e->getMessage() . '</div>';
        }
    }

    /**
     * 获取标签页类型对应的索引（与现有系统保持一致）
     */
    private function getTabIndex($tabType)
    {
        $mapping = [
            'contact_record' => 8,      // 联系人（索引8）
            'sales_opportunity' => 1,   // 销售机会（索引1）
            'contract_info' => 2,       // 客户合同（索引2）
            'service_record' => 5,      // 售后单（索引5）
            'follow_plan' => 7,         // 跟进计划（索引7）
            'payment_record' => 3,      // 收款单（索引3）
            'payment_out' => 4,         // 付款单（索引4）
            'sales_order' => 6          // 销售单（索引6）
        ];

        return isset($mapping[$tabType]) ? $mapping[$tabType] : false;
    }

    /**
     * 获取标签页类型名称
     */
    private function getTabTypeName($tabType)
    {
        $names = [
            'contact_record' => '联系记录',
            'sales_opportunity' => '销售机会',
            'contract_info' => '合同信息',
            'service_record' => '服务记录',
            'follow_plan' => '跟进计划',
            'payment_record' => '收款记录',
            'sales_stats' => '销售统计',
            'customer_timeline' => '客户动态'
        ];

        return isset($names[$tabType]) ? $names[$tabType] : '相关';
    }

    /**
     * 直接从数据库获取客户数据
     * @param int $customerId 客户ID
     * @param string $tabType 标签页类型
     * @return string
     */
    private function getCustomerDataByType($customerId, $tabType)
    {
        global $db;

        if (!$db) {
            return '<div class="error">数据库连接失败</div>';
        }

        try {
            switch ($tabType) {
                case 'contact_record':
                    return $this->getContactRecords($customerId);

                case 'sales_opportunity':
                    return $this->getSalesOpportunities($customerId);

                case 'contract_info':
                    return $this->getContractInfo($customerId);

                case 'service_record':
                    return $this->getServiceRecords($customerId);

                case 'follow_plan':
                    return $this->getFollowPlans($customerId);

                case 'payment_record':
                    return $this->getPaymentRecords($customerId);

                case 'sales_stats':
                    return $this->getCustomerSalesStats($customerId);

                case 'customer_timeline':
                    return $this->getCustomerTimeline($customerId);

                default:
                    return '<div class="no-data">暂无' . $this->getTabTypeName($tabType) . '数据</div>';
            }
        } catch (Exception $e) {
            return '<div class="error">数据加载失败：' . $e->getMessage() . '</div>';
        }
    }

    /**
     * 获取联系记录
     */
    private function getContactRecords($customerId)
    {
        global $db;

        try {
            $sql = "SELECT * FROM " . PREFIX . "custcontrel WHERE customer_id = {$customerId} ORDER BY id DESC LIMIT 10";
            $contacts = $db->getall($sql);

            if (empty($contacts)) {
                return '<div class="no-data">暂无联系记录</div>';
            }

            $html = '<div class="contact-records">';
            foreach ($contacts as $contact) {
                $html .= '<div class="record-item">';
                $html .= '<div class="record-time">' . htmlspecialchars($contact['optdt'] ?? date('Y-m-d H:i:s')) . '</div>';
                $html .= '<div class="record-content">' . htmlspecialchars($contact['content'] ?? '联系记录') . '</div>';
                $html .= '<div class="record-person">联系人：' . htmlspecialchars($contact['linkname'] ?? '未知') . '</div>';
                $html .= '</div>';
            }
            $html .= '</div>';

            return $html;
        } catch (Exception $e) {
            return '<div class="no-data">暂无联系记录（表可能不存在）</div>';
        }
    }

    /**
     * 获取销售机会
     */
    private function getSalesOpportunities($customerId)
    {
        global $db;

        try {
            $sql = "SELECT * FROM " . PREFIX . "custxiaoji WHERE custid = {$customerId} ORDER BY id DESC LIMIT 10";
            $opportunities = $db->getall($sql);

            if (empty($opportunities)) {
                return '<div class="no-data">暂无销售机会</div>';
            }

            $html = '<div class="sales-opportunities">';
            foreach ($opportunities as $opp) {
                $html .= '<div class="opportunity-item">';
                $html .= '<div class="opportunity-title">' . htmlspecialchars($opp['name'] ?? '销售机会') . '</div>';
                $html .= '<div class="opportunity-amount">预计金额：¥' . number_format($opp['money'] ?? 0, 2) . '</div>';
                $html .= '<div class="opportunity-stage">当前阶段：' . htmlspecialchars($opp['status'] ?? '进行中') . '</div>';
                $html .= '</div>';
            }
            $html .= '</div>';

            return $html;
        } catch (Exception $e) {
            return '<div class="no-data">暂无销售机会（表可能不存在）</div>';
        }
    }

    /**
     * 获取合同信息
     */
    private function getContractInfo($customerId)
    {
        global $db;

        try {
            $sql = "SELECT * FROM " . PREFIX . "custract WHERE custid = {$customerId} AND status != '已删除' ORDER BY id DESC LIMIT 10";
            $contracts = $db->getall($sql);

            if (empty($contracts)) {
                return '<div class="no-data">暂无合同信息</div>';
            }

            $html = '<div class="contracts">';
            foreach ($contracts as $contract) {
                $html .= '<div class="contract-item">';
                $html .= '<div class="contract-title">' . htmlspecialchars($contract['name'] ?? '合同') . '</div>';
                $html .= '<div class="contract-amount">合同金额：¥' . number_format($contract['money'] ?? 0, 2) . '</div>';
                $html .= '<div class="contract-date">签订日期：' . htmlspecialchars($contract['optdt'] ?? '') . ' <span class="contract-status">' . htmlspecialchars($contract['status'] ?? '') . '</span></div>';
                $html .= '</div>';
            }
            $html .= '</div>';

            return $html;
        } catch (Exception $e) {
            return '<div class="no-data">暂无合同信息（表可能不存在）</div>';
        }
    }

    /**
     * 获取服务记录
     */
    private function getServiceRecords($customerId)
    {
        global $db;

        try {
            $sql = "SELECT * FROM " . PREFIX . "shouhou WHERE custid = {$customerId} ORDER BY id DESC LIMIT 10";
            $services = $db->getall($sql);

            if (empty($services)) {
                return '<div class="no-data">暂无服务记录</div>';
            }

            $html = '<div class="service-records">';
            foreach ($services as $service) {
                $html .= '<div class="service-item">';
                $html .= '<div class="service-title">' . htmlspecialchars($service['name'] ?? '服务记录') . '</div>';
                $html .= '<div class="service-description">' . htmlspecialchars($service['content'] ?? '') . '</div>';
                $html .= '<div class="service-date">服务日期：' . htmlspecialchars($service['optdt'] ?? '') . ' <span class="service-status">' . htmlspecialchars($service['status'] ?? '') . '</span></div>';
                $html .= '</div>';
            }
            $html .= '</div>';

            return $html;
        } catch (Exception $e) {
            return '<div class="no-data">暂无服务记录（表可能不存在）</div>';
        }
    }

    /**
     * 获取跟进计划
     */
    private function getFollowPlans($customerId)
    {
        global $db;

        try {
            $sql = "SELECT * FROM " . PREFIX . "custplan WHERE custid = {$customerId} ORDER BY id DESC LIMIT 10";
            $plans = $db->getall($sql);

            if (empty($plans)) {
                return '<div class="no-data">暂无跟进计划</div>';
            }

            $html = '<div class="follow-plans">';
            foreach ($plans as $plan) {
                $html .= '<div class="plan-item">';
                $html .= '<div class="plan-title">' . htmlspecialchars($plan['title'] ?? '跟进计划') . '</div>';
                $html .= '<div class="plan-content">' . htmlspecialchars($plan['content'] ?? '') . '</div>';
                $html .= '<div class="plan-date">计划时间：' . htmlspecialchars($plan['plantime'] ?? '') . '</div>';
                $html .= '</div>';
            }
            $html .= '</div>';

            return $html;
        } catch (Exception $e) {
            return '<div class="no-data">暂无跟进计划（表可能不存在）</div>';
        }
    }

    /**
     * 获取收款记录
     */
    private function getPaymentRecords($customerId)
    {
        global $db;

        try {
            $sql = "SELECT * FROM " . PREFIX . "custxiao WHERE custid = {$customerId} AND ispay = '1' ORDER BY id DESC LIMIT 10";
            $payments = $db->getall($sql);

            if (empty($payments)) {
                return '<div class="no-data">暂无收款记录</div>';
            }

            $html = '<div class="payment-records">';
            foreach ($payments as $payment) {
                $html .= '<div class="payment-item">';
                $html .= '<div class="payment-title">' . htmlspecialchars($payment['name'] ?? '收款记录') . '</div>';
                $html .= '<div class="payment-amount">收款金额：¥' . number_format($payment['money'] ?? 0, 2) . '</div>';
                $html .= '<div class="payment-date">收款日期：' . htmlspecialchars($payment['optdt'] ?? '') . '</div>';
                $html .= '</div>';
            }
            $html .= '</div>';

            return $html;
        } catch (Exception $e) {
            return '<div class="no-data">暂无收款记录（表可能不存在）</div>';
        }
    }

    /**
     * 获取客户动态时间线
     */
    private function getCustomerTimeline($customerId)
    {
        global $db;

        try {
            // 收集各种业务活动记录
            $timeline = [];

            // 合同记录
            $contracts = $db->getall("SELECT 'contract' as type, name as title, optdt as time, '签订合同' as action FROM " . PREFIX . "custract WHERE custid = {$customerId} AND status != '已删除' ORDER BY optdt DESC LIMIT 5");
            if ($contracts) {
                foreach ($contracts as $item) {
                    $timeline[] = $item;
                }
            }

            // 收款记录
            $payments = $db->getall("SELECT 'payment' as type, name as title, optdt as time, '收款' as action FROM " . PREFIX . "custxiao WHERE custid = {$customerId} AND ispay = '1' ORDER BY optdt DESC LIMIT 5");
            if ($payments) {
                foreach ($payments as $item) {
                    $timeline[] = $item;
                }
            }

            // 跟进计划
            $plans = $db->getall("SELECT 'plan' as type, title, optdt as time, '跟进计划' as action FROM " . PREFIX . "custplan WHERE custid = {$customerId} ORDER BY optdt DESC LIMIT 5");
            if ($plans) {
                foreach ($plans as $item) {
                    $timeline[] = $item;
                }
            }

            // 按时间排序
            usort($timeline, function($a, $b) {
                return strtotime($b['time']) - strtotime($a['time']);
            });

            // 限制显示数量
            $timeline = array_slice($timeline, 0, 10);

            if (empty($timeline)) {
                return '<div class="no-data">暂无客户动态</div>';
            }

            $html = '<div class="customer-timeline">';
            foreach ($timeline as $item) {
                $html .= '<div class="timeline-item">';
                $html .= '<div class="timeline-date">' . htmlspecialchars($item['time'] ?? '') . '</div>';
                $html .= '<div class="timeline-action">' . htmlspecialchars($item['action'] ?? '') . '</div>';
                $html .= '<div class="timeline-content">' . htmlspecialchars($item['title'] ?? '') . '</div>';
                $html .= '</div>';
            }
            $html .= '</div>';

            return $html;
        } catch (Exception $e) {
            return '<div class="no-data">暂无客户动态（数据加载失败）</div>';
        }
    }

    /**
     * 获取客户销售统计
     * @param int $customerId 客户ID
     * @return string
     */
    private function getCustomerSalesStats($customerId)
    {
        try {
            global $db;

            if (!$db) {
                return '<div class="error">数据库连接失败</div>';
            }

            // 获取合同统计
            $contracts = $db->getall("SELECT * FROM " . PREFIX . "custract WHERE custid = {$customerId} AND status != '已删除'");

            $totalContracts = count($contracts);
            $totalAmount = 0;
            $completedAmount = 0;

            foreach ($contracts as $contract) {
                $money = floatval($contract['money'] ?? 0);
                $totalAmount += $money;
                if (($contract['status'] ?? '') == '已完成') {
                    $completedAmount += $money;
                }
            }

            // 获取收款统计
            $payments = $db->getall("SELECT * FROM " . PREFIX . "custxiao WHERE custid = {$customerId} AND ispay = '1'");

            $paidAmount = 0;
            foreach ($payments as $payment) {
                $paidAmount += floatval($payment['money'] ?? 0);
            }

            // 计算统计数据
            $unpaidAmount = $totalAmount - $paidAmount;
            $paymentProgress = $totalAmount > 0 ? round(($paidAmount / $totalAmount) * 100, 1) : 0;

            // 生成HTML内容
            $html = '
                <div class="stats-section">
                    <div class="section-header">
                        <h4><i class="icon-bar-chart"></i> 销售统计概览</h4>
                    </div>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-value">' . $totalContracts . '</div>
                            <div class="stat-label">合同总数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">¥' . number_format($totalAmount, 2) . '</div>
                            <div class="stat-label">合同总额</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">¥' . number_format($paidAmount, 2) . '</div>
                            <div class="stat-label">已收金额</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">¥' . number_format($unpaidAmount, 2) . '</div>
                            <div class="stat-label">待收金额</div>
                        </div>
                    </div>
                    <div class="progress-section">
                        <div class="progress-item">
                            <div class="progress-label">回款进度</div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ' . $paymentProgress . '%"></div>
                            </div>
                            <div class="progress-text">' . $paymentProgress . '%</div>
                        </div>
                    </div>
                </div>
            ';

            return $html;

        } catch (Exception $e) {
            return '<div class="error">统计数据加载失败：' . $e->getMessage() . '</div>';
        }
    }

    /**
     * 检查标签页权限
     * @param array $tab 标签页配置
     * @return bool
     */
    private function checkTabPermission($tab)
    {
        // 如果没有设置权限，默认允许访问
        if (empty($tab['permissions'])) {
            return true;
        }
        
        // 这里可以根据实际需求实现权限检查逻辑
        // 例如检查用户角色、部门等
        $permissions = explode(',', $tab['permissions']);
        
        // 简化处理：检查用户是否有相应权限
        foreach ($permissions as $permission) {
            $permission = trim($permission);
            if ($this->checkUserPermission($permission)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 检查用户权限
     * @param string $permission 权限标识
     * @return bool
     */
    private function checkUserPermission($permission)
    {
        // 这里应该调用系统的权限检查方法
        // 简化处理，返回true
        return true;
    }






}
