/**
 * 移动端动态标签页组件
 * 创建时间：2025-01-03
 * 功能：为移动端展示页面提供动态标签页功能
 */

var MobileTabs = {
    // 配置选项
    options: {
        container: '.mobile-tabs-container',
        categoryCode: '',
        relationData: {},
        defaultTab: 0,
        loadMethod: 'immediate', // immediate, lazy
        onTabChange: null,
        onTabLoad: null
    },
    
    // 当前状态
    currentTab: 0,
    tabs: [],
    loaded: {},
    
    /**
     * 初始化标签页
     * @param {Object} opts 配置选项
     */
    init: function(opts) {
        this.options = $.extend({}, this.options, opts);
        this.loadTabs();
    },
    
    /**
     * 加载标签页配置
     */
    loadTabs: function() {
        var self = this;
        var params = {
            category_code: this.options.categoryCode
        };
        
        // 添加关联数据
        if (this.options.relationData) {
            params = $.extend(params, this.options.relationData);
        }
        
        $.ajax({
            url: js.getajaxurl('getMobileTabs', 'we', 'component'),
            type: 'GET',
            data: params,
            success: function(response) {
                try {
                    var result = typeof response === 'string' ? JSON.parse(response) : response;
                    if (result.success) {
                        self.tabs = result.data;
                        self.renderTabs();
                        self.bindEvents();
                        
                        // 激活默认标签页
                        var defaultIndex = self.findDefaultTab();
                        self.switchTab(defaultIndex);
                    } else {
                        console.error('加载标签页失败：', result.message);
                    }
                } catch (e) {
                    console.error('解析标签页数据失败：', e);
                }
            },
            error: function(xhr, status, error) {
                console.error('加载标签页请求失败：', error);
            }
        });
    },
    
    /**
     * 渲染标签页结构
     */
    renderTabs: function() {
        var container = $(this.options.container);
        if (container.length === 0) {
            console.error('标签页容器不存在：', this.options.container);
            return;
        }
        
        // 清空容器
        container.empty();
        
        if (this.tabs.length === 0) {
            container.html('<div class="no-tabs">暂无标签页</div>');
            return;
        }
        
        // 创建标签页导航
        var tabNav = $('<div class="r-tabs" tabid="mobile-dynamic">');
        $.each(this.tabs, function(index, tab) {
            var tabItem = $('<div class="r-tabs-item" index="' + index + '">');
            
            // 添加图标
            if (tab.tab_icon) {
                tabItem.append('<i class="' + tab.tab_icon + '"></i> ');
            }
            
            tabItem.append(tab.tab_name);
            tabNav.append(tabItem);
        });
        
        container.append(tabNav);
        
        // 创建标签页内容区域
        $.each(this.tabs, function(index, tab) {
            var tabContent = $('<div class="tab-content" tabitem="' + index + '" tabid="mobile-dynamic" style="display:none;">');
            tabContent.attr('data-tab-id', tab.id);
            tabContent.attr('data-load-method', tab.load_method);
            
            // 根据加载方式设置初始内容
            if (tab.load_method === 'immediate') {
                tabContent.html('<div class="loading">加载中...</div>');
            } else {
                tabContent.html('<div class="lazy-load">点击加载内容</div>');
            }
            
            container.append(tabContent);
        });
    },
    
    /**
     * 绑定事件
     */
    bindEvents: function() {
        var self = this;
        var container = $(this.options.container);
        
        // 标签页点击事件
        container.on('click', '.r-tabs-item', function() {
            var index = parseInt($(this).attr('index'));
            self.switchTab(index);
        });
        
        // 懒加载点击事件
        container.on('click', '.lazy-load', function() {
            var tabContent = $(this).closest('.tab-content');
            var index = parseInt(tabContent.attr('tabitem'));
            self.loadTabContent(index);
        });
    },
    
    /**
     * 切换标签页
     * @param {number} index 标签页索引
     */
    switchTab: function(index) {
        if (index < 0 || index >= this.tabs.length) {
            return;
        }
        
        var container = $(this.options.container);
        
        // 更新导航状态
        container.find('.r-tabs-item').removeClass('active');
        container.find('.r-tabs-item[index="' + index + '"]').addClass('active');
        
        // 更新内容显示
        container.find('.tab-content').hide();
        var currentContent = container.find('.tab-content[tabitem="' + index + '"]');
        currentContent.show();
        
        // 记录当前标签页
        this.currentTab = index;
        
        // 加载内容（如果需要）
        var tab = this.tabs[index];
        if (tab.load_method === 'immediate' && !this.loaded[index]) {
            this.loadTabContent(index);
        }
        
        // 触发回调
        if (typeof this.options.onTabChange === 'function') {
            this.options.onTabChange(index, tab);
        }
        
        // 记录访问统计
        this.recordTabAccess(tab.id);
    },
    
    /**
     * 加载标签页内容
     * @param {number} index 标签页索引
     */
    loadTabContent: function(index) {
        if (this.loaded[index]) {
            return;
        }
        
        var self = this;
        var tab = this.tabs[index];
        var container = $(this.options.container);
        var tabContent = container.find('.tab-content[tabitem="' + index + '"]');
        
        // 显示加载状态
        tabContent.html('<div class="loading"><i class="fa fa-spinner fa-spin"></i> 加载中...</div>');
        
        // 根据内容类型加载
        switch (tab.content_type) {
            case 'html':
                this.loadHtmlContent(index, tab, tabContent);
                break;
            case 'ajax':
                this.loadAjaxContent(index, tab, tabContent);
                break;
            case 'iframe':
                this.loadIframeContent(index, tab, tabContent);
                break;
            default:
                tabContent.html('<div class="error">不支持的内容类型</div>');
        }
    },
    
    /**
     * 加载HTML内容
     */
    loadHtmlContent: function(index, tab, tabContent) {
        var content = this.processTemplate(tab.content_source);
        tabContent.html(content);
        this.loaded[index] = true;
        this.onTabLoaded(index, tab);
    },
    
    /**
     * 加载AJAX内容
     */
    loadAjaxContent: function(index, tab, tabContent) {
        var self = this;
        var parts = tab.content_source.split(',');
        
        if (parts.length < 3) {
            tabContent.html('<div class="error">AJAX配置错误</div>');
            return;
        }
        
        var params = $.extend({}, this.options.relationData);
        
        $.ajax({
            url: js.getajaxurl(parts[2], parts[1], parts[0]),
            type: 'GET',
            data: params,
            success: function(response) {
                tabContent.html(response);
                self.loaded[index] = true;
                self.onTabLoaded(index, tab);
            },
            error: function() {
                tabContent.html('<div class="error">加载失败，请重试</div>');
            }
        });
    },
    
    /**
     * 加载iframe内容
     */
    loadIframeContent: function(index, tab, tabContent) {
        var url = this.processTemplate(tab.content_source);
        var iframe = '<iframe src="' + url + '" width="100%" height="400" frameborder="0"></iframe>';
        tabContent.html(iframe);
        this.loaded[index] = true;
        this.onTabLoaded(index, tab);
    },
    
    /**
     * 处理模板变量
     * @param {string} template 模板字符串
     * @returns {string} 处理后的字符串
     */
    processTemplate: function(template) {
        var result = template;
        
        // 替换关联数据变量
        for (var key in this.options.relationData) {
            var regex = new RegExp('\\{' + key + '\\}', 'g');
            result = result.replace(regex, this.options.relationData[key]);
        }
        
        return result;
    },
    
    /**
     * 查找默认标签页
     * @returns {number} 默认标签页索引
     */
    findDefaultTab: function() {
        for (var i = 0; i < this.tabs.length; i++) {
            if (this.tabs[i].is_default == 1) {
                return i;
            }
        }
        return this.options.defaultTab;
    },
    
    /**
     * 标签页加载完成回调
     */
    onTabLoaded: function(index, tab) {
        if (typeof this.options.onTabLoad === 'function') {
            this.options.onTabLoad(index, tab);
        }
    },
    
    /**
     * 记录标签页访问统计
     * @param {number} tabId 标签页ID
     */
    recordTabAccess: function(tabId) {
        // 异步记录，不影响用户体验
        $.ajax({
            url: js.getajaxurl('recordTabAccess', 'we', 'component'),
            type: 'POST',
            data: {
                tab_id: tabId,
                timestamp: new Date().getTime()
            },
            success: function() {
                // 静默记录
            },
            error: function() {
                // 忽略错误
            }
        });
    },
    
    /**
     * 刷新当前标签页
     */
    refreshCurrentTab: function() {
        this.loaded[this.currentTab] = false;
        this.loadTabContent(this.currentTab);
    },
    
    /**
     * 获取当前标签页信息
     * @returns {Object} 当前标签页信息
     */
    getCurrentTab: function() {
        return this.tabs[this.currentTab];
    },
    
    /**
     * 销毁标签页组件
     */
    destroy: function() {
        var container = $(this.options.container);
        container.off('click', '.r-tabs-item');
        container.off('click', '.lazy-load');
        container.empty();
        
        this.tabs = [];
        this.loaded = {};
        this.currentTab = 0;
    }
};

// 全局快捷方法
window.initMobileTabs = function(options) {
    var tabs = Object.create(MobileTabs);
    tabs.init(options);
    return tabs;
};
