# 移动端标签页功能使用说明

## 功能概述

移动端标签页功能为展示页面提供了灵活的标签页管理能力，支持动态配置、权限控制、内容懒加载等特性，让移动端页面更加丰富和易用。

## 主要特性

### ✨ 核心功能
- **动态配置**：通过后台管理界面配置标签页，无需修改代码
- **多种内容类型**：支持HTML、AJAX、iframe三种内容类型
- **懒加载**：支持立即加载和懒加载两种模式
- **权限控制**：可设置标签页访问权限
- **使用统计**：记录标签页访问统计数据
- **响应式设计**：完美适配移动端设备

### 🎯 适用场景
- 客户详情页面的多维度信息展示
- 项目详情页面的分类信息管理
- 流程详情页面的相关数据展示
- 任何需要分类展示信息的移动端页面

## 安装部署

### 1. 数据库初始化

执行SQL脚本创建相关数据表：

```sql
-- 执行 mobile_tabs_design.sql 文件中的SQL语句
```

### 2. 文件部署

确保以下文件已正确部署：

```
webmain/
├── model/mobileTabModel.php              # 数据模型
├── system/mobiletab/                     # 后台管理
│   ├── mobileTabAction.php              # 控制器
│   ├── tpl_mobileTab.html               # 管理界面
│   └── mobileTab.js                     # 管理脚本
├── we/component/                        # 移动端组件
│   ├── componentAction.php              # API控制器
│   └── mobileTabs.js                    # 前端组件
├── css/rui.css                          # 样式文件（已更新）
└── flow/page/view_customer_mobile_tabs.html  # 示例模板
```

### 3. 菜单配置

在系统管理中添加菜单项：
- 菜单名称：移动端标签页管理
- 访问地址：index.php?d=system&m=mobiletab
- 权限设置：系统管理员

## 使用指南

### 1. 后台配置

#### 1.1 分类管理
1. 进入"移动端标签页管理"
2. 在"标签页分类管理"区域点击"新增分类"
3. 填写分类信息：
   - **分类名称**：显示名称，如"客户详情"
   - **分类代码**：程序识别码，如"customer"
   - **描述**：分类说明
   - **排序号**：显示顺序
   - **状态**：启用/禁用

#### 1.2 标签页配置
1. 在"标签页配置管理"区域点击"新增标签页"
2. 填写标签页信息：
   - **所属分类**：选择已创建的分类
   - **标签名称**：显示在标签上的文字
   - **标签代码**：程序识别码
   - **标签图标**：FontAwesome图标类名
   - **内容类型**：HTML/AJAX/iframe
   - **内容源**：根据类型填写相应内容
   - **加载方式**：立即加载/懒加载
   - **是否默认**：是否默认选中

#### 1.3 内容类型说明

**HTML类型**：
```html
<div class="custom-content">
    <h4>客户基本信息</h4>
    <p>客户名称：{name}</p>
    <p>联系电话：{tel}</p>
</div>
```

**AJAX类型**：
```
customer,record,getlist
格式：模块名,控制器,方法名
```

**iframe类型**：
```
https://example.com/page?id={customer_id}
```

### 2. 前端集成

#### 2.1 基本用法

在移动端页面中添加标签页容器：

```html
<!-- 标签页容器 -->
<div class="mobile-tabs-container" id="myTabs"></div>

<!-- 引入组件脚本 -->
<script src="webmain/we/component/mobileTabs.js"></script>

<script>
// 初始化标签页
var tabsInstance = initMobileTabs({
    container: '#myTabs',
    categoryCode: 'customer',
    relationData: {
        customer_id: 123,
        id: 123
    }
});
</script>
```

#### 2.2 配置选项

```javascript
var options = {
    container: '.mobile-tabs-container',  // 容器选择器
    categoryCode: 'customer',             // 分类代码
    relationData: {                       // 关联数据
        customer_id: 123,
        project_id: 456
    },
    defaultTab: 0,                        // 默认标签页索引
    loadMethod: 'immediate',              // 加载方式
    onTabChange: function(index, tab) {   // 标签页切换回调
        console.log('切换到：', tab.tab_name);
    },
    onTabLoad: function(index, tab) {     // 标签页加载完成回调
        console.log('加载完成：', tab.tab_name);
    }
};
```

#### 2.3 API方法

```javascript
// 刷新当前标签页
tabsInstance.refreshCurrentTab();

// 切换到指定标签页
tabsInstance.switchTab(2);

// 获取当前标签页信息
var currentTab = tabsInstance.getCurrentTab();

// 销毁标签页组件
tabsInstance.destroy();
```

### 3. 模板变量

在标签页内容中可以使用以下变量：

```html
{id}           <!-- 记录ID -->
{name}         <!-- 名称 -->
{customer_id}  <!-- 客户ID -->
{project_id}   <!-- 项目ID -->
<!-- 其他relationData中的变量 -->
```

## 最佳实践

### 1. 性能优化
- 对于数据量大的标签页使用懒加载
- 合理设置标签页数量，避免过多标签页影响用户体验
- 使用AJAX类型时注意接口响应速度

### 2. 用户体验
- 设置合理的默认标签页
- 为标签页添加有意义的图标
- 保持标签页名称简洁明了

### 3. 权限控制
- 根据用户角色设置标签页权限
- 敏感信息的标签页要严格控制访问权限

### 4. 维护管理
- 定期检查标签页使用统计，优化不常用的标签页
- 及时更新标签页内容，保持信息的时效性

## 故障排除

### 常见问题

**Q: 标签页不显示**
A: 检查分类代码是否正确，确认标签页状态为启用

**Q: 内容加载失败**
A: 检查内容源配置是否正确，AJAX类型需要确认接口可访问

**Q: 样式显示异常**
A: 确认CSS文件已正确加载，检查样式冲突

**Q: 权限问题**
A: 检查用户权限设置，确认标签页权限配置

### 调试方法

1. 打开浏览器开发者工具
2. 查看Console面板的错误信息
3. 检查Network面板的请求状态
4. 使用`console.log`输出调试信息

## 版本历史

### v1.0 (2025-01-03)
- 初始版本发布
- 支持基本的标签页功能
- 提供完整的管理后台
- 包含示例模板和文档

## 技术支持

如有问题或建议，请联系开发团队或查看系统日志获取详细错误信息。
